<?php

namespace Database\Factories;

use App\Models\Post;
use App\Models\User;
use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Post>
 */
class PostFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence();
        $createdAt = $this->faker->dateTimeBetween('-1 year', 'now');
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'content' => $this->faker->paragraphs(5, true),
            'excerpt' => $this->faker->text(200),
            'status' => $this->faker->randomElement(['draft', 'published', 'archived']),
            'author_id' => User::factory(),
            'image' => 'posts/' . $this->faker->randomElement(['sample1.jpg', 'sample2.jpg', 'sample3.jpg']),
            'seo_title' => $this->faker->words(6, true),
            'seo_description' => $this->faker->sentence(),
            'seo_keywords' => implode(', ', $this->faker->words(5)),
            'published_at' => $this->faker->randomElement(['published', 'draft']) === 'published' ? $createdAt : null,
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ];
    }
    
    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterCreating(function (Post $post) {
            try {
                DB::beginTransaction();
                
                // Lấy tất cả ID của danh mục
                $allCategoryIds = Category::pluck('id')->toArray();
                
                if (empty($allCategoryIds)) {
                    // Tạo một danh mục mới nếu không tìm thấy danh mục nào
                    $category = Category::factory()->create();
                    $allCategoryIds = [$category->id];
                }
                
                // Chọn ngẫu nhiên từ 1-3 danh mục không trùng lặp
                $categoryCount = min($this->faker->numberBetween(1, 3), count($allCategoryIds));
                $selectedCategoryIds = $this->faker->randomElements($allCategoryIds, $categoryCount);
                
                // Lấy danh sách category_id đã tồn tại cho post này
                $existingCategories = DB::table('post_category')
                    ->where('post_id', $post->id)
                    ->pluck('category_id')
                    ->toArray();
                
                // Lọc ra các category_id chưa tồn tại
                $newCategoryIds = array_diff($selectedCategoryIds, $existingCategories);
                
                if (!empty($newCategoryIds)) {
                    // Chuẩn bị dữ liệu để insert
                    $now = now();
                    $records = [];
                    
                    foreach ($newCategoryIds as $index => $categoryId) {
                        // Kiểm tra xem đã tồn tại cặp post_id và category_id này chưa
                        $exists = DB::table('post_category')
                            ->where('post_id', $post->id)
                            ->where('category_id', $categoryId)
                            ->exists();
                            
                        if (!$exists) {
                            $records[] = [
                                'post_id' => $post->id,
                                'category_id' => $categoryId,
                                'is_primary' => empty($existingCategories) && $index === 0, // Chỉ set primary nếu là category đầu tiên và post chưa có category nào
                                'created_at' => $now,
                                'updated_at' => $now,
                            ];
                        }
                    }
                    
                    if (!empty($records)) {
                        // Insert các bản ghi mới
                        DB::table('post_category')->insert($records);
                    }
                }
                
                // Đảm bảo luôn có một danh mục primary
                $hasPrimary = DB::table('post_category')
                    ->where('post_id', $post->id)
                    ->where('is_primary', true)
                    ->exists();
                    
                if (!$hasPrimary) {
                    // Lấy category đầu tiên làm primary
                    DB::table('post_category')
                        ->where('post_id', $post->id)
                        ->orderBy('id')
                        ->limit(1)
                        ->update(['is_primary' => true]);
                }
                
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        });
    }
    
    /**
     * Indicate that the post is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'published',
            'published_at' => $attributes['created_at'] ?? now(),
        ]);
    }
    
    /**
     * Indicate that the post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'published_at' => null,
        ]);
    }
}