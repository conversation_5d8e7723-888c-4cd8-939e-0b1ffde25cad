<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = ['vi', 'en'];

        foreach ($languages as $lang) {
            $this->createMainMenu($lang);
            $this->createFooterSupportMenu($lang);
            $this->createFooterPolicyMenu($lang);
        }
    }

    private function createMainMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Menu Chính (VI)',
                'items' => [
                    ['title' => 'Về chúng tôi', 'url' => '/ve-chung-toi'],
                    ['title' => 'Sản phẩm', 'url' => '/san-pham'],
                    ['title' => 'Giải pháp', 'url' => '/giai-phap'],
                    ['title' => 'Thương hiệu', 'url' => '/thuong-hieu'],
                    ['title' => 'Dự án', 'url' => '/du-an'],
                    ['title' => 'Tin tức', 'url' => '/tin-tuc'],
                    ['title' => 'Liên hệ', 'url' => '/lien-he'],
                ],
            ],
            'en' => [
                'name' => 'Main Menu (EN)',
                'items' => [
                    ['title' => 'About Us', 'url' => '/about-us'],
                    ['title' => 'Products', 'url' => '/products'],
                    ['title' => 'Solutions', 'url' => '/solutions'],
                    ['title' => 'Brands', 'url' => '/brands'],
                    ['title' => 'Projects', 'url' => '/projects'],
                    ['title' => 'News', 'url' => '/news'],
                    ['title' => 'Contact', 'url' => '/contact'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'main-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'main-menu',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'custom',
                    'status' => true
                ]
            );
        }
    }

    private function createFooterSupportMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Hỗ trợ khách hàng (VI)',
                'items' => [
                    ['title' => 'Hướng dẫn mua hàng', 'url' => '/huong-dan-mua-hang'],
                    ['title' => 'Hướng dẫn thanh toán', 'url' => '/huong-dan-thanh-toan'],
                    ['title' => 'Đăng ký đại lý', 'url' => '/dang-ky-dai-ly'],
                ],
            ],
            'en' => [
                'name' => 'Customer Support (EN)',
                'items' => [
                    ['title' => 'Shopping Guide', 'url' => '/shopping-guide'],
                    ['title' => 'Payment Guide', 'url' => '/payment-guide'],
                    ['title' => 'Agent Registration', 'url' => '/agent-registration'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'footer-support-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-support',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'page',
                    'status' => true
                ]
            );
        }
    }

    private function createFooterPolicyMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Chính sách (VI)',
                'items' => [
                    ['title' => 'Chính sách bảo hành', 'url' => '/chinh-sach-bao-hanh'],
                    ['title' => 'Chính sách bảo mật', 'url' => '/chinh-sach-bao-mat'],
                    ['title' => 'Chính sách mua hàng', 'url' => '/chinh-sach-mua-hang'],
                    ['title' => 'Chính sách vận chuyển', 'url' => '/chinh-sach-van-chuyen'],
                    ['title' => 'Chính sách đổi trả', 'url' => '/chinh-sach-doi-tra'],
                ],
            ],
            'en' => [
                'name' => 'Policies (EN)',
                'items' => [
                    ['title' => 'Warranty Policy', 'url' => '/warranty-policy'],
                    ['title' => 'Privacy Policy', 'url' => '/privacy-policy'],
                    ['title' => 'Shopping Policy', 'url' => '/shopping-policy'],
                    ['title' => 'Shipping Policy', 'url' => '/shipping-policy'],
                    ['title' => 'Return Policy', 'url' => '/return-policy'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'footer-policy-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-policy',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'page',
                    'status' => true
                ]
            );
        }
    }
} 