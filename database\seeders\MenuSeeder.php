<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Database\Seeder;

class MenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = ['vi', 'en'];

        foreach ($languages as $lang) {
            $this->createMainMenu($lang);
            $this->createFooterSupportMenu($lang);
            $this->createFooterPolicyMenu($lang);
        }
    }

    private function createMainMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Menu Chính (VI)',
                'items' => [
                    [
                        'title' => 'Về chúng tôi',
                        'url' => '/ve-chung-toi',
                        'children' => [
                            ['title' => 'Thiết bị bán lẻ', 'url' => '/ve-chung-toi/thiet-bi-ban-le'],
                            ['title' => 'Thiết bị dân dụng', 'url' => '/ve-chung-toi/thiet-bi-dan-dung'],
                            ['title' => 'Thiết bị ống sáng', 'url' => '/ve-chung-toi/thiet-bi-ong-sang'],
                            ['title' => 'Thiết bị điều khiển', 'url' => '/ve-chung-toi/thiet-bi-dieu-khien'],
                            ['title' => 'Phụ kiện', 'url' => '/ve-chung-toi/phu-kien'],
                            ['title' => 'Thiết bị thao dụng dụng', 'url' => '/ve-chung-toi/thiet-bi-thao-dung-dung'],
                        ]
                    ],
                    [
                        'title' => 'Sản phẩm',
                        'url' => '/san-pham',
                        'children' => [
                            ['title' => 'Loa sub', 'url' => '/san-pham/loa-sub'],
                            ['title' => 'Loa kiểm âm', 'url' => '/san-pham/loa-kiem-am'],
                            ['title' => 'Loa', 'url' => '/san-pham/loa'],
                        ]
                    ],
                    [
                        'title' => 'Giải pháp',
                        'url' => '/giai-phap',
                        'children' => [
                            ['title' => 'Hội nghị truyền hình', 'url' => '/giai-phap/hoi-nghi-truyen-hinh'],
                            ['title' => 'Phòng học thông minh', 'url' => '/giai-phap/phong-hoc-thong-minh'],
                            ['title' => 'Âm thanh hội thảo', 'url' => '/giai-phap/am-thanh-hoi-thao'],
                            ['title' => 'Âm thanh nhạc nền', 'url' => '/giai-phap/am-thanh-nhac-nen'],
                            ['title' => 'Âm thanh biểu diễn', 'url' => '/giai-phap/am-thanh-bieu-dien'],
                            ['title' => 'Âm thanh thông báo', 'url' => '/giai-phap/am-thanh-thong-bao'],
                        ]
                    ],
                    ['title' => 'Thương hiệu', 'url' => '/thuong-hieu'],
                    ['title' => 'Dự án', 'url' => '/du-an'],
                    ['title' => 'Tin tức', 'url' => '/tin-tuc'],
                    ['title' => 'Liên hệ', 'url' => '/lien-he'],
                ],
            ],
            'en' => [
                'name' => 'Main Menu (EN)',
                'items' => [
                    [
                        'title' => 'About Us',
                        'url' => '/about-us',
                        'children' => [
                            ['title' => 'Retail Equipment', 'url' => '/about-us/retail-equipment'],
                            ['title' => 'Consumer Equipment', 'url' => '/about-us/consumer-equipment'],
                            ['title' => 'Tube Lighting Equipment', 'url' => '/about-us/tube-lighting-equipment'],
                            ['title' => 'Control Equipment', 'url' => '/about-us/control-equipment'],
                            ['title' => 'Accessories', 'url' => '/about-us/accessories'],
                            ['title' => 'Operating Equipment', 'url' => '/about-us/operating-equipment'],
                        ]
                    ],
                    [
                        'title' => 'Products',
                        'url' => '/products',
                        'children' => [
                            ['title' => 'Subwoofers', 'url' => '/products/subwoofers'],
                            ['title' => 'Monitor Speakers', 'url' => '/products/monitor-speakers'],
                            ['title' => 'Speakers', 'url' => '/products/speakers'],
                        ]
                    ],
                    [
                        'title' => 'Solutions',
                        'url' => '/solutions',
                        'children' => [
                            ['title' => 'Video Conferencing', 'url' => '/solutions/video-conferencing'],
                            ['title' => 'Smart Classroom', 'url' => '/solutions/smart-classroom'],
                            ['title' => 'Conference Audio', 'url' => '/solutions/conference-audio'],
                            ['title' => 'Background Music', 'url' => '/solutions/background-music'],
                            ['title' => 'Performance Audio', 'url' => '/solutions/performance-audio'],
                            ['title' => 'Announcement Audio', 'url' => '/solutions/announcement-audio'],
                        ]
                    ],
                    ['title' => 'Brands', 'url' => '/brands'],
                    ['title' => 'Projects', 'url' => '/projects'],
                    ['title' => 'News', 'url' => '/news'],
                    ['title' => 'Contact', 'url' => '/contact'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'main-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'main-menu',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            $parentMenuItem = MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'custom',
                    'status' => true,
                    'parent_id' => null,
                ]
            );

            // Tạo sub menu items nếu có
            if (isset($item['children']) && is_array($item['children'])) {
                foreach ($item['children'] as $childIndex => $childItem) {
                    MenuItem::firstOrCreate(
                        ['menu_id' => $menu->id, 'title' => $childItem['title'], 'parent_id' => $parentMenuItem->id],
                        [
                            'url' => $childItem['url'],
                            'order' => $childIndex + 1,
                            'target' => '_self',
                            'type' => 'custom',
                            'status' => true,
                            'parent_id' => $parentMenuItem->id,
                        ]
                    );
                }
            }
        }
    }

    private function createFooterSupportMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Hỗ trợ khách hàng (VI)',
                'items' => [
                    ['title' => 'Hướng dẫn mua hàng', 'url' => '/huong-dan-mua-hang'],
                    ['title' => 'Hướng dẫn thanh toán', 'url' => '/huong-dan-thanh-toan'],
                    ['title' => 'Đăng ký đại lý', 'url' => '/dang-ky-dai-ly'],
                ],
            ],
            'en' => [
                'name' => 'Customer Support (EN)',
                'items' => [
                    ['title' => 'Shopping Guide', 'url' => '/shopping-guide'],
                    ['title' => 'Payment Guide', 'url' => '/payment-guide'],
                    ['title' => 'Agent Registration', 'url' => '/agent-registration'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'footer-support-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-support',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'page',
                    'status' => true
                ]
            );
        }
    }

    private function createFooterPolicyMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Chính sách (VI)',
                'items' => [
                    ['title' => 'Chính sách bảo hành', 'url' => '/chinh-sach-bao-hanh'],
                    ['title' => 'Chính sách bảo mật', 'url' => '/chinh-sach-bao-mat'],
                    ['title' => 'Chính sách mua hàng', 'url' => '/chinh-sach-mua-hang'],
                    ['title' => 'Chính sách vận chuyển', 'url' => '/chinh-sach-van-chuyen'],
                    ['title' => 'Chính sách đổi trả', 'url' => '/chinh-sach-doi-tra'],
                ],
            ],
            'en' => [
                'name' => 'Policies (EN)',
                'items' => [
                    ['title' => 'Warranty Policy', 'url' => '/warranty-policy'],
                    ['title' => 'Privacy Policy', 'url' => '/privacy-policy'],
                    ['title' => 'Shopping Policy', 'url' => '/shopping-policy'],
                    ['title' => 'Shipping Policy', 'url' => '/shipping-policy'],
                    ['title' => 'Return Policy', 'url' => '/return-policy'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => 'footer-policy-menu-' . $lang, 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-policy',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'page',
                    'status' => true
                ]
            );
        }
    }
} 