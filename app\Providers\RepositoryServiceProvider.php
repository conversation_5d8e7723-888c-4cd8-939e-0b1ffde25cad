<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\BannerRepository;
use App\Repositories\CategoryRepository;
use App\Repositories\ProductRepository;
use App\Models\Banner;
use App\Models\Category;
use App\Models\Product;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(BannerRepository::class, function ($app) {
            return new BannerRepository(new Banner());
        });

        $this->app->singleton(CategoryRepository::class, function ($app) {
            return new CategoryRepository(new Category());
        });

        $this->app->singleton(ProductRepository::class, function ($app) {
            return new ProductRepository(new Product());
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        //
    }
} 