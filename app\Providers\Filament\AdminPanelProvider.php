<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;

use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Resources\OrderResource;
use App\Filament\Pages\Auth\LoginPanel;
use App\Http\Middleware\LogLogoutMiddleware;
use App\Http\Middleware\LocaleMiddleware;

use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->maxContentWidth('full')
            ->id('admin')
            ->path('admin')
            ->login(LoginPanel::class)
            ->colors([
                'primary' => Color::Amber,
            ])
            // ->navigationGroups([
            //     NavigationGroup::make()
            //         ->label('Quản lý Users') 
            //         ->icon('heroicon-o-users') 
            //         ->items([ 
            //             NavigationItem::make('Users') 
            //                 ->icon('heroicon-o-users') 
            //                 ->isActiveWhen(fn (): bool => request()->routeIs('filament.admin.resources.users.index')) 
            //                 ->url(route('filament.admin.resources.users.index')),
            
            //             NavigationItem::make('Roles') 
            //                 ->icon('heroicon-o-shield-check') 
            //                 ->isActiveWhen(fn (): bool => request()->routeIs('filament.admin.resources.roles.index')) 
            //                 ->url(route('filament.admin.resources.roles.index')),
            
            //             NavigationItem::make('Permissions') 
            //                 ->icon('heroicon-o-key') 
            //                 ->isActiveWhen(fn (): bool => request()->routeIs('filament.admin.resources.permissions.index')) 
            //                 ->url(route('filament.admin.resources.permissions.index')),
            //         ])
            //         ->collapsible(), 
            // ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->resources([
                OrderResource::class,
            ])
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
                \App\Filament\Widgets\StatsOverview::class,
                \App\Filament\Widgets\ProductReviewsStatsWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                LogLogoutMiddleware::class,
                LocaleMiddleware::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->renderHook(
                PanelsRenderHook::GLOBAL_SEARCH_AFTER,
                fn (): string => Blade::render('@livewire("language-switcher")')
            )
            ->renderHook(
                'tables::toolbar.search.before',
                fn (): string => Blade::render('<div class="mr-4">@livewire("table-language-filter")</div>')
            )
            ->renderHook(
                PanelsRenderHook::STYLES_AFTER,
                fn (): string => Blade::render('<style>
                    /* Style for table language filter */
                    .fi-ta-search-field {
                        display: flex;
                        align-items: center;
                        gap: 1rem;
                    }

                    /* Ensure language filter appears before search */
                    [data-field-wrapper] + .fi-ta-search-field {
                        flex-direction: row-reverse;
                    }

                    /* Style the language filter container */
                    .table-language-filter {
                        margin-right: 1rem;
                        flex-shrink: 0;
                    }
                </style>')
            )
            ->renderHook(
                PanelsRenderHook::SCRIPTS_AFTER,
                fn (): string => Blade::render('<script>
                    document.addEventListener("DOMContentLoaded", function() {
                        // Function to sync language filters
                        function syncLanguageFilters() {
                            const currentLocale = "{{ session(\'locale\', config(\'app.locale\')) }}";

                            // Find all language filter selects and set their value
                            setTimeout(() => {
                                const langSelects = document.querySelectorAll("select[name*=\'tableFilters[lang]\'], select[name*=\'tableFilters.lang\']");
                                langSelects.forEach(select => {
                                    if (currentLocale && select.value !== currentLocale) {
                                        // Set the value
                                        select.value = currentLocale;

                                        // Trigger Livewire update
                                        if (select.hasAttribute("wire:model") || select.hasAttribute("wire:model.live")) {
                                            select.dispatchEvent(new Event("input", { bubbles: true }));
                                            select.dispatchEvent(new Event("change", { bubbles: true }));
                                        }
                                    }
                                });
                            }, 500);
                        }

                        // Sync on page load
                        syncLanguageFilters();

                        // Listen for Livewire navigation
                        document.addEventListener("livewire:navigated", function() {
                            syncLanguageFilters();
                        });

                        // Listen for Livewire updates
                        document.addEventListener("livewire:updated", function() {
                            syncLanguageFilters();
                        });
                    });
                </script>')
            );
    }
}
