<?php

namespace App\Repositories;

use App\Models\Banner;

class BannerRepository extends BaseRepository
{
    /**
     * @return string
     */
    public function getModel()
    {
        return Banner::class;
    }
    
    /**
     * Lấy banner theo vị trí
     *
     * @param string $position Vị trí banner (top, main, sidebar, footer)
     * @return mixed
     */
    public function getBanner($position)
    {
        return $this->model->where('position', $position)
            ->where('status', true)
            ->orderBy('order')
            ->first();
    }
    
    /**
     * L<PERSON>y danh sách banner theo vị trí
     *
     * @param string $position Vị trí banner (top, main, sidebar, footer)
     * @return mixed
     */
    public function getBanners($position)
    {
        return $this->model->where('position', $position)
            ->where('status', true)
            ->orderBy('order')
            ->get();
    }
    
    /**
     * Lấy tất cả banner
     *
     * @return mixed
     */
    public function getActiveBanners($type = 'banner', $position = null)
    {
        $query = $this->model->ofType($type)->active()->ordered();
        if ($position) {
            $query->ofPosition($position);
        }
        
        return $query->get();
    }
    
    /**
     * L<PERSON>y một banner cụ thể theo slug
     *
     * @param string $slug
     * @return mixed
     */
    public function getBannerBySlug($slug)
    {
        return $this->model->where('slug', $slug)
                      ->active()
                      ->first();
    }
    
    /**
     * Lấy banner theo type và position
     *
     * @param string $type
     * @param string $position
     * @return mixed
     */
    public function getBannersByTypeAndPosition($type, $position = null)
    {
        $query = $this->model->ofType($type)->active()->ordered();
        
        if ($position) {
            $query->ofPosition($position);
        }
        
        return $query->get();
    }
} 