<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use App\Services\LanguageFileService;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditLanguage extends EditRecord
{
    protected static string $resource = LanguageResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $service = new LanguageFileService();
        $record = $this->record;
        $locale = $record['locale'];
        $file = $record['file'];
        
        $languageFile = $service->findLanguageFile($record['id']);
        if ($languageFile) {
            $data['translations'] = $languageFile['translations'];
        }
        
        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $service = new LanguageFileService();
        $record = $this->record;
        $locale = $record['locale'];
        $file = $record['file'];
        $translations = $data['translations'] ?? [];
        
        try {
            $service->updateLanguageFile($locale, $file, $translations);
            
            Notification::make()
                ->success()
                ->title('Thành công')
                ->body('Đã cập nhật file ngôn ngữ.')
                ->send();
            
            return [
                'id' => $locale . '_' . $file,
                'locale' => $locale,
                'file' => $file,
                'keys_count' => count(array_filter($translations, fn($key) => !empty($key), ARRAY_FILTER_USE_KEY)),
                'last_modified' => time(),
                'translations' => $translations,
            ];
        } catch (\InvalidArgumentException $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body($e->getMessage())
                ->send();
            
            $this->halt();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        $record = $this->record;
        return "Sửa file: {$record['file']} ({$record['locale']})";
    }
} 