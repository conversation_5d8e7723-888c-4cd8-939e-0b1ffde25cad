<div class="review-block container mx-auto mb-12 lg:mb-[70px]">
    <div class="reviews-section bg-white rounded-xl shadow-new-shadow p-6">
        <!-- Review Header -->
        <div class="reviews-header mb-8">
            <h2 class="text-primary-base font-bold text-2xl md:text-[28px]/[1.4] mb-6">
                B<PERSON><PERSON> luận &amp; đánh gi<PERSON> sản phẩm
            </h2>

            <!-- Review Summary -->
            <div class="review-summary flex items-center justify-start flex-col md:flex-row gap-6 md:gap-12 lg:gap-16">
                <!-- Overall Rating -->
                <div class="overall-rating">
                    <div class="flex flex-col items-center gap-3">
                        <p class="text-secondary-main font-medium">
                            <span id="reviewsCount">{{ $reviewsCount }}</span>/{{ $reviewsCount }} đ<PERSON>h gi<PERSON>
                        </p>
                        <div class="flex flex-row items-center gap-3">
                            <div class="text-2xl font-bold text-primary-price">
                                {{ $averageRating }}
                            </div>
                            <div class="rating-stars flex gap-[2px]">
                                {!! getStarRating($averageRating) !!}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Review Filters and Sort -->
                <div class="review-controls flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div class="review-filters flex flex-col gap-3">
                        <div class="review-filters-stars flex items-center justify-start flex-wrap gap-2">
                            <button
                                class="filter-btn active bg-white text-secondary-main px-4 py-2 rounded-full ring-1 ring-inset text-sm font-medium whitespace-nowrap"
                                data-filter="all">
                                Tất cả
                            </button>
                            <button
                                class="filter-btn inline-flex items-center gap-1 bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="5">
                                5
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M11.7731 15.1133C11.4198 15.1133 10.9664 15 10.3998 14.6667L8.40642 13.4867C8.19975 13.3667 7.79975 13.3667 7.59975 13.4867L5.59975 14.6667C4.41975 15.3667 3.72642 15.0867 3.41309 14.86C3.10642 14.6333 2.62642 14.0533 2.93975 12.72L3.41309 10.6733C3.46642 10.46 3.35975 10.0933 3.19975 9.93334L1.54642 8.28001C0.719753 7.45334 0.78642 6.74667 0.899753 6.40001C1.01309 6.05334 1.37309 5.44 2.51975 5.24667L4.64642 4.89334C4.84642 4.86 5.13309 4.64667 5.21975 4.46667L6.39975 2.11334C6.93309 1.04 7.63309 0.880005 7.99975 0.880005C8.36642 0.880005 9.06642 1.04 9.59975 2.11334L10.7731 4.46C10.8664 4.64 11.1531 4.85334 11.3531 4.88667L13.4798 5.24001C14.6331 5.43334 14.9931 6.04667 15.0998 6.39334C15.2064 6.74001 15.2731 7.44667 14.4531 8.27334L12.7998 9.93334C12.6398 10.0933 12.5398 10.4533 12.5864 10.6733L13.0598 12.72C13.3664 14.0533 12.8931 14.6333 12.5864 14.86C12.4198 14.98 12.1531 15.1133 11.7731 15.1133ZM7.99975 12.3933C8.32642 12.3933 8.65309 12.4733 8.91309 12.6267L10.9064 13.8067C11.4864 14.1533 11.8531 14.1533 11.9931 14.0533C12.1331 13.9533 12.2331 13.6 12.0864 12.9467L11.6131 10.9C11.4864 10.3467 11.6931 9.63334 12.0931 9.22667L13.7464 7.57334C14.0731 7.24667 14.2198 6.92667 14.1531 6.70667C14.0798 6.48667 13.7731 6.30667 13.3198 6.23334L11.1931 5.88C10.6798 5.79334 10.1198 5.38 9.88642 4.91334L8.71309 2.56667C8.49975 2.14 8.23309 1.88667 7.99975 1.88667C7.76642 1.88667 7.49975 2.14 7.29309 2.56667L6.11309 4.91334C5.87975 5.38 5.31975 5.79334 4.80642 5.88L2.68642 6.23334C2.23309 6.30667 1.92642 6.48667 1.85309 6.70667C1.77975 6.92667 1.93309 7.25334 2.25975 7.57334L3.91309 9.22667C4.31309 9.62667 4.51975 10.3467 4.39309 10.9L3.91975 12.9467C3.76642 13.6067 3.87309 13.9533 4.01309 14.0533C4.15309 14.1533 4.51309 14.1467 5.09975 13.8067L7.09309 12.6267C7.34642 12.4733 7.67309 12.3933 7.99975 12.3933Z"
                                        fill="#535563"></path>
                                </svg>
                            </button>
                            <button
                                class="filter-btn inline-flex items-center gap-1 bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="4">
                                4
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M11.7731 15.1133C11.4198 15.1133 10.9664 15 10.3998 14.6667L8.40642 13.4867C8.19975 13.3667 7.79975 13.3667 7.59975 13.4867L5.59975 14.6667C4.41975 15.3667 3.72642 15.0867 3.41309 14.86C3.10642 14.6333 2.62642 14.0533 2.93975 12.72L3.41309 10.6733C3.46642 10.46 3.35975 10.0933 3.19975 9.93334L1.54642 8.28001C0.719753 7.45334 0.78642 6.74667 0.899753 6.40001C1.01309 6.05334 1.37309 5.44 2.51975 5.24667L4.64642 4.89334C4.84642 4.86 5.13309 4.64667 5.21975 4.46667L6.39975 2.11334C6.93309 1.04 7.63309 0.880005 7.99975 0.880005C8.36642 0.880005 9.06642 1.04 9.59975 2.11334L10.7731 4.46C10.8664 4.64 11.1531 4.85334 11.3531 4.88667L13.4798 5.24001C14.6331 5.43334 14.9931 6.04667 15.0998 6.39334C15.2064 6.74001 15.2731 7.44667 14.4531 8.27334L12.7998 9.93334C12.6398 10.0933 12.5398 10.4533 12.5864 10.6733L13.0598 12.72C13.3664 14.0533 12.8931 14.6333 12.5864 14.86C12.4198 14.98 12.1531 15.1133 11.7731 15.1133ZM7.99975 12.3933C8.32642 12.3933 8.65309 12.4733 8.91309 12.6267L10.9064 13.8067C11.4864 14.1533 11.8531 14.1533 11.9931 14.0533C12.1331 13.9533 12.2331 13.6 12.0864 12.9467L11.6131 10.9C11.4864 10.3467 11.6931 9.63334 12.0931 9.22667L13.7464 7.57334C14.0731 7.24667 14.2198 6.92667 14.1531 6.70667C14.0798 6.48667 13.7731 6.30667 13.3198 6.23334L11.1931 5.88C10.6798 5.79334 10.1198 5.38 9.88642 4.91334L8.71309 2.56667C8.49975 2.14 8.23309 1.88667 7.99975 1.88667C7.76642 1.88667 7.49975 2.14 7.29309 2.56667L6.11309 4.91334C5.87975 5.38 5.31975 5.79334 4.80642 5.88L2.68642 6.23334C2.23309 6.30667 1.92642 6.48667 1.85309 6.70667C1.77975 6.92667 1.93309 7.25334 2.25975 7.57334L3.91309 9.22667C4.31309 9.62667 4.51975 10.3467 4.39309 10.9L3.91975 12.9467C3.76642 13.6067 3.87309 13.9533 4.01309 14.0533C4.15309 14.1533 4.51309 14.1467 5.09975 13.8067L7.09309 12.6267C7.34642 12.4733 7.67309 12.3933 7.99975 12.3933Z"
                                        fill="#535563"></path>
                                </svg>
                            </button>
                            <button
                                class="filter-btn inline-flex items-center gap-1 bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="3">
                                3
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M11.7731 15.1133C11.4198 15.1133 10.9664 15 10.3998 14.6667L8.40642 13.4867C8.19975 13.3667 7.79975 13.3667 7.59975 13.4867L5.59975 14.6667C4.41975 15.3667 3.72642 15.0867 3.41309 14.86C3.10642 14.6333 2.62642 14.0533 2.93975 12.72L3.41309 10.6733C3.46642 10.46 3.35975 10.0933 3.19975 9.93334L1.54642 8.28001C0.719753 7.45334 0.78642 6.74667 0.899753 6.40001C1.01309 6.05334 1.37309 5.44 2.51975 5.24667L4.64642 4.89334C4.84642 4.86 5.13309 4.64667 5.21975 4.46667L6.39975 2.11334C6.93309 1.04 7.63309 0.880005 7.99975 0.880005C8.36642 0.880005 9.06642 1.04 9.59975 2.11334L10.7731 4.46C10.8664 4.64 11.1531 4.85334 11.3531 4.88667L13.4798 5.24001C14.6331 5.43334 14.9931 6.04667 15.0998 6.39334C15.2064 6.74001 15.2731 7.44667 14.4531 8.27334L12.7998 9.93334C12.6398 10.0933 12.5398 10.4533 12.5864 10.6733L13.0598 12.72C13.3664 14.0533 12.8931 14.6333 12.5864 14.86C12.4198 14.98 12.1531 15.1133 11.7731 15.1133ZM7.99975 12.3933C8.32642 12.3933 8.65309 12.4733 8.91309 12.6267L10.9064 13.8067C11.4864 14.1533 11.8531 14.1533 11.9931 14.0533C12.1331 13.9533 12.2331 13.6 12.0864 12.9467L11.6131 10.9C11.4864 10.3467 11.6931 9.63334 12.0931 9.22667L13.7464 7.57334C14.0731 7.24667 14.2198 6.92667 14.1531 6.70667C14.0798 6.48667 13.7731 6.30667 13.3198 6.23334L11.1931 5.88C10.6798 5.79334 10.1198 5.38 9.88642 4.91334L8.71309 2.56667C8.49975 2.14 8.23309 1.88667 7.99975 1.88667C7.76642 1.88667 7.49975 2.14 7.29309 2.56667L6.11309 4.91334C5.87975 5.38 5.31975 5.79334 4.80642 5.88L2.68642 6.23334C2.23309 6.30667 1.92642 6.48667 1.85309 6.70667C1.77975 6.92667 1.93309 7.25334 2.25975 7.57334L3.91309 9.22667C4.31309 9.62667 4.51975 10.3467 4.39309 10.9L3.91975 12.9467C3.76642 13.6067 3.87309 13.9533 4.01309 14.0533C4.15309 14.1533 4.51309 14.1467 5.09975 13.8067L7.09309 12.6267C7.34642 12.4733 7.67309 12.3933 7.99975 12.3933Z"
                                        fill="#535563"></path>
                                </svg>
                            </button>
                            <button
                                class="filter-btn inline-flex items-center gap-1 bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="2">
                                2
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M11.7731 15.1133C11.4198 15.1133 10.9664 15 10.3998 14.6667L8.40642 13.4867C8.19975 13.3667 7.79975 13.3667 7.59975 13.4867L5.59975 14.6667C4.41975 15.3667 3.72642 15.0867 3.41309 14.86C3.10642 14.6333 2.62642 14.0533 2.93975 12.72L3.41309 10.6733C3.46642 10.46 3.35975 10.0933 3.19975 9.93334L1.54642 8.28001C0.719753 7.45334 0.78642 6.74667 0.899753 6.40001C1.01309 6.05334 1.37309 5.44 2.51975 5.24667L4.64642 4.89334C4.84642 4.86 5.13309 4.64667 5.21975 4.46667L6.39975 2.11334C6.93309 1.04 7.63309 0.880005 7.99975 0.880005C8.36642 0.880005 9.06642 1.04 9.59975 2.11334L10.7731 4.46C10.8664 4.64 11.1531 4.85334 11.3531 4.88667L13.4798 5.24001C14.6331 5.43334 14.9931 6.04667 15.0998 6.39334C15.2064 6.74001 15.2731 7.44667 14.4531 8.27334L12.7998 9.93334C12.6398 10.0933 12.5398 10.4533 12.5864 10.6733L13.0598 12.72C13.3664 14.0533 12.8931 14.6333 12.5864 14.86C12.4198 14.98 12.1531 15.1133 11.7731 15.1133ZM7.99975 12.3933C8.32642 12.3933 8.65309 12.4733 8.91309 12.6267L10.9064 13.8067C11.4864 14.1533 11.8531 14.1533 11.9931 14.0533C12.1331 13.9533 12.2331 13.6 12.0864 12.9467L11.6131 10.9C11.4864 10.3467 11.6931 9.63334 12.0931 9.22667L13.7464 7.57334C14.0731 7.24667 14.2198 6.92667 14.1531 6.70667C14.0798 6.48667 13.7731 6.30667 13.3198 6.23334L11.1931 5.88C10.6798 5.79334 10.1198 5.38 9.88642 4.91334L8.71309 2.56667C8.49975 2.14 8.23309 1.88667 7.99975 1.88667C7.76642 1.88667 7.49975 2.14 7.29309 2.56667L6.11309 4.91334C5.87975 5.38 5.31975 5.79334 4.80642 5.88L2.68642 6.23334C2.23309 6.30667 1.92642 6.48667 1.85309 6.70667C1.77975 6.92667 1.93309 7.25334 2.25975 7.57334L3.91309 9.22667C4.31309 9.62667 4.51975 10.3467 4.39309 10.9L3.91975 12.9467C3.76642 13.6067 3.87309 13.9533 4.01309 14.0533C4.15309 14.1533 4.51309 14.1467 5.09975 13.8067L7.09309 12.6267C7.34642 12.4733 7.67309 12.3933 7.99975 12.3933Z"
                                        fill="#535563"></path>
                                </svg>
                            </button>
                            <button
                                class="filter-btn inline-flex items-center gap-1 bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="1">
                                1
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M11.7731 15.1133C11.4198 15.1133 10.9664 15 10.3998 14.6667L8.40642 13.4867C8.19975 13.3667 7.79975 13.3667 7.59975 13.4867L5.59975 14.6667C4.41975 15.3667 3.72642 15.0867 3.41309 14.86C3.10642 14.6333 2.62642 14.0533 2.93975 12.72L3.41309 10.6733C3.46642 10.46 3.35975 10.0933 3.19975 9.93334L1.54642 8.28001C0.719753 7.45334 0.78642 6.74667 0.899753 6.40001C1.01309 6.05334 1.37309 5.44 2.51975 5.24667L4.64642 4.89334C4.84642 4.86 5.13309 4.64667 5.21975 4.46667L6.39975 2.11334C6.93309 1.04 7.63309 0.880005 7.99975 0.880005C8.36642 0.880005 9.06642 1.04 9.59975 2.11334L10.7731 4.46C10.8664 4.64 11.1531 4.85334 11.3531 4.88667L13.4798 5.24001C14.6331 5.43334 14.9931 6.04667 15.0998 6.39334C15.2064 6.74001 15.2731 7.44667 14.4531 8.27334L12.7998 9.93334C12.6398 10.0933 12.5398 10.4533 12.5864 10.6733L13.0598 12.72C13.3664 14.0533 12.8931 14.6333 12.5864 14.86C12.4198 14.98 12.1531 15.1133 11.7731 15.1133ZM7.99975 12.3933C8.32642 12.3933 8.65309 12.4733 8.91309 12.6267L10.9064 13.8067C11.4864 14.1533 11.8531 14.1533 11.9931 14.0533C12.1331 13.9533 12.2331 13.6 12.0864 12.9467L11.6131 10.9C11.4864 10.3467 11.6931 9.63334 12.0931 9.22667L13.7464 7.57334C14.0731 7.24667 14.2198 6.92667 14.1531 6.70667C14.0798 6.48667 13.7731 6.30667 13.3198 6.23334L11.1931 5.88C10.6798 5.79334 10.1198 5.38 9.88642 4.91334L8.71309 2.56667C8.49975 2.14 8.23309 1.88667 7.99975 1.88667C7.76642 1.88667 7.49975 2.14 7.29309 2.56667L6.11309 4.91334C5.87975 5.38 5.31975 5.79334 4.80642 5.88L2.68642 6.23334C2.23309 6.30667 1.92642 6.48667 1.85309 6.70667C1.77975 6.92667 1.93309 7.25334 2.25975 7.57334L3.91309 9.22667C4.31309 9.62667 4.51975 10.3467 4.39309 10.9L3.91975 12.9467C3.76642 13.6067 3.87309 13.9533 4.01309 14.0533C4.15309 14.1533 4.51309 14.1467 5.09975 13.8067L7.09309 12.6267C7.34642 12.4733 7.67309 12.3933 7.99975 12.3933Z"
                                        fill="#535563"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="review-filters-attrs flex items-center justify-start gap-2">
                            <button
                                class="filter-btn active bg-white text-secondary-main px-4 py-2 rounded-full ring-1 ring-inset text-sm font-medium"
                                data-filter="comments">
                                Có bình luận
                            </button>
                            <button
                                class="filter-btn bg-primary-grey text-primary-gray2 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-200"
                                data-filter="images">
                                Có hình ảnh/ video
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reviews List -->
        <div class="reviews-list space-y-4">
            @forelse($reviews as $review)
                <div class="review-item border-b pb-4 last:border-b-0" data-rating="{{ $review['rating'] }}">
                    <div class="flex gap-4">
                        <div class="review-content flex-1">
                            <div class="reviewer-info mb-2">
                                <h4 class="font-medium text-primary-base mb-1">{{ $review['reviewer_name'] }}</h4>
                                <div class="rating-stars flex gap-[2px] mb-6">{!! getStarRating($review['rating']) !!}</div>
                            </div>
                            <div class="review-text">
                                <p class="text-primary-gray2 mb-3">{{ $review['comment'] }}</p>
                                <div class="review-images flex gap-2 mb-2"></div>
                            </div>
                            <div class="review-actions flex items-center gap-4">
                                <button
                                    class="helpful-btn inline-flex flex-row flex-nowrap items-center gap-1 font-medium text-sm text-secondary-main hover:text-primary-base">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M8.864 0.0460842C7.908 -0.192916 7.02 0.530084 6.956 1.46608C6.884 2.51708 6.726 3.48208 6.528 4.05608C6.403 4.41608 6.049 5.06908 5.488 5.69508C4.931 6.31808 4.206 6.87308 3.357 7.10508C2.685 7.28808 2 7.87008 2 8.72008V12.7211C2 13.5661 2.682 14.1851 3.448 14.2661C4.518 14.3801 5.012 14.6811 5.516 14.9891L5.564 15.0191C5.836 15.1841 6.142 15.3671 6.534 15.5031C6.931 15.6391 7.395 15.7201 8 15.7201H11.5C12.437 15.7201 13.099 15.2431 13.434 14.6561C13.5959 14.3791 13.6834 14.0649 13.688 13.7441C13.688 13.5921 13.665 13.4321 13.611 13.2801C13.812 13.0171 13.991 12.7021 14.099 12.3791C14.209 12.0491 14.271 11.6171 14.103 11.2301C14.172 11.1001 14.223 10.9611 14.262 10.8271C14.339 10.5571 14.375 10.2591 14.375 9.97008C14.375 9.68208 14.339 9.38508 14.262 9.11408C14.227 8.9895 14.1808 8.86834 14.124 8.75208C14.2991 8.50296 14.4118 8.21539 14.4525 7.91362C14.4932 7.61185 14.4608 7.30471 14.358 7.01808C14.152 6.42608 13.676 5.91808 13.158 5.74608C12.311 5.46408 11.355 5.47008 10.642 5.53508C10.494 5.5484 10.3463 5.56507 10.199 5.58508C10.5459 4.09947 10.5246 2.5516 10.137 1.07608C10.0696 0.839673 9.9402 0.625569 9.76228 0.455914C9.58436 0.286258 9.36435 0.167211 9.125 0.111084L8.864 0.0460842ZM11.5 14.7211H8C7.49 14.7211 7.137 14.6521 6.86 14.5571C6.579 14.4601 6.354 14.3291 6.084 14.1641L6.044 14.1401C5.489 13.8011 4.846 13.4091 3.554 13.2721C3.221 13.2361 3 12.9821 3 12.7221V8.72008C3 8.46608 3.226 8.17708 3.62 8.07008C4.715 7.77008 5.597 7.07408 6.234 6.36208C6.869 5.65208 7.298 4.88708 7.472 4.38408C7.715 3.68408 7.879 2.61608 7.954 1.53408C7.979 1.17208 8.314 0.940084 8.621 1.01608L8.883 1.08208C9.043 1.12208 9.141 1.22508 9.171 1.33708C9.57939 2.89311 9.52903 4.53403 9.026 6.06208C8.99753 6.14707 8.99242 6.23815 9.01122 6.32579C9.03002 6.41342 9.07203 6.49439 9.13285 6.56023C9.19368 6.62606 9.27108 6.67433 9.35695 6.69999C9.44283 6.72565 9.53403 6.72776 9.621 6.70608L9.624 6.70508L9.638 6.70208L9.696 6.68808C10.038 6.61547 10.3839 6.56304 10.732 6.53108C11.395 6.47108 12.189 6.47708 12.842 6.69508C13.017 6.75308 13.292 6.99508 13.412 7.34508C13.519 7.65308 13.499 8.01508 13.146 8.36708L12.793 8.72008L13.146 9.07408C13.189 9.11708 13.251 9.21508 13.3 9.38908C13.348 9.55608 13.375 9.75908 13.375 9.97008C13.375 10.1821 13.348 10.3841 13.3 10.5521C13.25 10.7261 13.189 10.8241 13.146 10.8671L12.793 11.2201L13.146 11.5741C13.193 11.6211 13.255 11.7511 13.151 12.0621C13.0426 12.3637 12.8704 12.6383 12.646 12.8671L12.293 13.2201L12.646 13.5741C12.652 13.5791 12.687 13.6241 12.687 13.7441C12.6826 13.8908 12.6409 14.0339 12.566 14.1601C12.401 14.4481 12.063 14.7201 11.5 14.7201V14.7211Z"
                                            fill="#246DDA"></path>
                                    </svg>
                                    Hữu ích ({{ $review['helpful_count'] }})
                                </button>
                                <button
                                    class="reply-btn font-medium inline-flex gap-1 items-center flex-row text-sm text-secondary-main hover:text-primary-base">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M2.678 11.894C2.7818 11.9982 2.86133 12.124 2.91094 12.2625C2.96055 12.4009 2.97901 12.5486 2.965 12.695C2.89472 13.3726 2.76148 14.0421 2.567 14.695C3.962 14.372 4.814 13.998 5.201 13.802C5.4205 13.6908 5.67329 13.6645 5.911 13.728C6.59243 13.9097 7.29477 14.0011 8 14C11.996 14 15 11.193 15 8C15 4.808 11.996 2 8 2C4.004 2 1 4.808 1 8C1 9.468 1.617 10.83 2.678 11.894ZM2.185 15.799C1.94807 15.846 1.71037 15.889 1.472 15.928C1.272 15.96 1.12 15.752 1.199 15.566C1.28779 15.3566 1.36918 15.1441 1.443 14.929L1.446 14.919C1.694 14.199 1.896 13.371 1.97 12.6C0.743 11.37 0 9.76 0 8C0 4.134 3.582 1 8 1C12.418 1 16 4.134 16 8C16 11.866 12.418 15 8 15C7.20765 15.0011 6.41859 14.8982 5.653 14.694C5.133 14.957 4.014 15.436 2.185 15.799Z"
                                            fill="#246DDA"></path>
                                    </svg>
                                    Thảo luận
                                </button>
                                <span class="w-[1px] h-6 bg-primary-border"></span>
                                <span
                                    class="text-sm text-primary-grey2 font-medium">{{ \Carbon\Carbon::parse($review['created_at'])->diffForHumans() }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <p class="text-gray-500">Chưa có đánh giá nào</p>
            @endforelse
        </div>

        <!-- Pagination -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-8 gap-4">
            <!-- Items per page selector -->
            <div class="flex items-center gap-2">
                <select class="border border-primary-border rounded-full pl-5 pr-14 h-11 text-primary-gray2">
                    <option value="10" selected="">
                        Hiển thị 10/ trang
                    </option>
                    <option value="20">Hiển thị 20/ trang</option>
                    <option value="50">Hiển thị 50/ trang</option>
                </select>
            </div>

            <!-- Pagination Numbers -->
            @if(count($reviews) > 10)
                <div>
                    <nav class="inline-flex gap-4" aria-label="Pagination">
                        <a href="#" aria-current="page"
                            class="relative z-10 inline-flex items-center justify-center bg-secondary-main rounded-full w-11 h-11 text-white focus:z-20 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-seconbg-secondary-main">1</a>
                        <a href="#"
                            class="relative inline-flex items-center justify-center rounded-full w-11 h-11 text-primary-gray2 ring-1 ring-primary-gray2 ring-inset hover:bg-gray-50 focus:z-20 focus:outline-offset-0">2</a>
                        <a href="#"
                            class="relative hidden items-center justify-center rounded-full w-11 h-11 text-primary-gray2 ring-1 ring-primary-gray2 ring-inset hover:bg-gray-50 focus:z-20 focus:outline-offset-0 md:inline-flex">3</a>
                        <span class="relative inline-flex items-center justify-center w-11 h-11">...</span>
                        <a href="#"
                            class="relative inline-flex items-center justify-center rounded-full w-11 h-11 text-primary-gray2 ring-1 ring-primary-gray2 ring-inset hover:bg-gray-50 focus:z-20 focus:outline-offset-0">21</a>
                    </nav>
                </div>
            @endif
        </div>
        <!-- Go to review button -->
        <div class="mt-6">
            <button
                class="write-review-btn bg-gradient2 text-white px-6 h-11 rounded-full font-medium hover:bg-primary-dark transition-colors">
                Gửi đánh giá
            </button>
        </div>

        <!-- Review Form Modal (Initially Hidden) -->
        <div class="review-modal fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="modal-content bg-white rounded-lg max-w-2xl mx-auto mt-20 p-6 m-4 max-h-[80vh] overflow-y-auto">
                <div class="modal-header flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-900">
                        Viết đánh giá
                    </h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <form class="review-form" id="reviewForm">
                    <!-- Rating Selection -->
                    <div class="rating-input mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Đánh giá của bạn *
                        </label>
                        <div class="star-rating flex gap-1">
                            <button type="button" class="star-btn" data-rating="1">
                                <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z">
                                    </path>
                                </svg>
                            </button>
                            <button type="button" class="star-btn" data-rating="2">
                                <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z">
                                    </path>
                                </svg>
                            </button>
                            <button type="button" class="star-btn" data-rating="3">
                                <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z">
                                    </path>
                                </svg>
                            </button>
                            <button type="button" class="star-btn" data-rating="4">
                                <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z">
                                    </path>
                                </svg>
                            </button>
                            <button type="button" class="star-btn" data-rating="5">
                                <svg class="w-8 h-8 text-gray-300 hover:text-yellow-400" fill="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z">
                                    </path>
                                </svg>
                            </button>
                        </div>
                        <input type="hidden" name="rating" id="selectedRating" required="">
                    </div>

                    <!-- Review Text -->
                    <div class="review-text-input mb-6">
                        <label for="reviewText" class="block text-sm font-medium text-gray-700 mb-2">
                            Nội dung đánh giá *
                        </label>
                        <textarea id="reviewText" name="reviewText" rows="4"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-base focus:border-transparent"
                            placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm..." required=""></textarea>
                    </div>

                    <!-- Reviewer Name -->
                    <div class="reviewer-name-input mb-6">
                        <label for="reviewerName" class="block text-sm font-medium text-gray-700 mb-2">
                            Tên của bạn *
                        </label>
                        <input type="text" id="reviewerName" name="reviewerName"
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-base focus:border-transparent"
                            placeholder="Nhập tên của bạn" required="">
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions flex gap-3 justify-end">
                        <button type="button"
                            class="cancel-btn bg-primary-grey text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-200">
                            Hủy
                        </button>
                        <button type="submit"
                            class="submit-btn bg-primary-base text-white px-6 py-2 rounded-lg hover:bg-primary-dark">
                            Gửi đánh giá
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Review System JavaScript -->
<script>
    // Review system functionality
    function initializeReviewSystem() {
        // Modal controls
        const writeReviewBtn = document.querySelector(".write-review-btn");
        const reviewModal = document.querySelector(".review-modal");
        const closeModalBtn = document.querySelector(".close-modal");
        const cancelBtn = document.querySelector(".cancel-btn");

        // Open modal
        if (writeReviewBtn) {
            writeReviewBtn.addEventListener("click", function () {
                reviewModal.classList.remove("hidden");
                document.body.style.overflow = "hidden";
            });
        }

        // Close modal function
        function closeModal() {
            reviewModal.classList.add("hidden");
            document.body.style.overflow = "auto";
            // Reset form
            document.getElementById("reviewForm").reset();
            document.getElementById("selectedRating").value = "";
            // Reset star display
            const stars = document.querySelectorAll(".star-btn svg");
            stars.forEach((star) => {
                star.classList.remove("text-yellow-400");
                star.classList.add("text-gray-300");
            });
        }

        // Close modal events
        if (closeModalBtn) {
            closeModalBtn.addEventListener("click", closeModal);
        }
        if (cancelBtn) {
            cancelBtn.addEventListener("click", closeModal);
        }

        // Close modal when clicking outside
        if (reviewModal) {
            reviewModal.addEventListener("click", function (e) {
                if (e.target === reviewModal) {
                    closeModal();
                }
            });
        }

        // Star rating functionality
        const starButtons = document.querySelectorAll(".star-btn");
        starButtons.forEach((button, index) => {
            button.addEventListener("click", function (e) {
                e.preventDefault();
                const rating = parseInt(this.dataset.rating);
                document.getElementById("selectedRating").value = rating;

                // Update star display
                starButtons.forEach((star, starIndex) => {
                    const svg = star.querySelector("svg");
                    if (starIndex < rating) {
                        svg.classList.remove("text-gray-300");
                        svg.classList.add("text-yellow-400");
                    } else {
                        svg.classList.remove("text-yellow-400");
                        svg.classList.add("text-gray-300");
                    }
                });
            });

            // Hover effect
            button.addEventListener("mouseenter", function () {
                const rating = parseInt(this.dataset.rating);
                starButtons.forEach((star, starIndex) => {
                    const svg = star.querySelector("svg");
                    if (starIndex < rating) {
                        svg.classList.add("text-yellow-400");
                    } else {
                        svg.classList.remove("text-yellow-400");
                    }
                });
            });
        });

        // Review filter functionality
        const filterButtons = document.querySelectorAll(".filter-btn");
        const reviewItems = document.querySelectorAll(".review-item");

        filterButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const filter = this.dataset.filter;

                // Update active button
                filterButtons.forEach((btn) => {
                    btn.classList.remove(
                        "active",
                        "bg-white",
                        "text-secondary-main",
                        "ring-1",
                        "ring-inset"
                    );
                    btn.classList.add(
                        "bg-primary-grey",
                        "text-primary-gray2"
                    );
                });
                this.classList.add(
                    "active",
                    "bg-white",
                    "text-secondary-main",
                    "ring-1",
                    "ring-inset"
                );
                this.classList.remove(
                    "bg-primary-grey",
                    "text-primary-gray2"
                );

                // Filter reviews
                reviewItems.forEach((item) => {
                    if (filter === "all") {
                        item.style.display = "block";
                    } else if (filter === "images") {
                        const hasImages =
                            item.querySelector(".review-images");
                        item.style.display = hasImages ? "block" : "none";
                    } else {
                        const rating = item.dataset.rating;
                        item.style.display =
                            rating === filter ? "block" : "none";
                    }
                });
            });
        });

        // Helpful button functionality
        const helpfulButtons = document.querySelectorAll(".helpful-btn");
        helpfulButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const countSpan = this.querySelector("span");
                const currentCount = parseInt(
                    countSpan.textContent.match(/\d+/)[0]
                );

                // Toggle helpful state
                if (this.classList.contains("voted")) {
                    this.classList.remove("voted", "text-primary-base");
                    this.classList.add("text-gray-500");
                    countSpan.textContent = `Hữu ích (${currentCount - 1})`;
                } else {
                    this.classList.add("voted", "text-primary-base");
                    this.classList.remove("text-gray-500");
                    countSpan.textContent = `Hữu ích (${currentCount + 1})`;
                }
            });
        });

        // Review form submission
        const reviewForm = document.getElementById("reviewForm");
        if (reviewForm) {
            reviewForm.addEventListener("submit", function (e) {
                e.preventDefault();

                const rating = document.getElementById("selectedRating").value;
                const reviewText = document.getElementById("reviewText").value;
                const reviewerName = document.getElementById("reviewerName").value;

                if (!rating) {
                    alert("Vui lòng chọn số sao đánh giá");
                    return;
                }

                if (!reviewText.trim()) {
                    alert("Vui lòng nhập nội dung đánh giá");
                    return;
                }

                if (!reviewerName.trim()) {
                    alert("Vui lòng nhập tên của bạn");
                    return;
                }

                // Tạo đối tượng dữ liệu để gửi
                const reviewData = {
                    rating: rating,
                    comment: reviewText,
                    reviewer_name: reviewerName
                };

                // Gửi dữ liệu đến server
                fetch('/products/{{ $product->id }}/reviews', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content') // Nếu có CSRF token
                    },
                    body: JSON.stringify(reviewData)
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Hiển thị thông báo thành công
                        if (data.success) {
                            alert(data.message);
                        } else {
                            alert(data.message);
                        }
                        // Đóng modal
                        closeModal();
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert("Đã xảy ra lỗi. Vui lòng thử lại sau.");
                    });
            });
        }

        // Sort functionality
        const sortSelect = document.querySelector(".review-sort");
        if (sortSelect) {
            sortSelect.addEventListener("change", function () {
                const sortBy = this.value;
                const reviewsList = document.querySelector(".reviews-list");
                const reviews = Array.from(
                    reviewsList.querySelectorAll(".review-item")
                );

                reviews.sort((a, b) => {
                    switch (sortBy) {
                        case "newest":
                            // Simulate sorting by date (newest first)
                            return Math.random() - 0.5;
                        case "oldest":
                            // Simulate sorting by date (oldest first)
                            return Math.random() - 0.5;
                        case "highest":
                            return (
                                parseInt(b.dataset.rating) -
                                parseInt(a.dataset.rating)
                            );
                        case "lowest":
                            return (
                                parseInt(a.dataset.rating) -
                                parseInt(b.dataset.rating)
                            );
                        case "helpful":
                            // Simulate sorting by helpfulness
                            return Math.random() - 0.5;
                        default:
                            return 0;
                    }
                });

                // Re-append sorted reviews
                reviews.forEach((review) =>
                    reviewsList.appendChild(review)
                );
            });
        }
    }

    // Initialize review system when DOM is loaded
    document.addEventListener("DOMContentLoaded", function () {
        initializeReviewSystem();
        initializeTabSystem();
        initializeProductGallery();
    });

    // Product Gallery functionality
    function initializeProductGallery() {
        // Initialize thumbnail swiper first
        const productThumbsSwiper = new Swiper(".product-thumbs-swiper", {
            slidesPerView: 3,
            spaceBetween: 8,
            watchSlidesProgress: true,
            navigation: {
                nextEl: ".product-gallery-thumbs .product-thumbs-next",
                prevEl: ".product-gallery-thumbs .product-thumbs-prev",
            },
            freeMode: false,
            breakpoints: {
                480: {
                    slidesPerView: 3,
                    spaceBetween: 10,
                },
                768: {
                    slidesPerView: 4,
                    spaceBetween: 12,
                },
                1024: {
                    slidesPerView: 5.5,
                    spaceBetween: 12,
                },
            },
        });

        // Initialize main gallery swiper
        const productGallerySwiper = new Swiper(".product-gallery-swiper", {
            loop: true,
            spaceBetween: 0,
            // navigation: {
            //     nextEl: ".product-gallery-next",
            //     prevEl: ".product-gallery-prev",
            // },
            thumbs: {
                swiper: productThumbsSwiper,
            },
            effect: "fade",
            fadeEffect: {
                crossFade: true,
            },
        });

        // Add active border to selected thumbnail
        productGallerySwiper.on("slideChange", function () {
            // Remove active class from all thumbs
            document
                .querySelectorAll(
                    ".product-thumbs-swiper .swiper-slide > div"
                )
                .forEach((thumb) => {
                    thumb.classList.remove("border-secondary-main");
                    thumb.classList.add("border-primary-border");
                });

            // Add active class to current thumb
            const activeThumb = document.querySelector(
                `.product-thumbs-swiper .swiper-slide:nth-child(${productGallerySwiper.realIndex + 1
                }) > div`
            );
            if (activeThumb) {
                activeThumb.classList.remove("border-primary-border");
                activeThumb.classList.add("border-secondary-main");
            }
        });

        // Set initial active thumbnail
        const firstThumb = document.querySelector(
            ".product-thumbs-swiper .swiper-slide:first-child > div"
        );
        if (firstThumb) {
            firstThumb.classList.remove("border-transparent");
            firstThumb.classList.add("border-secondary-main");
        }
    }

    // Tab system functionality
    function initializeTabSystem() {
        // Tab switching
        const tabButtons = document.querySelectorAll(".tab-btn");
        const tabPanels = document.querySelectorAll(".tab-panel");

        tabButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const targetTab = this.dataset.tab;

                // Update active button
                tabButtons.forEach((btn) => {
                    btn.classList.remove(
                        "active",
                        "bg-gradient2",
                        "text-white"
                    );
                    btn.classList.add(
                        "bg-primary-border",
                        "text-primary-gray2"
                    );
                });
                this.classList.add("active", "bg-gradient2", "text-white");
                this.classList.remove(
                    "bg-primary-border",
                    "text-primary-gray2"
                );

                // Switch panels
                tabPanels.forEach((panel) => {
                    panel.classList.add("hidden");
                    panel.classList.remove("active");
                });

                const targetPanel = document.querySelector(
                    `[data-panel="${targetTab}"]`
                );
                if (targetPanel) {
                    targetPanel.classList.remove("hidden");
                    targetPanel.classList.add("active");

                    // Check if "Xem thêm" button should be shown
                    checkShowMoreButton(targetPanel);
                }
            });
        });

        // Show more functionality
        function checkShowMoreButton(panel) {
            const contentWrapper = panel.querySelector(".content-wrapper");
            const contentText = panel.querySelector(".content-text");
            const showMoreBtn = panel.querySelector(".show-more-btn");
            const fadeOverlay = panel.querySelector(".fade-overlay");

            if (contentText && contentText.scrollHeight > 600) {
                showMoreBtn.classList.remove("hidden");
                fadeOverlay.classList.remove("hidden");
            } else {
                showMoreBtn.classList.add("hidden");
                fadeOverlay.classList.add("hidden");
            }
        }

        // Show more button functionality
        const showMoreButtons = document.querySelectorAll(".show-more-btn");
        showMoreButtons.forEach((button) => {
            button.addEventListener("click", function () {
                const panel = this.closest(".tab-panel");
                const contentWrapper =
                    panel.querySelector(".content-wrapper");
                const fadeOverlay = panel.querySelector(".fade-overlay");

                if (contentWrapper.style.maxHeight === "600px") {
                    // Expand
                    contentWrapper.style.maxHeight = "none";
                    fadeOverlay.classList.add("hidden");
                    this.textContent = "Thu gọn";
                } else {
                    // Collapse
                    contentWrapper.style.maxHeight = "600px";
                    fadeOverlay.classList.remove("hidden");
                    this.textContent = "Xem thêm";

                    // Scroll to top of content
                    contentWrapper.scrollTop = 0;
                }
            });
        });

        // Initial check for all panels
        tabPanels.forEach((panel) => {
            if (!panel.classList.contains("hidden")) {
                checkShowMoreButton(panel);
            }
        });
    }

    // Video Modal functionality
    function openVideoModal() {
        const modal = document.getElementById("videoModal");
        const iframe = document.getElementById("videoFrame");

        // Set YouTube URL with autoplay
        const videoId = "tgbNymZ7vqY"; // Extract video ID from URL
        const embedUrl = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`;

        iframe.src = embedUrl;
        modal.classList.remove("hidden");
        // Add class to show modal
        modal.classList.add("flex");

        // Prevent body scroll
        document.body.style.overflow = "hidden";

        // Add fade-in animation
        setTimeout(() => {
            modal.classList.add("video-modal-open");
        }, 10);
    }

    function closeVideoModal() {
        const modal = document.getElementById("videoModal");
        const iframe = document.getElementById("videoFrame");

        // Stop video by clearing src
        iframe.src = "";

        // Hide modal
        modal.classList.remove("video-modal-open");
        modal.classList.remove("flex");
        modal.classList.add("hidden");

        // Restore body scroll
        document.body.style.overflow = "auto";
    }

    // Close modal when clicking outside the video
    document.addEventListener("DOMContentLoaded", function () {
        const modal = document.getElementById("videoModal");

        modal.addEventListener("click", function (e) {
            if (e.target === modal) {
                closeVideoModal();
            }
        });

        // Close modal on Escape key
        document.addEventListener("keydown", function (e) {
            if (e.key === "Escape") {
                closeVideoModal();
            }
        });
    });

    // Quantity selector functions
    function increaseQuantity() {
        const quantityInput = document.getElementById("quantity");
        let currentValue = parseInt(quantityInput.value);
        if (currentValue < 1802) {
            // Max available stock
            quantityInput.value = currentValue + 1;
        }
    }

    function decreaseQuantity() {
        const quantityInput = document.getElementById("quantity");
        let currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            // Minimum quantity is 1
            quantityInput.value = currentValue - 1;
        }
    }
</script>