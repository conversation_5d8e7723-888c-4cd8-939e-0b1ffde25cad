<?php

namespace Database\Factories;

use App\Models\OrderDetail;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderDetailFactory extends Factory
{
    protected $model = OrderDetail::class;

    public function definition(): array
    {
        // Lấy một sản phẩm ngẫu nhiên
        $product = Product::inRandomOrder()->first();
        
        // Nếu không có sản phẩm, tạo một sản phẩm mới
        if (!$product) {
            $product = Product::factory()->create();
        }
        
        $quantity = $this->faker->numberBetween(1, 5);
        $price = $product->price;
        $subtotal = $price * $quantity;
        
        // Giả sử PV và BV là tỉ lệ của giá sản phẩm
        $pvRate = $this->faker->randomFloat(2, 0.05, 0.2);
        $bvRate = $this->faker->randomFloat(2, 0.03, 0.15);
        $pv = round($price * $pvRate * $quantity);
        $bv = round($price * $bvRate * $quantity);

        return [
            'order_id' => Order::factory(),
            'product_id' => $product->id,
            'product_name' => $product->name,
            'product_sku' => $product->sku,
            'quantity' => $quantity,
            'price' => $price,
            'subtotal' => $subtotal,
            'options' => json_encode([
                'color' => $this->faker->optional()->colorName(),
                'size' => $this->faker->optional()->randomElement(['S', 'M', 'L', 'XL']),
            ]),
            'pv' => $pv,
            'bv' => $bv,
        ];
    }
} 