<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Banner extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'type',
        'slug',
        'description',
        'image',
        'url',
        'position',
        'order',
        'status',
        'start_date',
        'end_date',
    ];
    
    protected $casts = [
        'status' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];
    
    /**
     * Tự động tạo slug từ name nếu không có
     */
    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($banner) {
            if (empty($banner->slug)) {
                $banner->slug = Str::slug($banner->name);
            }
        });
    }
    
    /**
     * Lọc banner theo loại
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
    
    /**
     * Lọc banner đang active
     */
    public function scopeActive($query)
    {
        return $query->where('status', true)
            ->where(function ($q) {
                $now = now();
                $q->whereNull('start_date')
                    ->orWhere('start_date', '<=', $now);
            })
            ->where(function ($q) {
                $now = now();
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', $now);
            });
    }
    
    /**
     * Lọc banner theo vị trí
     */
    public function scopeOfPosition($query, $position)
    {
        return $query->where('position', $position);
    }
    
    /**
     * Sắp xếp banner theo thứ tự
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order');
    }
    
    /**
     * Get the image url
     */
    public function getImageUrlAttribute()
    {
        if (empty($this->image)) {
            return null;
        }
        
        // Nếu image đã có URL đầy đủ
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }
        
        // Nếu là đường dẫn tương đối, thêm domain
        return asset('uploads/banners/' . $this->image);
    }
} 