<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MenuResource\Pages;
use App\Models\Menu;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Resources\MenuItemResource;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use App\Traits\HasLanguageSync;

class MenuResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = Menu::class;

    protected static ?string $navigationIcon = 'heroicon-o-bars-3';

    protected static ?string $navigationGroup = 'Quản lý nội dung';

    protected static ?int $navigationSort = 20;

    public static function getModelLabel(): string
    {
        return 'Nhóm Menu';
    }

    public static function getPluralModelLabel(): string
    {
        return 'Nhóm Menu';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên nhóm menu')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn (string $state, Forms\Set $set) => 
                        $set('slug', Str::slug($state))),

                Forms\Components\TextInput::make('slug')
                    ->label('Slug')
                    ->required()
                    ->maxLength(255)
                    ->unique(ignoreRecord: true),

                Forms\Components\TextInput::make('location')
                    ->label('Vị trí hiển thị')
                    ->helperText('Vị trí hiển thị trên trang (header, footer, v.v...)')
                    ->maxLength(255),

                Forms\Components\Toggle::make('status')
                    ->label('Hiển thị')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên nhóm menu')
                    ->searchable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable(),

                Tables\Columns\TextColumn::make('location')
                    ->label('Vị trí')
                    ->searchable(),
                    
                Tables\Columns\TextColumn::make('items_count')
                    ->label('Số lượng')
                    ->counts('items')
                    ->sortable(),

                Tables\Columns\IconColumn::make('status')
                    ->label('Hiển thị')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        '1' => 'Hiển thị',
                        '0' => 'Ẩn',
                    ]),
            ])
            ->actions([
                Tables\Actions\Action::make('manage_items')
                    ->label('Quản lý menu')
                    ->icon('heroicon-o-list-bullet')
                    ->url(fn (Menu $record) => MenuItemResource::getUrl('index', ['menu' => $record->id])),
                Tables\Actions\EditAction::make()
                    ->modalHeading('Chỉnh sửa nhóm menu')
                    ->modalWidth('md'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('copy_to_language')
                        ->label('Copy sang ngôn ngữ khác...')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                // Prevent copying if it already exists
                                $exists = Menu::where('slug', $record->slug . '-' . $data['target_lang'])
                                                ->where('lang', $data['target_lang'])
                                                ->exists();

                                if ($exists) {
                                    Notification::make()
                                        ->warning()
                                        ->title('Menu đã tồn tại')
                                        ->body('Menu "' . $record->name . '" đã tồn tại ở ngôn ngữ đích.')
                                        ->send();
                                    continue;
                                }

                                $newMenu = $record->replicate();
                                $newMenu->lang = $data['target_lang'];
                                $newMenu->slug = $record->slug . '-' . $data['target_lang'];
                                $newMenu->name = $record->name . ' (' . strtoupper($data['target_lang']) . ')';
                                $newMenu->save();

                                // Replicate menu items
                                foreach ($record->items as $item) {
                                    $newItem = $item->replicate();
                                    $newItem->menu_id = $newMenu->id;
                                    $newItem->save();
                                }
                            }

                            Notification::make()
                                ->title('Đã copy menu thành công')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Chọn ngôn ngữ đích')
                                ->options([
                                    'vi' => 'Tiếng Việt',
                                    'en' => 'Tiếng Anh',
                                ])
                                ->required(),
                        ]),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->modalHeading('Tạo nhóm menu mới')
                    ->modalWidth('md'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // No relation managers
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMenus::route('/'),
            'create' => Pages\CreateMenu::route('/create'),
            'edit' => Pages\EditMenu::route('/{record}/edit'),
        ];
    }
}
