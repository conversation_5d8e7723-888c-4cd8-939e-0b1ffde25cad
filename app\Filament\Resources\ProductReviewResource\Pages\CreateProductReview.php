<?php

namespace App\Filament\Resources\ProductReviewResource\Pages;

use App\Filament\Resources\ProductReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateProductReview extends CreateRecord
{
    protected static string $resource = ProductReviewResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Nếu admin tạo bình luận và có phản hồi, tự động set admin_id và thời gian
        if (!empty($data['admin_reply'])) {
            $data['admin_id'] = auth()->id();
            $data['admin_reply_at'] = now();
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Cập nhật thống kê đánh giá cho sản phẩm
        $this->record->product->updateRatingStats();
    }
}
