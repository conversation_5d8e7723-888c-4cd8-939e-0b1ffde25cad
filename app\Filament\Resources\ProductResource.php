<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Repeater;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Collection;
use App\Traits\HasLanguageSync;

class ProductResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = Product::class;
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?string $navigationGroup = 'Quản lý sản phẩm';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = 'Sản phẩm';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Thông tin sản phẩm')
                            ->schema([
                                TextInput::make('name')
                                    ->label('Tên sản phẩm')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                        if ($operation === 'create') {
                                            $set('slug', Str::slug($state));
                                        }
                                    }),

                                TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->rules(['alpha_dash'])
                                    ->live(debounce: 500)
                                    ->afterStateUpdated(function ($state, Forms\Set $set, $record) {
                                        if (empty($state)) {
                                            return;
                                        }
                                        
                                        // Tạo slug hợp lệ từ text input
                                        $slug = Str::slug($state);
                                        
                                        // Kiểm tra tính duy nhất của slug
                                        $originalSlug = $slug;
                                        $count = 1;
                                        
                                        // Kiểm tra trùng lặp và thêm số nếu cần thiết
                                        while (Product::where('slug', $slug)
                                            ->when($record, fn($query) => $query->where('id', '!=', $record->id))
                                            ->exists()) {
                                            $slug = $originalSlug . '-' . $count++;
                                        }
                                        
                                        $set('slug', $slug);
                                    })
                                    ->helperText('Slug sẽ được sử dụng trong URL. Hệ thống sẽ tự động điều chỉnh nếu slug đã tồn tại.'),

                                TextInput::make('sku')
                                    ->label('Mã sản phẩm (SKU)')
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->helperText('Mã sản phẩm dùng để quản lý kho, mã này là duy nhất.')
                                    ->placeholder('VD: SP001'),

                                RichEditor::make('description')
                                    ->label('Mô tả sản phẩm')
                                    ->columnSpanFull(),

                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('price')
                                            ->label('Giá')
                                            ->numeric()
                                            ->prefix('đ')
                                            ->required(),

                                        TextInput::make('sale_price')
                                            ->label('Giá khuyến mãi')
                                            ->helperText('Giá đã giảm, hiển thị nổi bật')
                                            ->numeric()
                                            ->prefix('đ'),
                                    ]),

                                Select::make('category_id')
                                    ->label('Danh mục')
                                    ->options(
                                        Category::where('type', 'product')
                                            ->orderBy('name')
                                            ->pluck('name', 'id')
                                    )
                                    ->required()
                                    ->searchable(),

                                Select::make('brand_id')
                                    ->label('Thương hiệu')
                                    ->options(
                                        Brand::orderBy('name')
                                            ->pluck('name', 'id')
                                    )
                                    ->required()
                                    ->searchable(),
                            ]),

                        Section::make('Thông tin chi tiết')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextInput::make('origin')
                                            ->label('Xuất xứ')
                                            ->placeholder('VD: Đức, Trung Quốc'),

                                        TextInput::make('warranty')
                                            ->label('Bảo hành')
                                            ->placeholder('VD: 24 tháng'),

                                        TextInput::make('unit')
                                            ->label('Đơn vị tính')
                                            ->placeholder('VD: Bộ, Cái'),

                                        TextInput::make('condition')
                                            ->label('Tình trạng')
                                            ->placeholder('VD: Mới 100%'),

                                        TextInput::make('stock')
                                            ->label('Số lượng tồn kho')
                                            ->numeric()
                                            ->default(0)
                                            ->required(),
                                    ]),

                                Textarea::make('usage_instructions')
                                    ->label('Hướng dẫn sử dụng')
                                    ->rows(4)
                                    ->columnSpanFull(),

                                Repeater::make('technical_specs')
                                    ->label('Thông số kỹ thuật')
                                    ->schema([
                                        TextInput::make('key')
                                            ->label('Tên thông số')
                                            ->required(),
                                        TextInput::make('value')
                                            ->label('Giá trị')
                                            ->required(),
                                    ])
                                    ->columns(2)
                                    ->columnSpanFull()
                                    ->defaultItems(1),

                                Repeater::make('downloads')
                                    ->label('File tải về')
                                    ->schema([
                                        TextInput::make('title')
                                            ->label('Tiêu đề')
                                            ->required(),
                                        Select::make('type')
                                            ->label('Loại file')
                                            ->options([
                                                'PDF' => 'PDF',
                                                'DOC' => 'DOC/DOCX',
                                                'XLS' => 'XLS/XLSX',
                                                'MP4' => 'Video MP4',
                                                'ZIP' => 'ZIP',
                                            ])
                                            ->required(),
                                        FileUpload::make('url')
                                            ->label('File')
                                            ->directory('downloads')
                                            ->visibility('public')
                                            ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'video/mp4', 'application/zip'])
                                            ->maxSize(50000) // 50MB
                                            ->required(),
                                        TextInput::make('size')
                                            ->label('Kích thước file')
                                            ->placeholder('VD: 2.5 MB'),
                                    ])
                                    ->columns(2)
                                    ->columnSpanFull()
                                    ->defaultItems(0),
                            ]),

                        Section::make('Thiết lập hiển thị')
                            ->schema([
                                Toggle::make('is_featured')
                                    ->label('Sản phẩm nổi bật')
                                    ->default(false),

                                Toggle::make('is_new')
                                    ->label('Sản phẩm mới')
                                    ->default(false),

                                Toggle::make('is_best_seller')
                                    ->label('Sản phẩm bán chạy')
                                    ->default(false),

                                Toggle::make('status')
                                    ->label('Trạng thái hiển thị')
                                    ->default(true),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Group::make()
                    ->schema([
                        Section::make('Hình ảnh')
                            ->schema([
                                FileUpload::make('image_url')
                                    ->label('Hình ảnh chính')
                                    ->image()
                                    ->directory('products')
                                    ->visibility('public')
                                    ->maxSize(2048)
                                    ->imageResizeMode('cover')
                                    ->imageCropAspectRatio('1:1')
                                    ->imageResizeTargetWidth('600')
                                    ->imageResizeTargetHeight('600'),
                                    
                                FileUpload::make('gallery')
                                    ->label('Album ảnh sản phẩm')
                                    ->multiple()
                                    ->image()
                                    ->directory('products/gallery')
                                    ->visibility('public')
                                    ->maxSize(2048)
                                    ->imageResizeMode('cover')
                                    ->reorderable()
                                    ->helperText('Có thể tải lên nhiều ảnh cho sản phẩm. Kéo thả để sắp xếp thứ tự.')
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                ImageColumn::make('image_url')
                    ->label('Hình ảnh')
                    ->circular(),

                TextColumn::make('name')
                    ->label('Tên sản phẩm')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('sku')
                    ->label('Mã SP (SKU)')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('price')
                    ->label('Giá')
                    ->money('VND')
                    ->sortable(),

                TextColumn::make('sale_price')
                    ->label('Giá KM')
                    ->money('VND')
                    ->default('-')
                    ->sortable(),

                TextColumn::make('category.name')
                    ->label('Danh mục')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('brand.name')
                    ->label('Thương hiệu')
                    ->searchable()
                    ->sortable(),

                ToggleColumn::make('is_featured')
                    ->label('Nổi bật')
                    ->sortable()
                    ->afterStateUpdated(function($record, $state) {
                        $status = $state ? 'bật' : 'tắt';
                        return Notification::make()
                            ->title("Đã $status trạng thái nổi bật")
                            ->body("Sản phẩm \"{$record->name}\" đã được $status trạng thái nổi bật")
                            ->success()
                            ->send();
                    }),

                ToggleColumn::make('is_new')
                    ->label('Mới')
                    ->sortable()
                    ->afterStateUpdated(function($record, $state) {
                        $status = $state ? 'bật' : 'tắt';
                        return Notification::make()
                            ->title("Đã $status trạng thái mới")
                            ->body("Sản phẩm \"{$record->name}\" đã được $status trạng thái mới")
                            ->success()
                            ->send();
                    }),

                ToggleColumn::make('is_best_seller')
                    ->label('Bán chạy')
                    ->sortable()
                    ->afterStateUpdated(function($record, $state) {
                        $status = $state ? 'bật' : 'tắt';
                        return Notification::make()
                            ->title("Đã $status trạng thái bán chạy")
                            ->body("Sản phẩm \"{$record->name}\" đã được $status trạng thái bán chạy")
                            ->success()
                            ->send();
                    }),

                ToggleColumn::make('status')
                    ->label('Hiển thị')
                    ->sortable()
                    ->afterStateUpdated(function($record, $state) {
                        $status = $state ? 'bật' : 'tắt';
                        return Notification::make()
                            ->title("Đã $status trạng thái hiển thị")
                            ->body("Sản phẩm \"{$record->name}\" đã được $status trạng thái hiển thị")
                            ->success()
                            ->send();
                    }),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Danh mục')
                    ->relationship('category', 'name', fn (Builder $query) => $query->where('type', 'product')),

                Tables\Filters\SelectFilter::make('brand_id')
                    ->label('Thương hiệu')
                    ->relationship('brand', 'name'),

                Tables\Filters\TernaryFilter::make('status')
                    ->label('Trạng thái hiển thị'),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('copy_to_language')
                        ->label('Copy sang ngôn ngữ khác...')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $newRecord = $record->replicate();
                                $newRecord->lang = $data['target_lang'];
                                $newRecord->slug = $record->slug . '-' . $data['target_lang'];
                                $newRecord->sku = $record->sku ? $record->sku . '-' . $data['target_lang'] : null;
                                $newRecord->save();
                            }

                            Notification::make()
                                ->title('Đã copy ' . count($records) . ' bản ghi')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Chọn ngôn ngữ đích')
                                ->options([
                                    'vi' => 'Tiếng Việt',
                                    'en' => 'Tiếng Anh',
                                ])
                                ->required(),
                        ]),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
            'view' => Pages\ViewProduct::route('/{record}'),
        ];
    }
} 