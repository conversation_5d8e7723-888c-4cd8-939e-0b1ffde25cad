<?php

namespace App\View\Components;

use App\Models\Menu as MenuModel;
use App\Models\MenuItem;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use Illuminate\Support\Facades\Cache;

class Menu extends Component
{
    public $menu;
    public $items;

    /**
     * Create a new component instance.
     */
    public function __construct(string $name)
    {
        // Sử dụng cache để cải thiện hiệu suất
        $cacheKey = 'menu_' . $name;
        
        // Cache menu trong 60 phút, trừ khi được xóa thủ công khi cập nhật menu
        $this->menu = Cache::remember($cacheKey . '_model', 3600, function() use ($name) {
            return MenuModel::where('slug', $name)
                ->where('status', true)
                ->first();
        });
        
        if ($this->menu) {
            // Cache menu items
            $this->items = Cache::remember($cacheKey . '_items', 3600, function() {
                // Lấy menu items cấp cao nhất (không có parent)
                return $this->menu->items()
                    ->whereNull('parent_id')
                    ->where('status', true)
                    ->orderBy('order')
                    ->with(['children' => function($query) {
                        // Load các menu items con, đảm bảo sắp xếp theo order
                        $query->where('status', true)
                            ->orderBy('order')
                            ->with(['children' => function($query) {
                                // Load thêm một cấp nữa nếu cần (cho menu đa cấp)
                                $query->where('status', true)
                                    ->orderBy('order');
                            }]);
                    }])
                    ->get();
            });
        } else {
            $this->items = collect();
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.menu');
    }
}
