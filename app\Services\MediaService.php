<?php

namespace App\Services;

use App\Models\Media;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MediaService
{
    /**
     * Ánh xạ từ extension sang MIME type
     */
    protected $mimeTypeMap = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'zip' => 'application/zip',
        'txt' => 'text/plain',
    ];

    /**
     * Hàm helper để format kích thước file
     */
    public function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        $size = $bytes;
        $i = 0;
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        return round($size, 2) . ' ' . $units[$i];
    }

    /**
     * Upload file và tạo bản ghi media
     */
    public function upload($file, string $directory = 'media', ?string $disk = null): Media
    {
        $disk = $disk ?? config('filesystems.default', 'public');
        
        // Debug input
        Log::info('MediaService::upload - Input file', [
            'type' => gettype($file),
            'is_temporary' => $file instanceof TemporaryUploadedFile,
            'is_uploaded' => $file instanceof UploadedFile,
            'directory' => $directory,
            'disk' => $disk
        ]);
        
        // Khởi tạo các biến mặc định
        $path = '';
        $fileName = '';
        $extension = '';
        $mimeType = '';
        $size = 0;
        
        // Xử lý file từ Filament
        try {
            // Xử lý file upload
            if ($file instanceof TemporaryUploadedFile) {
                // Lưu file từ TemporaryUploadedFile của Filament
                $path = $file->store($directory, ['disk' => $disk]);
                $fileName = $file->getClientOriginalName() ?: $file->getFilename();
                $extension = $file->getClientOriginalExtension() ?: pathinfo($file->getFilename(), PATHINFO_EXTENSION);
                $mimeType = $file->getMimeType() ?: Storage::disk($disk)->mimeType($path);
                $size = $file->getSize();
                
                Log::info('MediaService - Xử lý TemporaryUploadedFile', [
                    'path' => $path,
                    'fileName' => $fileName,
                    'mimeType' => $mimeType,
                    'size' => $size
                ]);
            } elseif ($file instanceof UploadedFile) {
                // Lưu file từ UploadedFile thông thường
                $path = $file->store($directory, $disk);
                $fileName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                $mimeType = $file->getMimeType() ?: $file->getClientMimeType();
                $size = $file->getSize();
                
                Log::info('MediaService - Xử lý UploadedFile', [
                    'path' => $path,
                    'fileName' => $fileName,
                    'mimeType' => $mimeType,
                    'size' => $size
                ]);
            } elseif (is_string($file) && str_starts_with($file, 'livewire-file:')) {
                // Xử lý trường hợp Livewire/Filament gửi file dưới dạng chuỗi
                Log::info('MediaService - Xử lý livewire-file string', ['file' => $file]);
                try {
                    $tempFile = TemporaryUploadedFile::createFromLivewire($file);
                    return $this->upload($tempFile, $directory, $disk);
                } catch (\Exception $e) {
                    Log::error('Lỗi chuyển đổi livewire-file', [
                        'error' => $e->getMessage(),
                        'file' => $file
                    ]);
                }
            } elseif (is_string($file) && Storage::disk($disk)->exists($file)) {
                // Xử lý trường hợp đã là đường dẫn file trong storage
                $path = $file;
                $fileName = basename($file);
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                $mimeType = Storage::disk($disk)->mimeType($path);
                $size = Storage::disk($disk)->size($path);
                
                Log::info('MediaService - Xử lý file path', [
                    'path' => $path,
                    'fileName' => $fileName,
                    'mimeType' => $mimeType
                ]);
            } elseif (is_string($file) && filter_var($file, FILTER_VALIDATE_URL)) {
                // Xử lý trường hợp URL
                Log::info('MediaService - Đang xử lý URL', ['url' => $file]);
                
                // Tạo tên file từ URL
                $fileName = basename(parse_url($file, PHP_URL_PATH));
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                
                // Tải file về
                $tempFile = tempnam(sys_get_temp_dir(), 'download_');
                file_put_contents($tempFile, file_get_contents($file));
                
                // Lưu vào storage
                $path = Storage::disk($disk)->putFileAs(
                    $directory,
                    $tempFile,
                    $fileName
                );
                
                // Lấy thông tin file
                $mimeType = Storage::disk($disk)->mimeType($path);
                $size = Storage::disk($disk)->size($path);
                
                // Xóa file tạm
                @unlink($tempFile);
                
                Log::info('MediaService - URL đã được tải về', [
                    'path' => $path,
                    'mimeType' => $mimeType,
                    'size' => $size
                ]);
            } elseif (is_array($file) && isset($file['path'])) {
                // Xử lý trường hợp khi truyền vào là mảng có chứa thông tin file
                $path = $file['path'];
                $fileName = $file['file_name'] ?? basename($path);
                $extension = $file['extension'] ?? pathinfo($fileName, PATHINFO_EXTENSION);
                $mimeType = $file['mime_type'] ?? Storage::disk($disk)->mimeType($path);
                $size = $file['size'] ?? Storage::disk($disk)->size($path);
                
                Log::info('MediaService - Xử lý mảng thông tin file', [
                    'path' => $path,
                    'fileName' => $fileName
                ]);
            } else {
                // Thông tin chi tiết để debug
                Log::error('MediaService - Loại file không hỗ trợ', [
                    'type' => gettype($file),
                    'is_string' => is_string($file),
                    'is_array' => is_array($file),
                    'file' => $file,
                ]);
                
                // Tạo một file trống nếu không thể xử lý
                $fileName = 'file_' . time() . '.txt';
                $path = "{$directory}/{$fileName}";
                Storage::disk($disk)->put($path, 'File không hỗ trợ: ' . gettype($file));
                $extension = 'txt';
                $mimeType = 'text/plain';
                $size = Storage::disk($disk)->size($path);
            }
        } catch (\Exception $e) {
            // Log lỗi chi tiết
            Log::error('MediaService - Lỗi xử lý file', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file_type' => gettype($file)
            ]);
            
            // Tạo file lỗi để trả về
            $fileName = 'error_' . time() . '.txt';
            $path = "{$directory}/{$fileName}";
            Storage::disk($disk)->put($path, 'Lỗi: ' . $e->getMessage());
            $extension = 'txt';
            $mimeType = 'text/plain';
            $size = Storage::disk($disk)->size($path);
        }
        
        // Đảm bảo path không rỗng
        if (empty($path)) {
            Log::error('MediaService - Path rỗng sau khi xử lý file');
            $fileName = 'empty_' . time() . '.txt';
            $path = "{$directory}/{$fileName}";
            Storage::disk($disk)->put($path, 'File không hỗ trợ hoặc path rỗng');
            $extension = 'txt';
            $mimeType = 'text/plain';
            $size = Storage::disk($disk)->size($path);
        }
        
        // Nếu không lấy được mime type, thử đoán từ extension
        if (empty($mimeType)) {
            $ext = strtolower($extension);
            $mimeType = $this->mimeTypeMap[$ext] ?? 'application/octet-stream';
            Log::info('MediaService - Mime type được đoán từ extension', [
                'extension' => $ext,
                'mimeType' => $mimeType
            ]);
        }
        
        // Tạo bản ghi media
        $media = Media::create([
            'name' => pathinfo($fileName, PATHINFO_FILENAME),
            'file_name' => $fileName,
            'mime_type' => $mimeType,
            'size' => $size,
            'disk' => $disk,
            'directory' => $directory,
            'path' => $path,
            'extension' => $extension,
        ]);
        
        Log::info('MediaService - Đã tạo bản ghi media', [
            'id' => $media->id,
            'path' => $media->path,
            'mimeType' => $media->mime_type
        ]);
        
        // Tạo thumbnail nếu là ảnh
        if (str_starts_with($mimeType, 'image/')) {
            $this->createThumbnail($media);
        }
        
        return $media;
    }
    
    /**
     * Tạo thumbnail cho ảnh
     */
    public function createThumbnail(Media $media, int $width = 50, int $height = 50): bool
    {
        if (!str_starts_with($media->mime_type, 'image/')) {
            return false;
        }
        
        try {
            // Đường dẫn đến file gốc
            $originalPath = Storage::disk($media->disk)->path($media->path);
            
            // Tạo thư mục cho thumbnail nếu chưa tồn tại
            $thumbnailDir = dirname($media->path) . '/thumbnails';
            if (!Storage::disk($media->disk)->exists($thumbnailDir)) {
                Storage::disk($media->disk)->makeDirectory($thumbnailDir);
            }
            
            // Đường dẫn thumbnail
            $thumbnailPath = $this->getThumbnailPath($media);
            $thumbnailFullPath = Storage::disk($media->disk)->path($thumbnailPath);
            
            // Tạo thumbnail sử dụng GD
            $sourceImage = $this->createImageFromFile($originalPath, $media->mime_type);
            if (!$sourceImage) {
                Log::error('Không thể tạo ảnh từ file', [
                    'path' => $originalPath,
                    'mime_type' => $media->mime_type
                ]);
                return false;
            }
            
            // Lấy kích thước ảnh gốc
            $originalWidth = imagesx($sourceImage);
            $originalHeight = imagesy($sourceImage);
            
            // Cập nhật kích thước vào media record
            $media->update([
                'width' => $originalWidth,
                'height' => $originalHeight,
            ]);
            
            // Tạo thumbnail
            $thumbnail = imagecreatetruecolor($width, $height);
            
            // Giữ độ trong suốt cho PNG
            if ($media->mime_type === 'image/png') {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefilledrectangle($thumbnail, 0, 0, $width, $height, $transparent);
            }
            
            // Resize giữ tỷ lệ
            imagecopyresampled(
                $thumbnail, 
                $sourceImage, 
                0, 0, 0, 0, 
                $width, $height, 
                $originalWidth, $originalHeight
            );
            
            // Lưu thumbnail
            $this->saveImage($thumbnail, $thumbnailFullPath, $media->mime_type);
            
            // Giải phóng bộ nhớ
            imagedestroy($sourceImage);
            imagedestroy($thumbnail);
            
            Log::info('MediaService - Đã tạo thumbnail thành công', [
                'media_id' => $media->id,
                'thumbnail_path' => $thumbnailPath,
                'size' => $width . 'x' . $height
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('MediaService - Không thể tạo thumbnail', [
                'error' => $e->getMessage(),
                'media_id' => $media->id,
                'path' => $media->path
            ]);
            return false;
        }
    }
    
    /**
     * Tạo đối tượng image từ file
     */
    private function createImageFromFile(string $path, string $mimeType)
    {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagecreatefromjpeg($path);
            case 'image/png':
                return imagecreatefrompng($path);
            case 'image/gif':
                return imagecreatefromgif($path);
            case 'image/webp':
                if (function_exists('imagecreatefromwebp')) {
                    return imagecreatefromwebp($path);
                }
                break;
            default:
                return false;
        }
    }
    
    /**
     * Lưu ảnh theo định dạng
     */
    private function saveImage($image, string $path, string $mimeType): bool
    {
        switch ($mimeType) {
            case 'image/jpeg':
                return imagejpeg($image, $path, 90);
            case 'image/png':
                return imagepng($image, $path, 9);
            case 'image/gif':
                return imagegif($image, $path);
            case 'image/webp':
                if (function_exists('imagewebp')) {
                    return imagewebp($image, $path, 90);
                }
                // Fallback to JPEG if WebP is not supported
                return imagejpeg($image, $path, 90);
            default:
                return false;
        }
    }
    
    /**
     * Lấy đường dẫn thumbnail
     */
    public function getThumbnailPath(Media $media): string
    {
        $pathInfo = pathinfo($media->path);
        $extension = $pathInfo['extension'] ?? '';
        $filename = $pathInfo['filename'] ?? '';
        $dirname = $pathInfo['dirname'] ?? '';
        
        return "{$dirname}/thumbnails/{$filename}_thumb.{$extension}";
    }
    
    /**
     * Xóa media và file liên quan
     * 
     * @param Media $media Media cần xóa
     * @param bool $deleteRecord Có xóa bản ghi trong DB không
     * @return bool
     */
    public function delete(Media $media, bool $deleteRecord = true): bool
    {
        try {
            Log::info('MediaService - Bắt đầu xóa media', [
                'id' => $media->id,
                'path' => $media->path
            ]);
            
            // Xóa file
            $this->deleteFiles($media);
            
            // Xóa bản ghi nếu cần
            if ($deleteRecord) {
                // Dùng DB::table để tránh gọi lại event deleting của model
                DB::table('media')->where('id', $media->id)->delete();
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('MediaService - Không thể xóa media', [
                'error' => $e->getMessage(),
                'id' => $media->id,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * Chỉ xóa files mà không xóa bản ghi
     */
    public function deleteFiles(Media $media): bool
    {
        try {
            // Xóa file gốc
            if (Storage::disk($media->disk)->exists($media->path)) {
                Storage::disk($media->disk)->delete($media->path);
            }
            
            // Xóa thumbnail nếu có
            $thumbnailPath = $this->getThumbnailPath($media);
            if (Storage::disk($media->disk)->exists($thumbnailPath)) {
                Storage::disk($media->disk)->delete($thumbnailPath);
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('MediaService - Không thể xóa files', [
                'error' => $e->getMessage(),
                'id' => $media->id
            ]);
            return false;
        }
    }
}