{"__meta": {"id": "01K0AAVFECQ4XTHXGKHKP34JR2", "datetime": "2025-07-17 02:19:47", "utime": **********.406252, "method": "GET", "uri": "/test-menu", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[02:19:47] LOG.info: Route /{slug} đ<PERSON><PERSON><PERSON> gọi với slug: test-menu", "message_html": null, "is_string": false, "label": "info", "time": **********.346772, "xdebug_link": null, "collector": "log"}, {"message": "[02:19:47] LOG.info: Kiểm tra Post với slug: test-menu - <PERSON><PERSON><PERSON>ng tìm thấy", "message_html": null, "is_string": false, "label": "info", "time": **********.385452, "xdebug_link": null, "collector": "log"}, {"message": "[02:19:47] LOG.info: <PERSON><PERSON><PERSON><PERSON> tìm thấy gì, redirect về home", "message_html": null, "is_string": false, "label": "info", "time": **********.390778, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752693585.219935, "end": **********.406285, "duration": 2.186350107192993, "duration_str": "2.19s", "measures": [{"label": "Booting", "start": 1752693585.219935, "relative_start": 0, "end": **********.894673, "relative_end": **********.894673, "duration": 1.****************, "duration_str": "1.67s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.89471, "relative_start": 1.****************, "end": **********.406287, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "512ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.292559, "relative_start": 2.****************, "end": **********.30286, "relative_end": **********.30286, "duration": 0.*****************, "duration_str": "10.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.399997, "relative_start": 2.****************, "end": **********.400714, "relative_end": **********.400714, "duration": 0.0007169246673583984, "duration_str": "717μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029810000000000003, "accumulated_duration_str": "29.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `static_pages` where `slug` = 'test-menu' limit 1", "type": "query", "params": [], "bindings": ["test-menu"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 299}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.349344, "duration": 0.02169, "duration_str": "21.69ms", "memory": 0, "memory_str": null, "filename": "web.php:299", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 299}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=299", "ajax": false, "filename": "web.php", "line": "299"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 72.761}, {"sql": "select * from `posts` where `slug` = 'test-menu' and `status` = 'published' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["test-menu", "published", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 310}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.3786938, "duration": 0.00549, "duration_str": "5.49ms", "memory": 0, "memory_str": null, "filename": "web.php:310", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 310}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=310", "ajax": false, "filename": "web.php", "line": "310"}, "connection": "auvista", "explain": null, "start_percent": 72.761, "width_percent": 18.417}, {"sql": "select * from `categories` where `slug` = 'test-menu' limit 1", "type": "query", "params": [], "bindings": ["test-menu"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 334}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.387022, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "web.php:334", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=334", "ajax": false, "filename": "web.php", "line": "334"}, "connection": "auvista", "explain": null, "start_percent": 91.177, "width_percent": 8.823}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://auvista.test/test-menu", "action_name": "static.page", "controller_action": "Closure", "uri": "GET {slug}", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=295\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:295-391</a>", "middleware": "web", "duration": "2.15s", "peak_memory": "44MB", "response": "Redirect to https://auvista.test", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1359732705 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1359732705\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1289269685 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1289269685\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-72263138 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">curl/8.12.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72263138\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1542236612 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1542236612\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2088798775 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 19:19:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088798775\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1012369463 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">xwdG1raA74q1hNRAlDAHla55NbkRu3jHpR11RgFm</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012369463\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://auvista.test/test-menu", "action_name": "static.page", "controller_action": "Closure"}, "badge": "302 Found"}}