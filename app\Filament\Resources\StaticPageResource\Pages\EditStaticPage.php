<?php

namespace App\Filament\Resources\StaticPageResource\Pages;

use App\Filament\Resources\StaticPageResource;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class EditStaticPage extends EditRecord
{
    protected static string $resource = StaticPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->hidden(fn (Model $record): bool => $record->id === 1),
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Kiểm tra trùng lặp slug và tự động điều chỉnh nếu cần
        if (isset($data['slug']) && $record->id !== 1) {
            $originalSlug = $data['slug'];
            $slug = $originalSlug;
            $count = 1;
            
            while (\App\Models\StaticPage::where('slug', $slug)->where('id', '!=', $record->id)->exists()) {
                $slug = $originalSlug . '-' . $count++;
            }
            
            if ($slug !== $originalSlug) {
                $data['slug'] = $slug;
                
                // Hiển thị thông báo nếu slug đã được điều chỉnh
                Notification::make()
                    ->title('Thông báo về Slug')
                    ->body("Slug '{$originalSlug}' đã tồn tại và đã được tự động điều chỉnh thành '{$slug}'.")
                    ->success()
                    ->send();
            }
        }
        
        $record->update($data);
        
        return $record;
    }
} 