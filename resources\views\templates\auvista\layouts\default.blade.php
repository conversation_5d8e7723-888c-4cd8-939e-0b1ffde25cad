<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {{-- SEO Meta Tags --}}
    <meta name="description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta name="keywords" content="{{ isset($seo_keywords) ? $seo_keywords : getSetting('site_keywords', '') }}">
    <meta name="author" content="{{ isset($seo_author) ? $seo_author : getSetting('site_name', '') }}">
    <meta name="robots" content="{{ isset($seo_robots) ? $seo_robots : 'index, follow' }}">
    <meta name="canonical" content="{{ isset($seo_canonical) ? $seo_canonical : url()->current() }}">

    {{-- Open Graph Tags for Social Sharing --}}
    <meta property="og:title" content="{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}">
    <meta property="og:description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta property="og:image" content="{{ isset($seo_image) ? $seo_image : getSetting('og_image', '') }}">
    <meta property="og:url" content="{{ isset($seo_canonical) ? $seo_canonical : url()->current() }}">
    <meta property="og:type" content="{{ isset($seo_type) ? $seo_type : 'website' }}">
    <meta property="og:site_name" content="{{ isset($seo_author) ? $seo_author : getSetting('site_name', '') }}">

    {{-- Twitter Card Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}">
    <meta name="twitter:description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta name="twitter:image" content="{{ isset($seo_image) ? $seo_image : getSetting('og_image', '') }}">

    
    {{-- CSRF Token --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}</title>

    {{-- Favicon --}}
    @php
        $faviconPath = getSetting('site_logo'); // Ưu tiên site_logo trước
        if (empty($faviconPath)) {
            $faviconPath = getSetting('site_favicon'); // Dự phòng site_favicon
        }
        if (empty($faviconPath)) {
            $faviconPath = 'favicon/favicon.ico';
        }

        // Tạo URL favicon đơn giản
        if (filter_var($faviconPath, FILTER_VALIDATE_URL)) {
            $faviconUrl = $faviconPath;
        } elseif (file_exists(storage_path('app/public/' . $faviconPath))) {
            $faviconUrl = asset('storage/' . $faviconPath);
        } else {
            $faviconUrl = asset($faviconPath);
        }
    @endphp

    <link rel="icon" href="{{ $faviconUrl }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ $faviconUrl }}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{{ $faviconUrl }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ $faviconUrl }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ $faviconUrl }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ $faviconUrl }}">

    {{-- Font Awesome --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    {{-- Swiper CSS --}}
    <link rel="stylesheet" href="{{ asset('assets/css/swiper-bundle.min.css') }}" />

    {{-- Vite will now handle all CSS and JS through this single entry point --}}
    @vite('resources/js/app.js')

    @stack('styles')
    @livewireStyles

    {{-- Include Product Styles (except for categories page) --}}
    @unless (request()->routeIs('products.categories'))
        @include('templates.auvista.components.product_styles')
    @endunless

    <style>
        /* Force HTML template styles to override any conflicting styles */
        .bg-gradient8 {
            background: linear-gradient(180deg, #04389D 0%, #17C1F5 100%) !important;
            background-image: linear-gradient(180deg, #04389D 0%, #17C1F5 100%) !important;
        }

        /* Ensure rounded corners work */
        .rounded-3xl {
            border-radius: 1.5rem !important;
        }

        /* Ensure wave image displays */
        .relative.z-10 {
            position: relative !important;
            z-index: 10 !important;
        }

        /* Error Pages */
        .error-page {
            min-height: 60vh;
            display: flex;
            align-items: center;
        }

        .error-content {
            padding: 2rem;
        }

        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: #e5e7eb;
            line-height: 1;
            margin-bottom: 1rem;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }

        .error-page .btn-primary {
            padding: 0.75rem 2rem;
            font-size: 1.125rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .error-page .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Remove extra spacing at bottom of page */
        body {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        footer {
            margin-top: 0 !important;
        }

        /* Remove any extra space from page wrapper */
        .page-wrapper {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        /* Force remove min-height that might create white space */
        * {
            min-height: auto !important;
        }

        /* Specifically target common containers */
        .container,
        .wrapper,
        main,
        #app,
        .app {
            min-height: auto !important;
        }

        /* Remove any potential viewport height usage */
        html,
        body {
            height: auto !important;
            min-height: auto !important;
        }

        /* Fixed Header Styles */
        body {
            padding-top: 130px;
            /* Reduced space for fixed header with navigation pulled up */
        }

        /* Header scroll effects */
        header.scrolled {
            background: rgba(4, 56, 157, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Dropdown Menu Styles */
        .mainMenu .group:hover .absolute {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        .mainMenu .absolute {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            background: white;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            min-width: 220px;
            z-index: 1000;
        }

        .mainMenu .absolute::before {
            content: '';
            position: absolute;
            top: -6px;
            left: 20px;
            width: 12px;
            height: 12px;
            background: white;
            border: 1px solid #e5e7eb;
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        .mainMenu .absolute a {
            display: block;
            padding: 12px 16px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .mainMenu .absolute a:last-child {
            border-bottom: none;
        }

        .mainMenu .absolute a:hover {
            background-color: #f8fafc;
            color: #04389D;
            padding-left: 20px;
        }

        .mainMenu .absolute a:first-child {
            border-radius: 8px 8px 0 0;
        }

        .mainMenu .absolute a:last-child {
            border-radius: 0 0 8px 8px;
        }

        /* Mobile responsive for dropdown */
        @media (max-width: 768px) {
            .mainMenu .absolute {
                position: static !important;
                opacity: 1 !important;
                visibility: visible !important;
                transform: none !important;
                box-shadow: none;
                border: none;
                background: transparent;
                margin-left: 20px;
                margin-top: 10px;
            }

            .mainMenu .absolute::before {
                display: none;
            }

            .mainMenu .absolute a {
                color: rgba(255, 255, 255, 0.8);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                padding: 8px 0;
            }

            .mainMenu .absolute a:hover {
                color: white;
                background: transparent;
                padding-left: 0;
            }
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
                /* Smaller padding for mobile */
            }
        }

        .gradient-bg {
            background: linear-gradient(to bottom right, #1e3a8a, #3b82f6);
        }

        .back-to-top {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            display: none;
        }

        .back-to-top.show {
            display: block;
        }

        /* Product Grid - Ensure equal height cards */
        .products.list-products {
            display: grid !important;
            align-items: stretch !important;
        }

        .products.list-products > * {
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }

        /* Ensure product cards use full height */
        .product-card {
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }
    </style>
</head>

<body>
    @if (!request()->is('admin') && !request()->is('admin/*'))
        {{-- Frontend Layout --}}
        @include('templates.auvista.blocks.header')

        @hasSection('breadcrumbs')
            @yield('breadcrumbs')
        @else
            @unless (request()->routeIs('products.brand'))
                @include('templates.auvista.blocks.breadcrumb')
            @endunless
        @endif

        <main class="main-content">
            @yield('content')
        </main>

        <livewire:cart-popup />

        @include('templates.auvista.blocks.footer')

        {{-- Fixed Contact Widget - Hiển thị trên tất cả trang --}}
        <div class="fixed top-1/2 right-0 transform -translate-y-1/2 z-50 space-y-3">
            <!-- Hotline Button 1 -->
            <a href="tel:0979998877" class="block">
                <div class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out">
                    <div class="flex items-center gap-3">
                        <div class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                {{ __('messages.hotline') }}: 0979998877
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Hotline Button 2 -->
            <a href="tel:0988888988" class="block">
                <div class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out">
                    <div class="flex items-center gap-3">
                        <div class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"/>
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                {{ __('messages.hotline') }}: 0988888988
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Zalo Button -->
            <a href="https://zalo.me/0979998877" target="_blank" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-zalo.png') }}" alt="zalo" class="w-12 h-12" />
            </a>

            <!-- Messenger Button -->
            <a href="https://m.me/avplus.vietnam" target="_blank" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-messenger.png') }}" alt="messenger" class="w-12 h-12" />
            </a>

            <!-- Email Button -->
            <a href="mailto:<EMAIL>" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-email.png') }}" alt="email" class="w-12 h-12" />
            </a>
        </div>

        {{--  bằng tailwind --}}
        <button id="backToTop"
            class="back-to-top gradient-bg bg-blue-500 text-white rounded-full p-3 shadow-lg hover:bg-blue-600 transition">
            <i class="fas fa-arrow-up"></i>
        </button>
    @else
        {{-- Admin Layout --}}
        <main class="main-content">
            @yield('content')
        </main>
    @endif

    @livewireScripts
    @stack('scripts')

    {{-- Swiper JS --}}
    <script src="{{ asset('js/swiper-bundle.min.js') }}"></script>
    {{-- Main JS with Swiper initialization --}}
    <script src="{{ asset('js/main.js') }}?v={{ time() }}"></script>
</body>

</html>
