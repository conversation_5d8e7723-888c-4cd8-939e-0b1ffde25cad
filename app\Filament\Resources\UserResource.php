<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;

class UserResource extends BaseResource
{
    protected static ?string $model = User::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'Quản lý tài khoản';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = 'Người dùng';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Họ tên')
                            ->required(),
                        Forms\Components\TextInput::make('email')
                            ->label('Email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('phone')
                            ->label('Số điện thoại')
                            ->tel()
                            ->telRegex('/^([0-9\s\-\+\(\)]*)$/')
                            ->maxLength(20),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'active' => 'Hoạt động',
                                'inactive' => 'Tạm khóa',
                                'banned' => 'Cấm vĩnh viễn',
                            ])
                            ->default('active')
                            ->required(),
                        Forms\Components\TextInput::make('password')
                            ->label('Mật khẩu')
                            ->password()
                            ->dehydrateStateUsing(fn (?string $state): ?string => 
                                filled($state) ? Hash::make($state) : null)
                            ->dehydrated(fn (?string $state): bool => 
                                filled($state))
                            ->required(fn ($livewire) => $livewire instanceof Pages\CreateUser)
                            ->confirmed()
                            ->minLength(8),
                        Forms\Components\TextInput::make('password_confirmation')
                            ->label('Xác nhận mật khẩu')
                            ->password()
                            ->requiredWith('password'),
                        Forms\Components\FileUpload::make('avatar')
                            ->label('Ảnh đại diện')
                            ->image()
                            ->directory('avatars')
                            ->preserveFilenames()
                            ->imageEditor()
                            ->columnSpanFull(),
                    ])->columnSpan(['lg' => 2]),

                Forms\Components\Section::make('Quyền hạn')
                    ->schema([
                        Forms\Components\Select::make('roles')
                            ->label('Vai trò')
                            ->multiple()
                            ->relationship('roles', 'name')
                            ->preload(),
                    ])->columnSpan(['lg' => 1]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('Ảnh')
                    ->circular()
                    ->defaultImageUrl(asset(Config::get('filament-media.defaults.placeholder_image.url')))
                    ->size(80),
                Tables\Columns\TextColumn::make('name')
                    ->label('Họ tên')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->label('Email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Số điện thoại')
                    ->searchable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'inactive',
                        'danger' => 'banned',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'active' => 'Hoạt động',
                        'inactive' => 'Tạm khóa',
                        'banned' => 'Bị cấm',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('roles.name')
                    ->label('Vai trò')
                    ->badge(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime(Config::get('app.displayFormat', 'd/m/Y H:i'))
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('roles')
                    ->label('Vai trò')
                    ->relationship('roles', 'name'),
            ])
            ->actions([
                ViewAction::make()
                    ->modalWidth('7xl')
                    // Only admin can view admin
                    ->visible(fn (Model $record): bool => 
                        ($record->id !== 1) || (auth()->id() === 1)),
                EditAction::make()
                    ->modalWidth('7xl')
                    // Only admin can edit admin
                    ->visible(fn (Model $record): bool => 
                        ($record->id !== 1) || (auth()->id() === 1)),
                DeleteAction::make()
                    // Admin cannot be deleted
                    ->hidden(fn (Model $record): bool => $record->id === 1)
                    ->before(function (Model $record) {
                        if ($record->id === 1) {
                            Notification::make()
                                ->danger()
                                ->title('Protected account')
                                ->body('The admin account cannot be deleted.')
                                ->send();
                            
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Collection $records) {
                            if ($records->contains('id', 1)) {
                                Notification::make()
                                    ->danger()
                                    ->title('Protected account')
                                    ->body('The admin account cannot be deleted.')
                                    ->send();
                                
                                return $records->filter(fn ($record) => $record->id !== 1);
                            }
                        }),
                ]),
            ])
            ->headerActions([
                CreateAction::make()->modalWidth('7xl'),
            ])
            ->recordAction(null);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
        ];
    }
}
