<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class CategoryProductController extends Controller
{
    /**
     * Hiển thị danh sách danh mục sản phẩm
     */
    public function index()
    {
        $categories = Category::where('type', 'product')
            ->orderBy('name')
            ->paginate(15);

        return view('admin.category-products.index', compact('categories'));
    }

    /**
     * Hiển thị form tạo danh mục sản phẩm mới
     */
    public function create()
    {
        $parentCategories = Category::where('type', 'product')
            ->where('parent_id', 0)
            ->orderBy('name')
            ->get();

        return view('admin.category-products.create', compact('parentCategories'));
    }

    /**
     * <PERSON><PERSON><PERSON> mụ<PERSON> sản phẩm mới
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|max:255',
            'slug' => 'required|unique:categories|max:255',
            'description' => 'nullable',
            'parent_id' => 'nullable|integer',
            'status' => 'required|in:active,inactive',
            'seo_title' => 'nullable|max:255',
            'seo_description' => 'nullable',
            'seo_keywords' => 'nullable|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $validatedData['type'] = 'product';
        
        if ($request->hasFile('thumbnail')) {
            $path = $request->file('thumbnail')->store('categories', 'public');
            $validatedData['thumbnail'] = $path;
        }

        if (empty($validatedData['parent_id'])) {
            $validatedData['parent_id'] = 0;
        }

        Category::create($validatedData);

        return redirect()->route('admin.category-products.index')
            ->with('success', 'Danh mục sản phẩm đã được tạo thành công.');
    }

    /**
     * Hiển thị form chỉnh sửa danh mục sản phẩm
     */
    public function edit($id)
    {
        $category = Category::findOrFail($id);
        
        // Đảm bảo chỉ chỉnh sửa danh mục loại 'product'
        if ($category->type !== 'product') {
            return redirect()->route('admin.category-products.index')
                ->with('error', 'Không thể chỉnh sửa danh mục này.');
        }
        
        $parentCategories = Category::where('type', 'product')
            ->where('parent_id', 0)
            ->where('id', '!=', $id) // Loại bỏ category hiện tại
            ->orderBy('name')
            ->get();

        return view('admin.category-products.edit', compact('category', 'parentCategories'));
    }

    /**
     * Cập nhật danh mục sản phẩm
     */
    public function update(Request $request, $id)
    {
        $category = Category::findOrFail($id);
        
        // Đảm bảo chỉ cập nhật danh mục loại 'product'
        if ($category->type !== 'product') {
            return redirect()->route('admin.category-products.index')
                ->with('error', 'Không thể cập nhật danh mục này.');
        }
        
        $validatedData = $request->validate([
            'name' => 'required|max:255',
            'slug' => ['required', 'max:255', Rule::unique('categories')->ignore($id)],
            'description' => 'nullable',
            'parent_id' => 'nullable|integer',
            'status' => 'required|in:active,inactive',
            'seo_title' => 'nullable|max:255',
            'seo_description' => 'nullable',
            'seo_keywords' => 'nullable|max:255',
            'thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($request->hasFile('thumbnail')) {
            // Xóa ảnh cũ nếu có
            if ($category->thumbnail) {
                Storage::disk('public')->delete($category->thumbnail);
            }
            
            $path = $request->file('thumbnail')->store('categories', 'public');
            $validatedData['thumbnail'] = $path;
        }

        if (empty($validatedData['parent_id'])) {
            $validatedData['parent_id'] = 0;
        }

        $category->update($validatedData);

        return redirect()->route('admin.category-products.index')
            ->with('success', 'Danh mục sản phẩm đã được cập nhật thành công.');
    }

    /**
     * Xóa danh mục sản phẩm
     */
    public function destroy($id)
    {
        $category = Category::findOrFail($id);
        
        // Đảm bảo chỉ xóa danh mục loại 'product'
        if ($category->type !== 'product') {
            return redirect()->route('admin.category-products.index')
                ->with('error', 'Không thể xóa danh mục này.');
        }
        
        // Kiểm tra xem có danh mục con không
        if (Category::where('parent_id', $id)->exists()) {
            return redirect()->route('admin.category-products.index')
                ->with('error', 'Không thể xóa danh mục này vì có danh mục con.');
        }
        
        // Kiểm tra xem có sản phẩm trong danh mục không
        if ($category->products()->exists()) {
            return redirect()->route('admin.category-products.index')
                ->with('error', 'Không thể xóa danh mục này vì có sản phẩm trong danh mục.');
        }
        
        // Xóa ảnh nếu có
        if ($category->thumbnail) {
            Storage::disk('public')->delete($category->thumbnail);
        }
        
        $category->delete();

        return redirect()->route('admin.category-products.index')
            ->with('success', 'Danh mục sản phẩm đã được xóa thành công.');
    }
} 