<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            'vi' => [
                // General Info
                ['name' => 'site_title', 'value' => 'auvista - nơi âm thanh và tầm nhìn gặp gỡ', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Tiêu đề website'],
                ['name' => 'site_description', 'value' => 'Chuyên cung cấp các sản phẩm chính hãng', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Mô tả website'],
                ['name' => 'site_copyright', 'value' => 'Bản quyền © 2025 Auvista.', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Bản quyền'],
                ['name' => 'designer_credit', 'value' => 'Thiết kế bởi Uni Creation Việt Nam', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Đơn vị thiết kế'],

                // Contact
                ['name' => 'contact_address', 'value' => 'Trụ sở chính 88 Thái Hà, Hà Nội', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Địa chỉ'],
                ['name' => 'office_address', 'value' => '123 Chùa Bộc, Hà Nội', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Địa chỉ văn phòng'],
                ['name' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Email'],
                ['name' => 'contact_phone', 'value' => '098888898', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Số điện thoại'],
            ],
            'en' => [
                // General Info
                ['name' => 'site_title', 'value' => 'auvista - where sound meets vision', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Website Title'],
                ['name' => 'site_description', 'value' => 'Specializing in providing genuine products', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Website Description'],
                ['name' => 'site_copyright', 'value' => 'Copyright © 2025 Auvista.', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Copyright'],
                ['name' => 'designer_credit', 'value' => 'Designed by Uni Creation Viet Nam', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Designer Credit'],
                
                // Contact
                ['name' => 'contact_address', 'value' => 'Head Office 88 Thai Ha, Hanoi', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Address'],
                ['name' => 'office_address', 'value' => '123 Chua Boc, Hanoi', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Office Address'],
                ['name' => 'contact_email', 'value' => '<EMAIL>', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Email'],
                ['name' => 'contact_phone', 'value' => '098888898', 'type' => 'string', 'group' => 'Liên hệ', 'description' => 'Phone Number'],
            ]
        ];
        
        $commonSettings = [
            // Common settings that don't need translation
            ['name' => 'site_logo', 'value' => 'settings/logo.png', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Logo website'],
            ['name' => 'site_favicon', 'value' => 'settings/favicon.ico', 'type' => 'string', 'group' => 'Thông tin chung', 'description' => 'Favicon website'],
            ['name' => 'og_image', 'value' => 'settings/og-default.jpg', 'type' => 'string', 'group' => 'SEO', 'description' => 'Ảnh mặc định cho social sharing'],
            ['name' => 'site_name', 'value' => 'Auvista', 'type' => 'string', 'group' => 'SEO', 'description' => 'Tên website cho social sharing'],
            ['name' => 'facebook_url', 'value' => 'https://www.facebook.com', 'type' => 'string', 'group' => 'Mạng xã hội', 'description' => 'Facebook URL'],
            ['name' => 'youtube_url', 'value' => 'https://www.youtube.com', 'type' => 'string', 'group' => 'Mạng xã hội', 'description' => 'Youtube URL'],
            ['name' => 'instagram_url', 'value' => 'https://www.instagram.com', 'type' => 'string', 'group' => 'Mạng xã hội', 'description' => 'Instagram URL'],
        ];

        foreach ($settings as $lang => $langSettings) {
            foreach ($langSettings as $setting) {
                Setting::updateOrCreate(
                    ['name' => $setting['name'], 'lang' => $lang],
                    $setting
                );
            }
        }
        
        foreach ($commonSettings as $setting) {
            Setting::updateOrCreate(
                ['name' => $setting['name'], 'lang' => 'vi'], // Assign to default lang
                $setting
            );
        }
    }
} 