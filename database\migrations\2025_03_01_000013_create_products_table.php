<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('sku')->nullable()->unique();
            $table->text('description')->nullable();
            $table->longText('content')->nullable();
            $table->decimal('price', 15, 2)->default(0);
            $table->decimal('sale_price', 15, 2)->nullable();
            $table->integer('stock')->default(0);
            $table->string('image_url')->nullable();
            $table->json('gallery')->nullable();
            $table->string('status', 20)->default('draft');
            $table->string('lang', 5)->default('vi');

            // Các trường mới được thêm vào
            $table->string('origin')->nullable()->comment('Xuất xứ');
            $table->string('warranty')->nullable()->comment('Bảo hành');
            $table->string('unit')->nullable()->comment('Đơn vị tính');
            $table->string('condition')->nullable()->comment('Tình trạng');
            
            $table->json('specifications')->nullable();
            $table->json('technical_specs')->nullable();
            $table->text('usage_instructions')->nullable();
            $table->json('downloads')->nullable();

            $table->foreignId('category_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('brand_id')->nullable()->constrained()->onDelete('set null');

            $table->string('meta_title')->nullable();
            
            // Thêm các trường đánh giá sản phẩm
            $table->decimal('average_rating', 3, 1)->default(0);
            $table->integer('reviews_count')->default(0);
            $table->json('rating_distribution')->nullable();
            $table->json('product_reviews')->nullable();
            
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_new')->default(false);
            $table->boolean('is_best_seller')->default(false);
            
            $table->timestamps();
        });

        // Tạo bảng khuyến mãi
        Schema::create('product_promotions', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('Tiêu đề khuyến mãi');
            $table->date('start_date')->comment('Ngày bắt đầu khuyến mãi');
            $table->date('end_date')->comment('Ngày kết thúc khuyến mãi');
            $table->json('promotion_items')->nullable()->comment('Danh sách các item khuyến mãi dạng JSON');
            $table->json('locations')->nullable()->comment('Các địa điểm áp dụng khuyến mãi');
            $table->boolean('is_active')->default(true)->comment('Trạng thái khuyến mãi');
            $table->timestamps();
        });
        
        // Bảng pivot để liên kết khuyến mãi với sản phẩm
        Schema::create('product_promotion_pivot', function (Blueprint $table) {
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_promotion_id')->constrained('product_promotions')->onDelete('cascade');
            $table->primary(['product_id', 'product_promotion_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_promotion_pivot');
        Schema::dropIfExists('product_promotions');
        Schema::dropIfExists('products');
    }
}; 