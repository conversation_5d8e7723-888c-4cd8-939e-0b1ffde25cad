<?php

namespace App\Observers;

use App\Models\MenuItem;

class MenuItemObserver
{
    /**
     * Handle the MenuItem "created" event.
     */
    public function created(MenuItem $menuItem): void
    {
        // Nếu không có order, đặt order là max + 1
        if (!$menuItem->order) {
            $maxOrder = MenuItem::where('menu_id', $menuItem->menu_id)
                ->where('parent_id', $menuItem->parent_id)
                ->max('order');
            
            $menuItem->order = $maxOrder ? $maxOrder + 1 : 1;
            $menuItem->saveQuietly();
        }
    }

    /**
     * Handle the MenuItem "updated" event.
     */
    public function updated(MenuItem $menuItem): void
    {
        // Nếu parent_id thay đổi, cập nhật order
        if ($menuItem->isDirty('parent_id')) {
            $maxOrder = MenuItem::where('menu_id', $menuItem->menu_id)
                ->where('parent_id', $menuItem->parent_id)
                ->max('order');
            
            $menuItem->order = $maxOrder ? $maxOrder + 1 : 1;
            $menuItem->saveQuietly();
        }
    }

    /**
     * Handle the MenuItem "deleted" event.
     */
    public function deleted(MenuItem $menuItem): void
    {
        // Xóa tất cả menu con khi xóa menu cha
        MenuItem::where('parent_id', $menuItem->id)->delete();
    }

    /**
     * Handle the MenuItem "restored" event.
     */
    public function restored(MenuItem $menuItem): void
    {
        //
    }

    /**
     * Handle the MenuItem "force deleted" event.
     */
    public function forceDeleted(MenuItem $menuItem): void
    {
        //
    }
}
