<?php

namespace App\Filament\Resources;

use App\Models\Post;
use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Filament\Resources\BaseResource;
use Illuminate\Support\Str;
use Filament\Forms\Set;
use FilamentTiptapEditor\TiptapEditor;
use FilamentTiptapEditor\Enums\TiptapOutput;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Resources\PostResource\Pages;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\Eloquent\Model;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Traits\HasLanguageSync;

class PostResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = Post::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Quản lý nội dung';
    protected static ?int $navigationGroupSort = 2; // Add this line for group sorting
    protected static ?string $navigationLabel = 'Bài viết';
    protected static ?int $navigationSort = 1;
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (Set $set, ?string $state) => 
                                $set('slug', Str::slug($state))
                            ),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->unique(ignoreRecord: true),
                        Forms\Components\Select::make('categories')
                            ->label('Danh mục')
                            ->relationship('categories', 'name')
                            ->multiple()
                            ->preload()
                            ->live()
                            ->required()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required(),
                                Forms\Components\TextInput::make('slug')
                                    ->required(),
                            ])
                            ->createOptionAction(
                                fn ($action) => $action->label('Tạo danh mục mới')
                            )
                            ->options(function () {
                                return \App\Models\Category::orderBy('name', 'asc')->pluck('name', 'id');
                            }),
                        
                        Forms\Components\Hidden::make('primary_category_id'),
                        Forms\Components\FileUpload::make('image')
                            ->image()
                            ->directory('posts')
                            ->columnSpanFull(),
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('excerpt')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'draft' => 'Draft',
                                'published' => 'Published',
                                'archived' => 'Archived',
                            ])
                            ->default('draft')
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                if ($state === 'published') {
                                    $set('published_at', now());
                                }
                            })
                            ->required(),
                        Forms\Components\Select::make('lang')
                            ->label('Ngôn ngữ')
                            ->options([
                                'vi' => 'Tiếng Việt',
                                'en' => 'English',
                            ])
                            ->default('vi')
                            ->required(),
                        Forms\Components\Select::make('trend')
                            ->label('Xu hướng')
                            ->options([
                                'hot' => 'Hot',
                                'featured' => 'Nổi bật',
                                'none' => 'Không',
                            ])
                            ->default('none')
                            ->required(),
                        Forms\Components\DateTimePicker::make('published_at')
                            ->visible(fn ($get) => $get('status') === 'published')
                            ->disabled(),
                    ])->columnSpan(['lg' => 2]),

                    Forms\Components\Section::make('Thông tin SEO')
                    ->schema([
                        Forms\Components\TextInput::make('seo_title')
                            ->label('Tiêu đề SEO'),
                        Forms\Components\Textarea::make('seo_description')
                            ->label('Mô tả SEO')
                            ->rows(3),
                        Forms\Components\TextInput::make('seo_keywords')
                            ->label('Từ khóa SEO'),
                    ])->columnSpan(['lg' => 1]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->disk('public')
                    ->square()
                    ->size(60)
                    ->visibility('public')
                    ->getStateUsing(function ($record) {
                        if ($record->image && Storage::disk('public')->exists($record->image)) {
                            return $record->image;
                        }
                        return null;
                    })
                    ->defaultImageUrl(asset(Config::get('filament-media.defaults.placeholder_image.url'))),
                    
                TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->formatStateUsing(function (Post $record) {
                        $html = '<div class="flex flex-col gap-1">';
                        $html .= '<span class="text-sm font-medium">' . e($record->title) . '</span>';
                        $html .= '<span class="text-xs text-gray-500">' . e($record->slug) . '</span>';
                        $html .= '</div>';
                        return $html;
                    })
                    ->html()
                    ->searchable()
                    ->sortable(),
                    
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'published' => 'success',
                        'draft' => 'warning',
                        'archived' => 'danger',
                        default => 'gray',
                    }),
                    
                TextColumn::make('trend')
                    ->label('Xu hướng')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'hot' => 'danger',
                        'featured' => 'warning',
                        'none' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'hot' => 'Hot',
                        'featured' => 'Nổi bật',
                        'none' => 'Không',
                        default => $state,
                    }),
                
                Tables\Columns\ViewColumn::make('categories')
                    ->label('Danh mục')
                    ->view('filament.tables.columns.categories-with-primary'),
                    
                Tables\Columns\TextColumn::make('info')
                    ->label('Thông tin')
                    ->state(function (Post $record): string {
                        return 'info';
                    })
                    ->formatStateUsing(function (string $state, Post $record): string {
                        return view('filament.tables.columns.post-info', [
                            'record' => $record,
                        ])->render();
                    })
                    ->html(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'published' => 'Published',
                        'archived' => 'Archived',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth('7xl'),
                Tables\Actions\EditAction::make()
                    ->modalWidth('7xl'),
                Tables\Actions\DeleteAction::make(),
                
                // Action ẩn để xử lý việc đặt danh mục chính
                Tables\Actions\Action::make('setPrimaryCategory')
                    ->label('Đặt danh mục chính')
                    ->icon('heroicon-o-star')
                    ->form([
                        Forms\Components\Hidden::make('category_id')
                            ->required(),
                    ])
                    ->action(function (Post $record, array $data) {
                        try {
                            $categoryId = $data['category_id'] ?? null;
                            
                            if (!$categoryId) {
                                Notification::make()
                                    ->title('Lỗi')
                                    ->body('Không tìm thấy danh mục.')
                                    ->danger()
                                    ->send();
                                    
                                return;
                            }
                            
                            // Kiểm tra xem category có thuộc về post không
                            $hasCategory = $record->categories()->where('category_id', $categoryId)->exists();
                            
                            if (!$hasCategory) {
                                Notification::make()
                                    ->title('Lỗi')
                                    ->body('Danh mục không thuộc về bài viết này.')
                                    ->danger()
                                    ->send();
                                    
                                return;
                            }
                            
                            // Đặt danh mục chính
                            \DB::table('post_category')
                                ->where('post_id', $record->id)
                                ->update(['is_primary' => false]);
                                
                            \DB::table('post_category')
                                ->where('post_id', $record->id)
                                ->where('category_id', $categoryId)
                                ->update(['is_primary' => true]);
                            
                            Notification::make()
                                ->title('Thành công')
                                ->body('Đã đặt danh mục chính.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Lỗi')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(false),
                
                Tables\Actions\Action::make('unsetPrimaryCategory')
                    ->label('Bỏ đặt danh mục chính')
                    ->icon('heroicon-o-x-mark')
                    ->action(function (Post $record) {
                        try {
                            // Reset tất cả danh mục thành không phải primary
                            \DB::table('post_category')
                                ->where('post_id', $record->id)
                                ->update(['is_primary' => false]);
                            
                            Notification::make()
                                ->title('Thành công')
                                ->body('Đã bỏ đặt danh mục chính.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Lỗi')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('copy')
                        ->label('Copy')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $newRecord = $record->replicate();
                                $newRecord->lang = $data['target_lang'];
                                $newRecord->slug = $record->slug . '-' . $data['target_lang'];
                                $newRecord->save();
                                
                                // Manually copy over the relationships
                                $newRecord->categories()->sync($record->categories->pluck('id')->toArray());
                            }

                            Notification::make()
                                ->title('Đã copy ' . count($records) . ' bản ghi')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Ngôn ngữ mục tiêu')
                                ->options([
                                    'en' => 'English',
                                    'vi' => 'Vietnamese',
                                ])
                                ->required(),
                        ]),
                ]),
            ])
            ->recordAction(null); // This disables row clicking;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
        ];
    }

    // Add methods to handle form data before save
    public static function mutateFormDataBeforeCreate(array $data): array
    {
        // Đặt author_id là user hiện tại nếu chưa được đặt
        $data['author_id'] = $data['author_id'] ?? auth()->id();
        
        return $data;
    }
    
    public static function mutateFormDataBeforeFill(array $data, Model $record): array
    {
        // Lấy danh mục chính (nếu có)
        $primaryCategoryId = $record->getPrimaryCategoryId();
        
        // Debug
        \Log::info('Mutate Form Data Before Fill', [
            'post_id' => $record->id,
            'primary_category_id' => $primaryCategoryId,
            'categories' => $record->categories()->pluck('category_id')->toArray()
        ]);
        
        if ($primaryCategoryId) {
            $data['primary_category_id'] = [$primaryCategoryId]; // Chuyển thành mảng cho CheckboxList
        }
        
        // Đảm bảo danh sách categories được lấy đúng
        $data['categories'] = $record->categories()->pluck('category_id')->toArray();
        
        return $data;
    }
    
    public static function afterCreate(Model $record, array $data): void
    {
        self::updateCategories($record, $data);
    }
    
    public static function afterUpdate(Model $record, array $data): void
    {
        self::updateCategories($record, $data);
    }
    
    protected static function updateCategories(Model $record, array $data): void
    {
        $categories = $data['categories'] ?? [];
        
        // Cập nhật danh mục
        if (!empty($categories)) {
            // Sync categories
            $record->categories()->sync($categories);
            
            // Không tự động đặt danh mục chính ở đây nữa
            // Người dùng sẽ đặt danh mục chính qua UI
        } else {
            // Xóa tất cả danh mục
            $record->categories()->detach();
        }
        
        // Đảm bảo dữ liệu được cập nhật trong database
        $record->refresh();
    }

    public static function mutateFormDataBeforeSave(array $data, Model $record = null): array
    {
        // Xử lý primary_category_id
        if (isset($data['primary_category_id']) && is_array($data['primary_category_id']) && !empty($data['primary_category_id'])) {
            $data['primary_category_id'] = reset($data['primary_category_id']); // Lấy phần tử đầu tiên của mảng
        }
        
        // Debug
        \Log::info('Mutate Form Data Before Save', [
            'post_id' => $record ? $record->id : 'new',
            'primary_category_id' => $data['primary_category_id'] ?? null,
            'primary_category_id_raw' => $data['primary_category_id'] ?? null,
            'categories' => $data['categories'] ?? []
        ]);
        
        return $data;
    }
}
