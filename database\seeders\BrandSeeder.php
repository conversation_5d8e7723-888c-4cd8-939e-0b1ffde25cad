<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Brand;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $brandsData = [
            [
                'name' => 'JBL',
                'description' => 'Thương hiệu âm thanh hàng đầu thế giới với hơn 75 năm kinh nghiệm trong lĩnh vực audio chuyên nghiệp và tiêu dùng',
                'logo' => 'partner-1.png'
            ],
            [
                'name' => 'Yamaha',
                'description' => 'Thương hiệu nổi tiếng về nhạc cụ và thiết bị âm thanh chuyên nghiệp với chất lượng vượt trội',
                'logo' => 'partner-2.png'
            ],
            [
                'name' => 'Shure',
                'description' => '<PERSON><PERSON><PERSON> sản xuất microphone và thiết bị âm thanh chuyên nghiệp được tin dùng bởi các nghệ sĩ hàng đầu',
                'logo' => 'partner-3.png'
            ],
            [
                'name' => 'Sennheiser',
                'description' => 'Thương hiệu tai nghe và micro cao cấp từ Đức với công nghệ âm thanh tiên tiến',
                'logo' => 'partner-4.png'
            ],
            [
                'name' => 'Bose',
                'description' => 'Thương hiệu Mỹ nổi tiếng với công nghệ chống ồn và hệ thống âm thanh chất lượng cao',
                'logo' => 'partner-5.png'
            ],
            [
                'name' => 'Audio-Technica',
                'description' => 'Thương hiệu Nhật Bản chuyên về tai nghe, micro và thiết bị DJ chuyên nghiệp',
                'logo' => 'partner-6.png'
            ],
            [
                'name' => 'Sony',
                'description' => 'Tập đoàn công nghệ Nhật Bản với dòng sản phẩm âm thanh đa dạng từ tai nghe đến hệ thống Hi-Fi',
                'logo' => 'partner-7.png'
            ],
            [
                'name' => 'TOA',
                'description' => 'Chuyên gia hệ thống âm thanh công cộng và thông báo với giải pháp toàn diện cho mọi không gian',
                'logo' => 'partner-8.png'
            ],
            [
                'name' => 'Beyerdynamic',
                'description' => 'Thương hiệu tai nghe cao cấp từ Đức với chất lượng âm thanh audiophile',
                'logo' => 'partner-9.png'
            ],
            [
                'name' => 'Focusrite',
                'description' => 'Nhà sản xuất audio interface và thiết bị thu âm chuyên nghiệp hàng đầu',
                'logo' => 'partner-10.png'
            ]
        ];

        foreach ($brandsData as $brandData) {
            $brand = Brand::firstOrCreate(
                ['name' => $brandData['name']],
                [
                    'slug' => Str::slug($brandData['name']),
                    'description' => $brandData['description'],
                    'status' => 'published'
                ]
            );

            if ($brand->wasRecentlyCreated || !$brand->hasMedia('logo')) {
                $imagePath = storage_path('app/public/images/' . $brandData['logo']);

                if (File::exists($imagePath)) {
                    $brand->addMedia($imagePath)
                          ->preservingOriginal()
                          ->toMediaCollection('logo');
                }
            }
        }
    }
}
