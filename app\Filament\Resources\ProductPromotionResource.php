<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductPromotionResource\Pages;
use App\Models\ProductPromotion;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;

class ProductPromotionResource extends Resource
{
    protected static ?string $model = ProductPromotion::class;
    protected static ?string $navigationIcon = 'heroicon-o-gift';
    protected static ?string $navigationGroup = 'Quản lý sản phẩm';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationLabel = 'Khuyến mãi sản phẩm';
    protected static ?string $recordTitleAttribute = 'title';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Thông tin khuyến mãi')
                    ->schema([
                        TextInput::make('title')
                            ->label('Tiêu đề khuyến mãi')
                            ->required()
                            ->maxLength(255),

                        DatePicker::make('start_date')
                            ->label('Ngày bắt đầu')
                            ->required()
                            ->beforeOrEqual('end_date'),

                        DatePicker::make('end_date')
                            ->label('Ngày kết thúc')
                            ->required()
                            ->afterOrEqual('start_date'),

                        Toggle::make('is_active')
                            ->label('Trạng thái kích hoạt')
                            ->default(true),
                    ])->columns(2),

                Section::make('Danh sách sản phẩm áp dụng')
                    ->schema([
                        Select::make('products')
                            ->label('Sản phẩm được áp dụng khuyến mãi')
                            ->multiple()
                            ->relationship('products', 'name')
                            ->searchable()
                            ->preload(),
                    ]),

                Section::make('Các khuyến mãi')
                    ->schema([
                        Repeater::make('promotion_items')
                            ->label('Danh sách khuyến mãi')
                            ->schema([
                                TextInput::make('item')
                                    ->label('Nội dung khuyến mãi')
                                    ->required()
                                    ->maxLength(255)
                            ])
                            ->defaultItems(1)
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['item'] ?? null),
                    ]),

                Section::make('Khu vực áp dụng')
                    ->schema([
                        Repeater::make('locations')
                            ->label('Khu vực áp dụng khuyến mãi')
                            ->schema([
                                TextInput::make('location')
                                    ->label('Tên khu vực')
                                    ->required()
                                    ->maxLength(255)
                            ])
                            ->defaultItems(1)
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['location'] ?? null),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('start_date')
                    ->label('Bắt đầu')
                    ->date('d/m/Y')
                    ->sortable(),

                TextColumn::make('end_date')
                    ->label('Kết thúc')
                    ->date('d/m/Y')
                    ->sortable(),

                TextColumn::make('products_count')
                    ->label('Sản phẩm')
                    ->counts('products')
                    ->sortable(),

                ToggleColumn::make('is_active')
                    ->label('Kích hoạt')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductPromotions::route('/'),
            'create' => Pages\CreateProductPromotion::route('/create'),
            'edit' => Pages\EditProductPromotion::route('/{record}/edit'),
        ];
    }
}
