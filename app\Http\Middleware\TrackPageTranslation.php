<?php

namespace App\Http\Middleware;

use App\Helpers\TranslationHelper;
use App\Models\StaticPage;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TrackPageTranslation
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track for successful responses
        if ($response->getStatusCode() !== 200) {
            return $response;
        }

        // Get the current route
        $route = $request->route();
        if (!$route) {
            return $response;
        }

        // Check if this is a static page route
        if ($route->getName() === 'link.detail') {
            $slug = $route->parameter('slug');
            
            // Find the static page
            $page = StaticPage::where('slug', $slug)
                ->where('lang', app()->getLocale())
                ->first();

            if ($page) {
                // Get the translation code and store in session
                $code = TranslationHelper::getCurrentPageCode($page->id, 'static_page');
                if ($code) {
                    session(['current_page_code' => $code]);
                }
            }
        }

        return $response;
    }
}
