<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Đảm bảo parent_id là null nếu không được chọn
        if (empty($data['parent_id'])) {
            $data['parent_id'] = null;
        }
        
        // Kiểm tra không chọn chính mình làm parent
        if ($data['parent_id'] == $this->record->id) {
            $data['parent_id'] = null;
            Notification::make()
                ->warning()
                ->title('Cảnh báo')
                ->body('<PERSON><PERSON> mục không thể chọn chính nó làm danh mục cha.')
                ->send();
        }
        
        return $data;
    }
} 