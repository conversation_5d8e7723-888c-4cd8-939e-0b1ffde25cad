<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('avatar')->nullable(); // Profile image path
            $table->string('phone')->nullable(); // Phone number
            $table->enum('status', ['active', 'inactive', 'banned'])->default('active'); // User status
            $table->enum('user_type', ['admin', 'distributor', 'customer'])->default('customer'); // User type
            $table->string('id_number')->nullable(); // CMND/CCCD
            $table->string('address')->nullable(); // Địa chỉ
            $table->string('bank_name')->nullable(); // Tên ngân hàng
            $table->string('bank_account')->nullable(); // Số tài khoản
            $table->string('bank_branch')->nullable(); // Chi nhánh
            $table->string('distributor_code')->nullable(); // Mã nhà phân phối giới thiệu
            $table->boolean('newsletter')->default(false); // Đăng ký nhận bản tin
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Xóa bảng posts trước vì nó có khóa ngoại đến bảng users
        if (Schema::hasTable('posts')) {
            Schema::dropIfExists('posts');
        }
        
        // Sau đó xóa các bảng khác
        Schema::dropIfExists('sessions');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('users');
    }
};
