<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use App\Services\LanguageFileService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;

class ListLanguages extends ListRecords
{
    protected static string $resource = LanguageResource::class;

    protected function getHeaderActions(): array
    {
        $service = new LanguageFileService();
        
        return [
            Actions\Action::make('create_file')
                ->label('Tạo file mới')
                ->icon('heroicon-o-plus')
                ->form([
                    \Filament\Forms\Components\Select::make('locale')
                        ->label('Ngôn ngữ')
                        ->options([
                            'vi' => 'Tiếng Việt',
                            'en' => 'English',
                        ])
                        ->required(),
                    
                    \Filament\Forms\Components\TextInput::make('filename')
                        ->label('Tên file')
                        ->required()
                        ->rules(['regex:/^[a-zA-Z_][a-zA-Z0-9_]*$/'])
                        ->helperText('Chỉ cho phép chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái hoặc dấu gạch dưới'),
                ])
                ->action(function (array $data) use ($service) {
                    $locale = $data['locale'];
                    $filename = $data['filename'];
                    
                    try {
                        $result = $service->createLanguageFile($locale, $filename);
                        
                        if ($result) {
                            Notification::make()
                                ->success()
                                ->title('Thành công')
                                ->body('Đã tạo file ngôn ngữ mới.')
                                ->send();
                        } else {
                            Notification::make()
                                ->danger()
                                ->title('Lỗi')
                                ->body('File đã tồn tại.')
                                ->send();
                        }
                    } catch (\InvalidArgumentException $e) {
                        Notification::make()
                            ->danger()
                            ->title('Lỗi')
                            ->body($e->getMessage())
                            ->send();
                    }
                }),
        ];
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50, 100];
    }

    public function getTitle(): string
    {
        return 'Quản lý file ngôn ngữ';
    }
} 