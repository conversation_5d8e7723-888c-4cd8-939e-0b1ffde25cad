<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\KeyValue;
use Illuminate\Support\Facades\File;

class ViewLanguage extends ViewRecord
{
    protected static string $resource = LanguageResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Thông tin file')
                    ->schema([
                        TextEntry::make('locale')
                            ->label('Ngôn ngữ')
                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                'vi' => 'Tiếng Việt',
                                'en' => 'English',
                                default => $state,
                            })
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'vi' => 'success',
                                'en' => 'info',
                                default => 'gray',
                            }),
                        
                        TextEntry::make('file')
                            ->label('Tên file'),
                        
                        TextEntry::make('keys_count')
                            ->label('Số lượng khóa')
                            ->formatStateUsing(fn ($state) => $state . ' khóa'),
                        
                        TextEntry::make('last_modified')
                            ->label('Cập nhật lần cuối')
                            ->dateTime('d/m/Y H:i:s'),
                    ])
                    ->columns(2),

                Section::make('Nội dung bản dịch')
                    ->schema([
                        KeyValue::make('translations')
                            ->label('Bản dịch')
                            ->keyLabel('Khóa')
                            ->valueLabel('Giá trị')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $record = $this->record;
        $locale = $record['locale'];
        $file = $record['file'];
        $filePath = lang_path("{$locale}/{$file}.php");
        
        if (File::exists($filePath)) {
            $translations = include $filePath;
            $data['translations'] = $translations;
        }
        
        return $data;
    }

    public function getTitle(): string
    {
        $record = $this->record;
        return "Xem file: {$record['file']} ({$record['locale']})";
    }
} 