<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('content');
            $table->text('excerpt');
            $table->enum('status', ['draft', 'published', 'archived']);
            $table->boolean('is_featured')->default(false);
            $table->string('seo_title')->nullable();
            $table->text('seo_description')->nullable();
            $table->string('seo_keywords')->nullable();
            $table->string('lang', 5)->default('vi');
            $table->timestamps();
            $table->timestamp('published_at')->nullable();
            $table->foreignId('author_id')->default(1)->constrained('users')->onDelete('set default');
            $table->string('image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Xóa bảng post_category trước vì nó có khóa ngoại đến posts
        Schema::dropIfExists('post_category');
        // Sau đó mới xóa bảng posts
        Schema::dropIfExists('posts');
    }
};
