<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'avatar' => null, // Let's default to null, can be set explicitly when needed
            'phone' => fake()->phoneNumber(),
            'status' => fake()->randomElement([
                'active', // Weighted to make 'active' more common
                'inactive',
                'banned',
            ]),
            'remember_token' => Str::random(10),
        ];
    }
    
    // Additional state method for active users only
    public function active(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'active',
            ];
        });
    }
    
    // Additional state method for users with avatars
    public function withAvatar(): self
    {
        return $this->state(function (array $attributes) {
            return [
                'avatar' => 'avatars/' . fake()->image('public/storage/avatars', 400, 400, null, false),
            ];
        });
    }
}