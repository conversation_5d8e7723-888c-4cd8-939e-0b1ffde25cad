<?php

namespace App\Traits;

use Filament\Tables\Filters\SelectFilter;
use App\Services\LanguageSyncService;

trait HasLanguageSync
{
    /**
     * Create a synchronized language filter (for filters array - deprecated)
     */
    public static function getLanguageFilter(): SelectFilter
    {
        return SelectFilter::make('lang')
            ->label('Ngôn ngữ')
            ->options([
                'vi' => 'Tiếng Việt',
                'en' => 'Tiếng Anh',
            ])
            ->placeholder('Tất cả')
            ->query(function ($query, $state) {
                if ($state) {
                    // Handle case where $state might be an array
                    $stateValue = is_array($state) ? (reset($state) ?: '') : $state;

                    if ($stateValue && is_string($stateValue)) {
                        // Sync locale when filter changes
                        LanguageSyncService::setFilamentLocale($stateValue);

                        return $query->where('lang', $stateValue);
                    }
                }
                return $query;
            })
            ->indicateUsing(function ($state) {
                if ($state) {
                    // Handle case where $state might be an array
                    $stateValue = is_array($state) ? (reset($state) ?: '') : $state;

                    if ($stateValue) {
                        $options = [
                            'vi' => 'Tiếng Việt',
                            'en' => 'Tiếng Anh',
                        ];
                        return 'Ngôn ngữ: ' . ($options[$stateValue] ?? $stateValue);
                    }
                }
                return null;
            });
    }

    /**
     * Add language filter to table header (outside filters)
     */
    public static function addLanguageFilterToHeader($table)
    {
        return $table->headerActions([
            \Filament\Tables\Actions\Action::make('language_filter')
                ->label('')
                ->icon('')
                ->view('livewire.table-language-filter')
                ->extraAttributes(['class' => 'language-filter-action'])
        ]);
    }

    /**
     * Apply language filter to table query
     */
    public static function applyLanguageFilter($table)
    {
        $currentLocale = LanguageSyncService::getCurrentLocale();

        return $table->modifyQueryUsing(function ($query) use ($currentLocale) {
            return $query->where('lang', $currentLocale);
        });
    }

    /**
     * Get table filters with default language filter
     */
    public static function getTableFiltersWithDefaults(): array
    {
        $currentLocale = LanguageSyncService::getCurrentLocale();

        // Set default filter state if not already set
        if (request()->has('tableFilters.lang')) {
            $filterLang = request()->input('tableFilters.lang');
            if ($filterLang && $filterLang !== $currentLocale) {
                LanguageSyncService::syncLocale($filterLang, 'filter');
            }
        } else {
            // Set default filter in URL if not present
            if (request()->isMethod('GET') && !request()->has('tableFilters')) {
                $queryParams = request()->query();
                $queryParams['tableFilters']['lang'] = $currentLocale;

                $newUrl = request()->url() . '?' . http_build_query($queryParams);
                return redirect()->to($newUrl);
            }
        }

        return [];
    }

    /**
     * Get the default language filter state
     */
    public static function getDefaultLanguageState(): ?string
    {
        return LanguageSyncService::getFilamentLocale();
    }
}
