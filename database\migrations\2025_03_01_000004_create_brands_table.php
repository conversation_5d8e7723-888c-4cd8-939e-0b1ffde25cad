<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('brands') && Schema::hasTable('categories')) {
            Schema::create('brands', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('image')->nullable();
                $table->string('status')->default('published');
                $table->string('lang', 5)->default('vi');
                $table->timestamps();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('brands');
    }
};