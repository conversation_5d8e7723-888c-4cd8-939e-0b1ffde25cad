<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class ProductReview extends Model
{
    use HasFactory;

    /**
     * <PERSON><PERSON><PERSON> thuộc tính được phép gán hàng loạt
     */
    protected $fillable = [
        'product_id',
        'user_id',
        'reviewer_name',
        'reviewer_email',
        'reviewer_phone',
        'rating',
        'comment',
        'admin_reply',
        'admin_reply_at',
        'admin_id',
        'recommend',
        'verified_purchase',
        'helpful_count',
        'status',
    ];

    /**
     * <PERSON><PERSON><PERSON> thuộc tính cần được ép kiểu
     */
    protected $casts = [
        'rating' => 'integer',
        'recommend' => 'boolean',
        'verified_purchase' => 'boolean',
        'helpful_count' => 'integer',
        'status' => 'boolean',
        'admin_reply_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * <PERSON>uan hệ với model Product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Quan hệ với model User (người đánh giá)
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Quan hệ với model User (admin phản hồi)
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * Scope chỉ lấy các đánh giá đã được phê duyệt
     */
    public function scopeApproved($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope để lọc theo sản phẩm
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope để lọc đánh giá theo rating
     */
    public function scopeWithRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope để lọc các đánh giá chưa có phản hồi từ admin
     */
    public function scopeWithoutAdminReply($query)
    {
        return $query->whereNull('admin_reply');
    }

    /**
     * Tăng số lượt đánh giá "hữu ích"
     */
    public function incrementHelpfulCount()
    {
        $this->increment('helpful_count');
        return $this;
    }

    /**
     * Thêm phản hồi từ admin
     */
    public function addAdminReply($reply)
    {
        $this->update([
            'admin_reply' => $reply,
            'admin_reply_at' => Carbon::now(),
        ]);
        
        // Cập nhật lại các thống kê đánh giá của sản phẩm
        $this->product->updateRatingStats();
        
        return $this;
    }

    /**
     * Cập nhật trạng thái hiển thị đánh giá
     */
    public function updateStatus($status)
    {
        $this->update(['status' => (bool)$status]);
        
        // Cập nhật lại các thống kê đánh giá của sản phẩm
        $this->product->updateRatingStats();
        
        return $this;
    }
    
    /**
     * Chuyển đổi datetime sang định dạng "time ago"
     */
    public function getTimeAgo()
    {
        return $this->created_at->diffForHumans();
    }
    
    /**
     * Lấy admin reply time ago
     */
    public function getAdminReplyTimeAgo()
    {
        return $this->admin_reply_at ? $this->admin_reply_at->diffForHumans() : null;
    }
}
