{"__meta": {"id": "01K0931X6XQAMJPMCY1N0MK5YC", "datetime": "2025-07-16 14:44:15", "utime": **********.069849, "method": "GET", "uri": "/admin/menus", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752651828.652882, "end": **********.069878, "duration": 26.416996002197266, "duration_str": "26.42s", "measures": [{"label": "Booting", "start": 1752651828.652882, "relative_start": 0, "end": **********.027356, "relative_end": **********.027356, "duration": 0.*****************, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.027366, "relative_start": 0.****************, "end": **********.06988, "relative_end": 1.9073486328125e-06, "duration": 26.***************, "duration_str": "26.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.165535, "relative_start": 0.****************, "end": **********.167891, "relative_end": **********.167891, "duration": 0.0023560523986816406, "duration_str": "2.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::resources.pages.list-records", "start": **********.976281, "relative_start": 3.****************, "end": **********.976281, "relative_end": **********.976281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.resources.tabs", "start": **********.285707, "relative_start": 3.****************, "end": **********.285707, "relative_end": **********.285707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd31e88145d24c6980a842fbcee446e7", "start": **********.988698, "relative_start": 12.335815906524658, "end": **********.988698, "relative_end": **********.988698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": 1752651841.194576, "relative_start": 12.541693925857544, "end": 1752651841.194576, "relative_end": 1752651841.194576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651841.197857, "relative_start": 12.54497480392456, "end": 1752651841.197857, "relative_end": 1752651841.197857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651841.199177, "relative_start": 12.546294927597046, "end": 1752651841.199177, "relative_end": 1752651841.199177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651841.209713, "relative_start": 12.556830883026123, "end": 1752651841.209713, "relative_end": 1752651841.209713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": 1752651841.527265, "relative_start": 12.874382972717285, "end": 1752651841.527265, "relative_end": 1752651841.527265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": 1752651841.715823, "relative_start": 13.062940835952759, "end": 1752651841.715823, "relative_end": 1752651841.715823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651841.717642, "relative_start": 13.064759969711304, "end": 1752651841.717642, "relative_end": 1752651841.717642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651841.71898, "relative_start": 13.066097974777222, "end": 1752651841.71898, "relative_end": 1752651841.71898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "start": 1752651841.720771, "relative_start": 13.067888975143433, "end": 1752651841.720771, "relative_end": 1752651841.720771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": 1752651841.721217, "relative_start": 13.068334817886353, "end": 1752651841.721217, "relative_end": 1752651841.721217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651841.722716, "relative_start": 13.069833993911743, "end": 1752651841.722716, "relative_end": 1752651841.722716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651841.733664, "relative_start": 13.080781936645508, "end": 1752651841.733664, "relative_end": 1752651841.733664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": 1752651841.734008, "relative_start": 13.081125974655151, "end": 1752651841.734008, "relative_end": 1752651841.734008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": 1752651841.734289, "relative_start": 13.081406831741333, "end": 1752651841.734289, "relative_end": 1752651841.734289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c8747f378d6678448e059688df7754e1", "start": 1752651841.736163, "relative_start": 13.083280801773071, "end": 1752651841.736163, "relative_end": 1752651841.736163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.table-language-filter", "start": 1752651841.801547, "relative_start": 13.148664951324463, "end": 1752651841.801547, "relative_end": 1752651841.801547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": 1752651842.304699, "relative_start": 13.651816844940186, "end": 1752651842.304699, "relative_end": 1752651842.304699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651842.30512, "relative_start": 13.652237892150879, "end": 1752651842.30512, "relative_end": 1752651842.30512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651842.306201, "relative_start": 13.653318881988525, "end": 1752651842.306201, "relative_end": 1752651842.306201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651842.306772, "relative_start": 13.653889894485474, "end": 1752651842.306772, "relative_end": 1752651842.306772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3ecca1ff40e5682e945502e1c847056", "start": 1752651843.556654, "relative_start": 14.903771877288818, "end": 1752651843.556654, "relative_end": 1752651843.556654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651843.855158, "relative_start": 15.20227599143982, "end": 1752651843.855158, "relative_end": 1752651843.855158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651843.857252, "relative_start": 15.20436978340149, "end": 1752651843.857252, "relative_end": 1752651843.857252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.badge", "start": 1752651843.869268, "relative_start": 15.216385841369629, "end": 1752651843.869268, "relative_end": 1752651843.869268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651845.445168, "relative_start": 16.792285919189453, "end": 1752651845.445168, "relative_end": 1752651845.445168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651845.448813, "relative_start": 16.795930862426758, "end": 1752651845.448813, "relative_end": 1752651845.448813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651845.449237, "relative_start": 16.79635500907898, "end": 1752651845.449237, "relative_end": 1752651845.449237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": 1752651846.845856, "relative_start": 18.192973852157593, "end": 1752651846.845856, "relative_end": 1752651846.845856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651846.922939, "relative_start": 18.27005696296692, "end": 1752651846.922939, "relative_end": 1752651846.922939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": 1752651846.926194, "relative_start": 18.273311853408813, "end": 1752651846.926194, "relative_end": 1752651846.926194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651848.15392, "relative_start": 19.50103783607483, "end": 1752651848.15392, "relative_end": 1752651848.15392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": 1752651848.154787, "relative_start": 19.50190496444702, "end": 1752651848.154787, "relative_end": 1752651848.154787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651848.15584, "relative_start": 19.502957820892334, "end": 1752651848.15584, "relative_end": 1752651848.15584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": 1752651848.156349, "relative_start": 19.503466844558716, "end": 1752651848.156349, "relative_end": 1752651848.156349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": 1752651848.156787, "relative_start": 19.503904819488525, "end": 1752651848.156787, "relative_end": 1752651848.156787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651848.667958, "relative_start": 20.01507592201233, "end": 1752651848.667958, "relative_end": 1752651848.667958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651848.695814, "relative_start": 20.04293179512024, "end": 1752651848.695814, "relative_end": 1752651848.695814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651848.69794, "relative_start": 20.045058012008667, "end": 1752651848.69794, "relative_end": 1752651848.69794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651848.86983, "relative_start": 20.21694779396057, "end": 1752651848.86983, "relative_end": 1752651848.86983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651849.369439, "relative_start": 20.716556787490845, "end": 1752651849.369439, "relative_end": 1752651849.369439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651849.381145, "relative_start": 20.728262901306152, "end": 1752651849.381145, "relative_end": 1752651849.381145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651849.388474, "relative_start": 20.735591888427734, "end": 1752651849.388474, "relative_end": 1752651849.388474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.260103, "relative_start": 22.607220888137817, "end": 1752651851.260103, "relative_end": 1752651851.260103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.418665, "relative_start": 22.765782833099365, "end": 1752651851.418665, "relative_end": 1752651851.418665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.603753, "relative_start": 22.950870990753174, "end": 1752651851.603753, "relative_end": 1752651851.603753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.605004, "relative_start": 22.95212197303772, "end": 1752651851.605004, "relative_end": 1752651851.605004, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.620354, "relative_start": 22.967471837997437, "end": 1752651851.620354, "relative_end": 1752651851.620354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.621404, "relative_start": 22.9685218334198, "end": 1752651851.621404, "relative_end": 1752651851.621404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.623062, "relative_start": 22.970179796218872, "end": 1752651851.623062, "relative_end": 1752651851.623062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.636311, "relative_start": 22.983428955078125, "end": 1752651851.636311, "relative_end": 1752651851.636311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.636984, "relative_start": 22.98410201072693, "end": 1752651851.636984, "relative_end": 1752651851.636984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.638555, "relative_start": 22.98567295074463, "end": 1752651851.638555, "relative_end": 1752651851.638555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651851.639156, "relative_start": 22.986274003982544, "end": 1752651851.639156, "relative_end": 1752651851.639156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651851.878394, "relative_start": 23.2255117893219, "end": 1752651851.878394, "relative_end": 1752651851.878394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.887512, "relative_start": 23.23462986946106, "end": 1752651851.887512, "relative_end": 1752651851.887512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.893916, "relative_start": 23.241033792495728, "end": 1752651851.893916, "relative_end": 1752651851.893916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.894369, "relative_start": 23.24148678779602, "end": 1752651851.894369, "relative_end": 1752651851.894369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.895495, "relative_start": 23.242612838745117, "end": 1752651851.895495, "relative_end": 1752651851.895495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.898645, "relative_start": 23.245762825012207, "end": 1752651851.898645, "relative_end": 1752651851.898645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.899212, "relative_start": 23.24632978439331, "end": 1752651851.899212, "relative_end": 1752651851.899212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.900292, "relative_start": 23.24740982055664, "end": 1752651851.900292, "relative_end": 1752651851.900292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.903279, "relative_start": 23.250396966934204, "end": 1752651851.903279, "relative_end": 1752651851.903279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.903827, "relative_start": 23.25094485282898, "end": 1752651851.903827, "relative_end": 1752651851.903827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.90521, "relative_start": 23.252327919006348, "end": 1752651851.90521, "relative_end": 1752651851.90521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651851.905853, "relative_start": 23.252970933914185, "end": 1752651851.905853, "relative_end": 1752651851.905853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651851.91055, "relative_start": 23.257668018341064, "end": 1752651851.91055, "relative_end": 1752651851.91055, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.920107, "relative_start": 23.26722478866577, "end": 1752651851.920107, "relative_end": 1752651851.920107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.926633, "relative_start": 23.27375078201294, "end": 1752651851.926633, "relative_end": 1752651851.926633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.927088, "relative_start": 23.274205923080444, "end": 1752651851.927088, "relative_end": 1752651851.927088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.928165, "relative_start": 23.275282859802246, "end": 1752651851.928165, "relative_end": 1752651851.928165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.931018, "relative_start": 23.278136014938354, "end": 1752651851.931018, "relative_end": 1752651851.931018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.931549, "relative_start": 23.278666973114014, "end": 1752651851.931549, "relative_end": 1752651851.931549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.933473, "relative_start": 23.280591011047363, "end": 1752651851.933473, "relative_end": 1752651851.933473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.936716, "relative_start": 23.283833980560303, "end": 1752651851.936716, "relative_end": 1752651851.936716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.937239, "relative_start": 23.284356832504272, "end": 1752651851.937239, "relative_end": 1752651851.937239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.938418, "relative_start": 23.28553581237793, "end": 1752651851.938418, "relative_end": 1752651851.938418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651851.938773, "relative_start": 23.285890817642212, "end": 1752651851.938773, "relative_end": 1752651851.938773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651851.943694, "relative_start": 23.290812015533447, "end": 1752651851.943694, "relative_end": 1752651851.943694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.955328, "relative_start": 23.302445888519287, "end": 1752651851.955328, "relative_end": 1752651851.955328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.96453, "relative_start": 23.31164789199829, "end": 1752651851.96453, "relative_end": 1752651851.96453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.96519, "relative_start": 23.312307834625244, "end": 1752651851.96519, "relative_end": 1752651851.96519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.966638, "relative_start": 23.313755989074707, "end": 1752651851.966638, "relative_end": 1752651851.966638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.971244, "relative_start": 23.31836199760437, "end": 1752651851.971244, "relative_end": 1752651851.971244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.971925, "relative_start": 23.319042921066284, "end": 1752651851.971925, "relative_end": 1752651851.971925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.973414, "relative_start": 23.320531845092773, "end": 1752651851.973414, "relative_end": 1752651851.973414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651851.976928, "relative_start": 23.32404589653015, "end": 1752651851.976928, "relative_end": 1752651851.976928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651851.977623, "relative_start": 23.324740886688232, "end": 1752651851.977623, "relative_end": 1752651851.977623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651851.979597, "relative_start": 23.326714992523193, "end": 1752651851.979597, "relative_end": 1752651851.979597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651851.980248, "relative_start": 23.32736587524414, "end": 1752651851.980248, "relative_end": 1752651851.980248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752651851.987166, "relative_start": 23.33428382873535, "end": 1752651851.987166, "relative_end": 1752651851.987166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651852.001962, "relative_start": 23.349079847335815, "end": 1752651852.001962, "relative_end": 1752651852.001962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651852.01204, "relative_start": 23.35915780067444, "end": 1752651852.01204, "relative_end": 1752651852.01204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651852.012841, "relative_start": 23.35995888710022, "end": 1752651852.012841, "relative_end": 1752651852.012841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651852.014454, "relative_start": 23.361571788787842, "end": 1752651852.014454, "relative_end": 1752651852.014454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651852.019068, "relative_start": 23.366185903549194, "end": 1752651852.019068, "relative_end": 1752651852.019068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651852.019773, "relative_start": 23.366890907287598, "end": 1752651852.019773, "relative_end": 1752651852.019773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651852.02123, "relative_start": 23.368347883224487, "end": 1752651852.02123, "relative_end": 1752651852.02123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::********************************", "start": 1752651852.025058, "relative_start": 23.372175931930542, "end": 1752651852.025058, "relative_end": 1752651852.025058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651852.026025, "relative_start": 23.373142957687378, "end": 1752651852.026025, "relative_end": 1752651852.026025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651852.027721, "relative_start": 23.374838829040527, "end": 1752651852.027721, "relative_end": 1752651852.027721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651852.028299, "relative_start": 23.37541699409485, "end": 1752651852.028299, "relative_end": 1752651852.028299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire::tailwind", "start": 1752651852.107265, "relative_start": 23.45438289642334, "end": 1752651852.107265, "relative_end": 1752651852.107265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.pagination.index", "start": 1752651852.335784, "relative_start": 23.682901859283447, "end": 1752651852.335784, "relative_end": 1752651852.335784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": 1752651854.054563, "relative_start": 25.401680946350098, "end": 1752651854.054563, "relative_end": 1752651854.054563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651854.055245, "relative_start": 25.402362823486328, "end": 1752651854.055245, "relative_end": 1752651854.055245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.select", "start": 1752651854.056581, "relative_start": 25.403698921203613, "end": 1752651854.056581, "relative_end": 1752651854.056581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651854.057017, "relative_start": 25.40413498878479, "end": 1752651854.057017, "relative_end": 1752651854.057017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651854.106854, "relative_start": 25.45397186279297, "end": 1752651854.106854, "relative_end": 1752651854.106854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651854.109461, "relative_start": 25.456578969955444, "end": 1752651854.109461, "relative_end": 1752651854.109461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651854.111033, "relative_start": 25.45815086364746, "end": 1752651854.111033, "relative_end": 1752651854.111033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651854.112576, "relative_start": 25.459693908691406, "end": 1752651854.112576, "relative_end": 1752651854.112576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651854.114345, "relative_start": 25.46146297454834, "end": 1752651854.114345, "relative_end": 1752651854.114345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": 1752651854.11614, "relative_start": 25.463257789611816, "end": 1752651854.11614, "relative_end": 1752651854.11614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": 1752651854.13876, "relative_start": 25.485877990722656, "end": 1752651854.13876, "relative_end": 1752651854.13876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.breadcrumbs", "start": 1752651854.139949, "relative_start": 25.487066984176636, "end": 1752651854.139949, "relative_end": 1752651854.139949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.473238, "relative_start": 25.820355892181396, "end": 1752651854.473238, "relative_end": 1752651854.473238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.48218, "relative_start": 25.82929801940918, "end": 1752651854.48218, "relative_end": 1752651854.48218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": 1752651854.490683, "relative_start": 25.837800979614258, "end": 1752651854.490683, "relative_end": 1752651854.490683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1752651854.678491, "relative_start": 26.025609016418457, "end": 1752651854.678491, "relative_end": 1752651854.678491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": 1752651854.907217, "relative_start": 26.254334926605225, "end": 1752651854.907217, "relative_end": 1752651854.907217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": 1752651854.90938, "relative_start": 26.256497859954834, "end": 1752651854.90938, "relative_end": 1752651854.90938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1752651854.927533, "relative_start": 26.274650812149048, "end": 1752651854.927533, "relative_end": 1752651854.927533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": 1752651854.928594, "relative_start": 26.27571201324463, "end": 1752651854.928594, "relative_end": 1752651854.928594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": 1752651854.943305, "relative_start": 26.290422916412354, "end": 1752651854.943305, "relative_end": 1752651854.943305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651854.945332, "relative_start": 26.292449951171875, "end": 1752651854.945332, "relative_end": 1752651854.945332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.946737, "relative_start": 26.29385495185852, "end": 1752651854.946737, "relative_end": 1752651854.946737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651854.948042, "relative_start": 26.295159816741943, "end": 1752651854.948042, "relative_end": 1752651854.948042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.949692, "relative_start": 26.296809911727905, "end": 1752651854.949692, "relative_end": 1752651854.949692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.index", "start": 1752651854.952187, "relative_start": 26.299304962158203, "end": 1752651854.952187, "relative_end": 1752651854.952187, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.field", "start": 1752651854.953016, "relative_start": 26.30013394355774, "end": 1752651854.953016, "relative_end": 1752651854.953016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": 1752651854.954486, "relative_start": 26.3016037940979, "end": 1752651854.954486, "relative_end": 1752651854.954486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651854.955031, "relative_start": 26.302148818969727, "end": 1752651854.955031, "relative_end": 1752651854.955031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.956394, "relative_start": 26.30351185798645, "end": 1752651854.956394, "relative_end": 1752651854.956394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651854.957005, "relative_start": 26.304122924804688, "end": 1752651854.957005, "relative_end": 1752651854.957005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fc6af43a80d1f8feb6964b2b41596895", "start": 1752651854.958469, "relative_start": 26.30558681488037, "end": 1752651854.958469, "relative_end": 1752651854.958469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": 1752651854.962871, "relative_start": 26.309988975524902, "end": 1752651854.962871, "relative_end": 1752651854.962871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": 1752651854.965644, "relative_start": 26.312761783599854, "end": 1752651854.965644, "relative_end": 1752651854.965644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": 1752651854.967833, "relative_start": 26.314950942993164, "end": 1752651854.967833, "relative_end": 1752651854.967833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": 1752651854.973538, "relative_start": 26.320655822753906, "end": 1752651854.973538, "relative_end": 1752651854.973538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": 1752651854.97477, "relative_start": 26.321887969970703, "end": 1752651854.97477, "relative_end": 1752651854.97477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.975823, "relative_start": 26.322940826416016, "end": 1752651854.975823, "relative_end": 1752651854.975823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": 1752651854.97666, "relative_start": 26.32377791404724, "end": 1752651854.97666, "relative_end": 1752651854.97666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651854.977693, "relative_start": 26.32481098175049, "end": 1752651854.977693, "relative_end": 1752651854.977693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.978464, "relative_start": 26.325581789016724, "end": 1752651854.978464, "relative_end": 1752651854.978464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651854.97949, "relative_start": 26.326607942581177, "end": 1752651854.97949, "relative_end": 1752651854.97949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.980025, "relative_start": 26.32714295387268, "end": 1752651854.980025, "relative_end": 1752651854.980025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651854.980636, "relative_start": 26.32775378227234, "end": 1752651854.980636, "relative_end": 1752651854.980636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.981202, "relative_start": 26.328319787979126, "end": 1752651854.981202, "relative_end": 1752651854.981202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": 1752651854.981899, "relative_start": 26.32901692390442, "end": 1752651854.981899, "relative_end": 1752651854.981899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": 1752651854.982301, "relative_start": 26.329418897628784, "end": 1752651854.982301, "relative_end": 1752651854.982301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.983744, "relative_start": 26.330861806869507, "end": 1752651854.983744, "relative_end": 1752651854.983744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": 1752651854.984522, "relative_start": 26.331640005111694, "end": 1752651854.984522, "relative_end": 1752651854.984522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": 1752651854.984783, "relative_start": 26.33190083503723, "end": 1752651854.984783, "relative_end": 1752651854.984783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": 1752651854.985663, "relative_start": 26.332780838012695, "end": 1752651854.985663, "relative_end": 1752651854.985663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": 1752651854.99279, "relative_start": 26.33990788459778, "end": 1752651854.99279, "relative_end": 1752651854.99279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651854.993984, "relative_start": 26.34110188484192, "end": 1752651854.993984, "relative_end": 1752651854.993984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651854.996352, "relative_start": 26.343469858169556, "end": 1752651854.996352, "relative_end": 1752651854.996352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651854.997773, "relative_start": 26.344890832901, "end": 1752651854.997773, "relative_end": 1752651854.997773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651854.998674, "relative_start": 26.345791816711426, "end": 1752651854.998674, "relative_end": 1752651854.998674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651854.999826, "relative_start": 26.346943855285645, "end": 1752651854.999826, "relative_end": 1752651854.999826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.000937, "relative_start": 26.348054885864258, "end": **********.000937, "relative_end": **********.000937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.001738, "relative_start": 26.34885597229004, "end": **********.001738, "relative_end": **********.001738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.002823, "relative_start": 26.34994101524353, "end": **********.002823, "relative_end": **********.002823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.004028, "relative_start": 26.35114598274231, "end": **********.004028, "relative_end": **********.004028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.005294, "relative_start": 26.35241198539734, "end": **********.005294, "relative_end": **********.005294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.006411, "relative_start": 26.35352897644043, "end": **********.006411, "relative_end": **********.006411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.006847, "relative_start": 26.353964805603027, "end": **********.006847, "relative_end": **********.006847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.007467, "relative_start": 26.35458493232727, "end": **********.007467, "relative_end": **********.007467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.007977, "relative_start": 26.35509490966797, "end": **********.007977, "relative_end": **********.007977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.008635, "relative_start": 26.35575294494629, "end": **********.008635, "relative_end": **********.008635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.009316, "relative_start": 26.356433868408203, "end": **********.009316, "relative_end": **********.009316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.010392, "relative_start": 26.35750985145569, "end": **********.010392, "relative_end": **********.010392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.01108, "relative_start": 26.358197927474976, "end": **********.01108, "relative_end": **********.01108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.011707, "relative_start": 26.358824968338013, "end": **********.011707, "relative_end": **********.011707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.012174, "relative_start": 26.359291791915894, "end": **********.012174, "relative_end": **********.012174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.012761, "relative_start": 26.35987901687622, "end": **********.012761, "relative_end": **********.012761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.013164, "relative_start": 26.360281944274902, "end": **********.013164, "relative_end": **********.013164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.014106, "relative_start": 26.361223936080933, "end": **********.014106, "relative_end": **********.014106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.015097, "relative_start": 26.36221480369568, "end": **********.015097, "relative_end": **********.015097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.015439, "relative_start": 26.36255693435669, "end": **********.015439, "relative_end": **********.015439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.016129, "relative_start": 26.36324691772461, "end": **********.016129, "relative_end": **********.016129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.016948, "relative_start": 26.364065885543823, "end": **********.016948, "relative_end": **********.016948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.017971, "relative_start": 26.365088939666748, "end": **********.017971, "relative_end": **********.017971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.019027, "relative_start": 26.36614489555359, "end": **********.019027, "relative_end": **********.019027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.019449, "relative_start": 26.3665668964386, "end": **********.019449, "relative_end": **********.019449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.020063, "relative_start": 26.367180824279785, "end": **********.020063, "relative_end": **********.020063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.020561, "relative_start": 26.36767888069153, "end": **********.020561, "relative_end": **********.020561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.021166, "relative_start": 26.368283987045288, "end": **********.021166, "relative_end": **********.021166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.021888, "relative_start": 26.369005918502808, "end": **********.021888, "relative_end": **********.021888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.022747, "relative_start": 26.36986494064331, "end": **********.022747, "relative_end": **********.022747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.023477, "relative_start": 26.37059497833252, "end": **********.023477, "relative_end": **********.023477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.024146, "relative_start": 26.37126398086548, "end": **********.024146, "relative_end": **********.024146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.025074, "relative_start": 26.372191905975342, "end": **********.025074, "relative_end": **********.025074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.026614, "relative_start": 26.37373185157776, "end": **********.026614, "relative_end": **********.026614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.027661, "relative_start": 26.374778985977173, "end": **********.027661, "relative_end": **********.027661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.027995, "relative_start": 26.375113010406494, "end": **********.027995, "relative_end": **********.027995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.028555, "relative_start": 26.375672817230225, "end": **********.028555, "relative_end": **********.028555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.02902, "relative_start": 26.37613797187805, "end": **********.02902, "relative_end": **********.02902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.029662, "relative_start": 26.376779794692993, "end": **********.029662, "relative_end": **********.029662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.030297, "relative_start": 26.37741494178772, "end": **********.030297, "relative_end": **********.030297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.03095, "relative_start": 26.37806797027588, "end": **********.03095, "relative_end": **********.03095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.031798, "relative_start": 26.378915786743164, "end": **********.031798, "relative_end": **********.031798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.033087, "relative_start": 26.380204916000366, "end": **********.033087, "relative_end": **********.033087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.034425, "relative_start": 26.381542921066284, "end": **********.034425, "relative_end": **********.034425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.034951, "relative_start": 26.382068872451782, "end": **********.034951, "relative_end": **********.034951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.035598, "relative_start": 26.382715940475464, "end": **********.035598, "relative_end": **********.035598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.036439, "relative_start": 26.383556842803955, "end": **********.036439, "relative_end": **********.036439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.037268, "relative_start": 26.38438582420349, "end": **********.037268, "relative_end": **********.037268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.038042, "relative_start": 26.385159969329834, "end": **********.038042, "relative_end": **********.038042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.039229, "relative_start": 26.3863468170166, "end": **********.039229, "relative_end": **********.039229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.040429, "relative_start": 26.3875470161438, "end": **********.040429, "relative_end": **********.040429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.040832, "relative_start": 26.38794994354248, "end": **********.040832, "relative_end": **********.040832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.041429, "relative_start": 26.38854694366455, "end": **********.041429, "relative_end": **********.041429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.041724, "relative_start": 26.3888418674469, "end": **********.041724, "relative_end": **********.041724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.042538, "relative_start": 26.389655828475952, "end": **********.042538, "relative_end": **********.042538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.043297, "relative_start": 26.39041495323181, "end": **********.043297, "relative_end": **********.043297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.044554, "relative_start": 26.391671895980835, "end": **********.044554, "relative_end": **********.044554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.045801, "relative_start": 26.392918825149536, "end": **********.045801, "relative_end": **********.045801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.046314, "relative_start": 26.393431901931763, "end": **********.046314, "relative_end": **********.046314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.047157, "relative_start": 26.394274950027466, "end": **********.047157, "relative_end": **********.047157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.047752, "relative_start": 26.394869804382324, "end": **********.047752, "relative_end": **********.047752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.048361, "relative_start": 26.39547896385193, "end": **********.048361, "relative_end": **********.048361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.049133, "relative_start": 26.39625096321106, "end": **********.049133, "relative_end": **********.049133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.050022, "relative_start": 26.39713978767395, "end": **********.050022, "relative_end": **********.050022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27df4909b5b8fbf02f3a65ba2f758414", "start": **********.052069, "relative_start": 26.399186849594116, "end": **********.052069, "relative_end": **********.052069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.059575, "relative_start": 26.40669298171997, "end": **********.059575, "relative_end": **********.059575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97c5322e947dbf60ae5ca480928dd519", "start": **********.06064, "relative_start": 26.407757997512817, "end": **********.06064, "relative_end": **********.06064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.065147, "relative_start": 26.412264823913574, "end": **********.06541, "relative_end": **********.06541, "duration": 0.00026297569274902344, "duration_str": "263μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.067731, "relative_start": 26.414848804473877, "end": **********.067835, "relative_end": **********.067835, "duration": 0.00010418891906738281, "duration_str": "104μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 52286856, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 230, "nb_templates": 230, "templates": [{"name": "1x filament-panels::resources.pages.list-records", "param_count": null, "params": [], "start": **********.97626, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/resources/pages/list-records.blade.phpfilament-panels::resources.pages.list-records", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fresources%2Fpages%2Flist-records.blade.php&line=1", "ajax": false, "filename": "list-records.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::resources.pages.list-records"}, {"name": "1x filament-panels::components.resources.tabs", "param_count": null, "params": [], "start": **********.285682, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/resources/tabs.blade.phpfilament-panels::components.resources.tabs", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fresources%2Ftabs.blade.php&line=1", "ajax": false, "filename": "tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.resources.tabs"}, {"name": "1x __components::bd31e88145d24c6980a842fbcee446e7", "param_count": null, "params": [], "start": **********.988652, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/bd31e88145d24c6980a842fbcee446e7.blade.php__components::bd31e88145d24c6980a842fbcee446e7", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fbd31e88145d24c6980a842fbcee446e7.blade.php&line=1", "ajax": false, "filename": "bd31e88145d24c6980a842fbcee446e7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd31e88145d24c6980a842fbcee446e7"}, {"name": "2x filament::components.button.index", "param_count": null, "params": [], "start": 1752651841.19455, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.button.index"}, {"name": "12x filament::components.icon-button", "param_count": null, "params": [], "start": 1752651841.197835, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 12, "name_original": "filament::components.icon-button"}, {"name": "67x filament::components.icon", "param_count": null, "params": [], "start": 1752651841.199158, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 67, "name_original": "filament::components.icon"}, {"name": "2x __components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "param_count": null, "params": [], "start": 1752651841.527246, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php__components::9acc8bd1c7bfbc0566f6e7ce6a92f873", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php&line=1", "ajax": false, "filename": "9acc8bd1c7bfbc0566f6e7ce6a92f873.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9acc8bd1c7bfbc0566f6e7ce6a92f873"}, {"name": "3x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": 1752651841.715802, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.item"}, {"name": "12x filament::components.loading-indicator", "param_count": null, "params": [], "start": 1752651841.718961, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 12, "name_original": "filament::components.loading-indicator"}, {"name": "3x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": 1752651841.733994, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.list.index"}, {"name": "3x filament::components.dropdown.index", "param_count": null, "params": [], "start": 1752651841.734276, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.dropdown.index"}, {"name": "1x __components::c8747f378d6678448e059688df7754e1", "param_count": null, "params": [], "start": 1752651841.736139, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/c8747f378d6678448e059688df7754e1.blade.php__components::c8747f378d6678448e059688df7754e1", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fc8747f378d6678448e059688df7754e1.blade.php&line=1", "ajax": false, "filename": "c8747f378d6678448e059688df7754e1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c8747f378d6678448e059688df7754e1"}, {"name": "1x livewire.table-language-filter", "param_count": null, "params": [], "start": 1752651841.801519, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/table-language-filter.blade.phplivewire.table-language-filter", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Ftable-language-filter.blade.php&line=1", "ajax": false, "filename": "table-language-filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.table-language-filter"}, {"name": "2x filament::components.input.index", "param_count": null, "params": [], "start": 1752651842.304679, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.input.index"}, {"name": "5x filament::components.input.wrapper", "param_count": null, "params": [], "start": 1752651842.305106, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.input.wrapper"}, {"name": "1x __components::b3ecca1ff40e5682e945502e1c847056", "param_count": null, "params": [], "start": 1752651843.556631, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/b3ecca1ff40e5682e945502e1c847056.blade.php__components::b3ecca1ff40e5682e945502e1c847056", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fb3ecca1ff40e5682e945502e1c847056.blade.php&line=1", "ajax": false, "filename": "b3ecca1ff40e5682e945502e1c847056.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3ecca1ff40e5682e945502e1c847056"}, {"name": "1x filament::components.badge", "param_count": null, "params": [], "start": 1752651843.869249, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/badge.blade.phpfilament::components.badge", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.badge"}, {"name": "18x filament::components.link", "param_count": null, "params": [], "start": 1752651845.445148, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 18, "name_original": "filament::components.link"}, {"name": "3x filament::components.input.select", "param_count": null, "params": [], "start": 1752651846.845828, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/select.blade.phpfilament::components.input.select", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.input.select"}, {"name": "1x __components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": 1752651846.926153, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7efa8d8730e6e64b895c482f47ff6151"}, {"name": "2x filament::components.grid.column", "param_count": null, "params": [], "start": 1752651848.153901, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.grid.column"}, {"name": "2x filament::components.grid.index", "param_count": null, "params": [], "start": 1752651848.15476, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.grid.index"}, {"name": "6x filament::components.input.checkbox", "param_count": null, "params": [], "start": 1752651848.869801, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/checkbox.blade.phpfilament::components.input.checkbox", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 6, "name_original": "filament::components.input.checkbox"}, {"name": "15x __components::********************************", "param_count": null, "params": [], "start": 1752651851.418643, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/********************************.blade.php__components::********************************", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F********************************.blade.php&line=1", "ajax": false, "filename": "********************************.blade.php", "line": "?"}, "render_count": 15, "name_original": "__components::********************************"}, {"name": "1x livewire::tailwind", "param_count": null, "params": [], "start": 1752651852.10724, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Features\\SupportPagination/views/tailwind.blade.phplivewire::tailwind", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportPagination%2Fviews%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire::tailwind"}, {"name": "1x filament::components.pagination.index", "param_count": null, "params": [], "start": 1752651852.335758, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/pagination/index.blade.phpfilament::components.pagination.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fpagination%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.pagination.index"}, {"name": "5x filament::components.modal.index", "param_count": null, "params": [], "start": 1752651854.106823, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": 1752651854.11612, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": 1752651854.138734, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.breadcrumbs", "param_count": null, "params": [], "start": 1752651854.13993, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/breadcrumbs.blade.phpfilament::components.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.breadcrumbs"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": 1752651854.490659, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "1x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1752651854.678474, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": 1752651854.909362, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1752651854.927505, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": 1752651854.928564, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": 1752651854.943287, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x filament-panels::components.global-search.index", "param_count": null, "params": [], "start": 1752651854.952168, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/index.blade.phpfilament-panels::components.global-search.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.index"}, {"name": "1x filament-panels::components.global-search.field", "param_count": null, "params": [], "start": 1752651854.952988, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/field.blade.phpfilament-panels::components.global-search.field", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.field"}, {"name": "1x __components::fc6af43a80d1f8feb6964b2b41596895", "param_count": null, "params": [], "start": 1752651854.958452, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/fc6af43a80d1f8feb6964b2b41596895.blade.php__components::fc6af43a80d1f8feb6964b2b41596895", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Ffc6af43a80d1f8feb6964b2b41596895.blade.php&line=1", "ajax": false, "filename": "fc6af43a80d1f8feb6964b2b41596895.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fc6af43a80d1f8feb6964b2b41596895"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": 1752651854.962851, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": 1752651854.965627, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": 1752651854.967814, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": 1752651854.973521, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": 1752651854.974748, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": 1752651854.976639, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": 1752651854.977674, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": 1752651854.985644, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.logo", "param_count": null, "params": [], "start": 1752651854.992768, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.logo"}, {"name": "9x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": 1752651854.993965, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 9, "name_original": "filament-panels::components.sidebar.group"}, {"name": "21x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": 1752651854.99633, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 21, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.049116, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.050007, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::27df4909b5b8fbf02f3a65ba2f758414", "param_count": null, "params": [], "start": **********.052044, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/27df4909b5b8fbf02f3a65ba2f758414.blade.php__components::27df4909b5b8fbf02f3a65ba2f758414", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F27df4909b5b8fbf02f3a65ba2f758414.blade.php&line=1", "ajax": false, "filename": "27df4909b5b8fbf02f3a65ba2f758414.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27df4909b5b8fbf02f3a65ba2f758414"}, {"name": "1x __components::97c5322e947dbf60ae5ca480928dd519", "param_count": null, "params": [], "start": **********.060617, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/97c5322e947dbf60ae5ca480928dd519.blade.php__components::97c5322e947dbf60ae5ca480928dd519", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F97c5322e947dbf60ae5ca480928dd519.blade.php&line=1", "ajax": false, "filename": "97c5322e947dbf60ae5ca480928dd519.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97c5322e947dbf60ae5ca480928dd519"}]}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011040000000000001, "accumulated_duration_str": "11.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.179704, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 15.67}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/BaseResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\BaseResource.php", "line": 14}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/CanAuthorizeResourceAccess.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\CanAuthorizeResourceAccess.php", "line": 14}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/CanAuthorizeResourceAccess.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\CanAuthorizeResourceAccess.php", "line": 9}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.1963272, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseResource.php:14", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/BaseResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\BaseResource.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FBaseResource.php&line=14", "ajax": false, "filename": "BaseResource.php", "line": "14"}, "connection": "auvista", "explain": null, "start_percent": 15.67, "width_percent": 18.297}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.256139, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 33.967, "width_percent": 7.518}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.259964, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 41.486, "width_percent": 12.319}, {"sql": "select count(*) as aggregate from `menus` where `lang` = 'vi'", "type": "query", "params": [], "bindings": ["vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1752651839.827668, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "auvista", "explain": null, "start_percent": 53.804, "width_percent": 9.601}, {"sql": "select `menus`.*, (select count(*) from `menu_items` where `menus`.`id` = `menu_items`.`menu_id` and `parent_id` is null) as `items_count` from `menus` where `lang` = 'vi' order by `menus`.`id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": ["vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": 1752651839.861219, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "auvista", "explain": null, "start_percent": 63.406, "width_percent": 19.203}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = 'new'", "type": "query", "params": [], "bindings": ["new"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1752651854.933878, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ContactFormResource.php:225", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FContactFormResource.php&line=225", "ajax": false, "filename": "ContactFormResource.php", "line": "225"}, "connection": "auvista", "explain": null, "start_percent": 82.609, "width_percent": 8.333}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = 'new'", "type": "query", "params": [], "bindings": ["new"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1752651854.9881542, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ContactFormResource.php:225", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FContactFormResource.php&line=225", "ajax": false, "filename": "ContactFormResource.php", "line": "225"}, "connection": "auvista", "explain": null, "start_percent": 90.942, "width_percent": 9.058}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Menu": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 96, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-resource.pages.list-menus #HBzjqr9c1PVDONSrQvJg": "array:4 [\n  \"data\" => array:39 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-resource.pages.list-menus\"\n  \"component\" => \"App\\Filament\\Resources\\MenuResource\\Pages\\ListMenus\"\n  \"id\" => \"HBzjqr9c1PVDONSrQvJg\"\n]", "table-language-filter #aD8gZKgZKsToriPT8d4A": "array:4 [\n  \"data\" => array:2 [\n    \"currentLocale\" => \"vi\"\n    \"tableName\" => \"default\"\n  ]\n  \"name\" => \"table-language-filter\"\n  \"component\" => \"App\\Livewire\\TableLanguageFilter\"\n  \"id\" => \"aD8gZKgZKsToriPT8d4A\"\n]", "filament.livewire.global-search #IqHZZIPBQinBL7ZGV6MR": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"IqHZZIPBQinBL7ZGV6MR\"\n]", "language-switcher #tncowXSd9p9mWRJ7168m": "array:4 [\n  \"data\" => []\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"tncowXSd9p9mWRJ7168m\"\n]", "filament.livewire.notifications #HlIDiYeN8mgvxvxZlfdZ": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2629\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"HlIDiYeN8mgvxvxZlfdZ\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/admin/menus", "action_name": "filament.admin.resources.menus.index", "controller_action": "App\\Filament\\Resources\\MenuResource\\Pages\\ListMenus", "uri": "GET admin/menus", "controller": "App\\Filament\\Resources\\MenuResource\\Pages\\ListMenus@render<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/menus", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\LogLogoutMiddleware, App\\Http\\Middleware\\LocaleMiddleware, Filament\\Http\\Middleware\\Authenticate", "duration": "26.42s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlFodU55emNwQ0liTmhaTDg1MzdwWFE9PSIsInZhbHVlIjoibTVRNU5UaEZuNU8ySkZpRG9iaCtRUXdSRXp0dzNBVlVGc1IvZ081VXFBcE5sYk4ybWUrcnRXY2lRY3EzTk5OZ0RwQ3hLYkNUN0xxamN1SVZNMHhacWZralF0UksxdnhGQTU0cVBGTXhTcEZ2alV3UmpmMStHL2FPWXAxLzBtQWUiLCJtYWMiOiJiNGEzMDI0YjBjYThjYmUyMGIxZjI2NDFlNmRlNjQ5YzQ0YzQ2YTAwOGEzZDkwMjg4NWQ2M2UwOWQ4YWZhM2Y0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjY3OVJFQ3M0QkNMOGhoT2NNbmh6SGc9PSIsInZhbHVlIjoiUndjR0RMeHFQOGdFckhrSHA4eTAwTjg4NDEwSG1rRWQ4NXgrOHFDUHQwcWpYSHptaXpET3kyak9WVjRxWTJDUUtYdC84UDJvaWJTRElPeHpMYlBCL3p6dllaQ3AwdDVmOVRMUFBmNVJIdm5tNXlOT2RWVW1zR3JZOTI4Y1pWSGQiLCJtYWMiOiIwOTIyZWMyYTViMTQ2ZGFkNjRkNmE4NWJkNDhiMzE3YjMyNzUwNzVjMWE5ZWE3ZjE1YmJiNWI1ODJhOGU2YTQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxYEiPXdkNcyfymjyeN8wk4RbgrpbsugedMWghRH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:44:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InB6bFBoeFlLNDBTWnpkdEswcU44Q3c9PSIsInZhbHVlIjoiSFB4cUpUaENxdHpwN1V4MzZkSTVnVkE5cTd6VVV6ZUhYLzR6TGh4a0U5dkN1eGhsOFk1dndadTdVVzl6NHkwRkVkQWNMSEV1MW03RXB6ZFlOaWxpd0RYeGlRZG93VFQ0Q21ucWJkSW1hRTFpNHVhbHEzYWN5Y0RWNG1wQm4vSjIiLCJtYWMiOiJhZTQ5ZTJiMWQzMzIzOWIwZWNjMzA0MDQxZGJmMTc5NzYxNjIxYzQxZDJjMjZiNWExNTE1MjAwYWI0MTg1ZmIzIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 09:44:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IkMrL0xpRit4dXJKV3ZQcXRjVjJ4Ync9PSIsInZhbHVlIjoiNjRNdkRZbXBUUDVqTkFKZ254WVVDSVd5clgyaG9Jam9vaWFoRDh2SHR2WjFoa2hkaHpLdG16MHV3TlRucXdUY1Y0SmhkQUlacDAzQ0JIZkpYaEZ1bTl0VEIxWHQ0VEdNVTZ1NXl3a04yY1RCcVl1b1V5YXdzOGJqT0xrRlErREUiLCJtYWMiOiIzZjIwZTYwMjgzYmE4ZTQyY2I1MWM2Y2UyNjk1YjQ5YmQ1MjdmYzQ1OTEwMDBjM2YxMTEzZmVkM2E0Yjg2ZDZkIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 09:44:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InB6bFBoeFlLNDBTWnpkdEswcU44Q3c9PSIsInZhbHVlIjoiSFB4cUpUaENxdHpwN1V4MzZkSTVnVkE5cTd6VVV6ZUhYLzR6TGh4a0U5dkN1eGhsOFk1dndadTdVVzl6NHkwRkVkQWNMSEV1MW03RXB6ZFlOaWxpd0RYeGlRZG93VFQ0Q21ucWJkSW1hRTFpNHVhbHEzYWN5Y0RWNG1wQm4vSjIiLCJtYWMiOiJhZTQ5ZTJiMWQzMzIzOWIwZWNjMzA0MDQxZGJmMTc5NzYxNjIxYzQxZDJjMjZiNWExNTE1MjAwYWI0MTg1ZmIzIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 09:44:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IkMrL0xpRit4dXJKV3ZQcXRjVjJ4Ync9PSIsInZhbHVlIjoiNjRNdkRZbXBUUDVqTkFKZ254WVVDSVd5clgyaG9Jam9vaWFoRDh2SHR2WjFoa2hkaHpLdG16MHV3TlRucXdUY1Y0SmhkQUlacDAzQ0JIZkpYaEZ1bTl0VEIxWHQ0VEdNVTZ1NXl3a04yY1RCcVl1b1V5YXdzOGJqT0xrRlErREUiLCJtYWMiOiIzZjIwZTYwMjgzYmE4ZTQyY2I1MWM2Y2UyNjk1YjQ5YmQ1MjdmYzQ1OTEwMDBjM2YxMTEzZmVkM2E0Yjg2ZDZkIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 09:44:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-494535743 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$GgKaDmjdfWlNzTUrcO1K5.P1hXVA9J4EX3bYW.ogCY4SrBpQeXIn2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494535743\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/admin/menus", "action_name": "filament.admin.resources.menus.index", "controller_action": "App\\Filament\\Resources\\MenuResource\\Pages\\ListMenus"}, "badge": null}}