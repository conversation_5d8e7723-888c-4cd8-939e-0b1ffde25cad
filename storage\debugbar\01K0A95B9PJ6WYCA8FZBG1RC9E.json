{"__meta": {"id": "01K0A95B9PJ6WYCA8FZBG1RC9E", "datetime": "2025-07-17 01:50:13", "utime": **********.686798, "method": "GET", "uri": "/auvista/public/test-menu", "ip": "::1"}, "messages": {"count": 3, "messages": [{"message": "[01:50:13] LOG.info: Route /{slug} đ<PERSON><PERSON>c gọi với slug: test-menu", "message_html": null, "is_string": false, "label": "info", "time": **********.58631, "xdebug_link": null, "collector": "log"}, {"message": "[01:50:13] LOG.info: Kiểm tra Post với slug: test-menu - <PERSON><PERSON><PERSON>ng tìm thấy", "message_html": null, "is_string": false, "label": "info", "time": **********.597732, "xdebug_link": null, "collector": "log"}, {"message": "[01:50:13] LOG.info: <PERSON><PERSON><PERSON><PERSON> tìm thấy gì, redirect về home", "message_html": null, "is_string": false, "label": "info", "time": **********.602204, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752691812.940909, "end": **********.686841, "duration": 0.7459321022033691, "duration_str": "746ms", "measures": [{"label": "Booting", "start": 1752691812.940909, "relative_start": 0, "end": **********.372385, "relative_end": **********.372385, "duration": 0.****************, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.372402, "relative_start": 0.*****************, "end": **********.686844, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.56999, "relative_start": 0.****************, "end": **********.574447, "relative_end": **********.574447, "duration": 0.004456996917724609, "duration_str": "4.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.682677, "relative_start": 0.****************, "end": **********.68379, "relative_end": **********.68379, "duration": 0.0011129379272460938, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.007590000000000001, "accumulated_duration_str": "7.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `static_pages` where `slug` = 'test-menu' limit 1", "type": "query", "params": [], "bindings": ["test-menu"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 299}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.5876849, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "web.php:299", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 299}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=299", "ajax": false, "filename": "web.php", "line": "299"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 44.401}, {"sql": "select * from `posts` where `slug` = 'test-menu' and `status` = 'published' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["test-menu", "published", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 310}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.594759, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "web.php:310", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 310}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=310", "ajax": false, "filename": "web.php", "line": "310"}, "connection": "auvista", "explain": null, "start_percent": 44.401, "width_percent": 23.847}, {"sql": "select * from `categories` where `slug` = 'test-menu' limit 1", "type": "query", "params": [], "bindings": ["test-menu"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 334}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\CallableDispatcher.php", "line": 40}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 244}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.5988781, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "web.php:334", "source": {"index": 16, "namespace": null, "name": "routes/web.php", "file": "H:\\laragon\\www\\auvista\\routes\\web.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=334", "ajax": false, "filename": "web.php", "line": "334"}, "connection": "auvista", "explain": null, "start_percent": 68.248, "width_percent": 31.752}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost/auvista/public/test-menu", "action_name": "static.page", "controller_action": "Closure", "uri": "GET {slug}", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Froutes%2Fweb.php&line=295\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">routes/web.php:295-391</a>", "middleware": "web", "duration": "721ms", "peak_memory": "44MB", "response": "Redirect to https://localhost/auvista/public", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-157957989 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-157957989\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-125767259 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-125767259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-853971419 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">curl/8.12.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853971419\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-769179802 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-769179802\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-941266986 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 18:50:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://localhost/auvista/public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941266986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1017939272 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JRIryUdprk3gBjwhQpKxiAvd5yViO3N30I18vONx</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1017939272\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost/auvista/public/test-menu", "action_name": "static.page", "controller_action": "Closure"}, "badge": "302 Found"}}