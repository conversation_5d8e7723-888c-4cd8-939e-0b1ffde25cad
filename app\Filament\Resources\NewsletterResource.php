<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NewsletterResource\Pages;
use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class NewsletterResource extends Resource
{
    protected static ?string $model = Contact::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?string $navigationLabel = 'Đăng ký nhận tin';
    
    protected static ?string $modelLabel = 'đăng ký nhận tin';
    
    protected static ?string $pluralModelLabel = 'đăng ký nhận tin';
    
    protected static ?string $navigationGroup = 'Liên hệ & Nhận tin';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('is_newsletter', true);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên')
                    ->required(),
                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required(),
                Forms\Components\TextInput::make('phone')
                    ->label('Số điện thoại')
                    ->tel(),
                Forms\Components\Toggle::make('is_newsletter')
                    ->label('Đăng ký nhận tin')
                    ->default(true)
                    ->required(),
                Forms\Components\DateTimePicker::make('created_at')
                    ->label('Ngày đăng ký')
                    ->disabled()
                    ->dehydrated(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('Số điện thoại')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_newsletter')
                    ->label('Đăng ký nhận tin')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày đăng ký')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\Filter::make('is_newsletter')
                    ->label('Chỉ hiển thị đăng ký nhận tin')
                    ->query(fn (Builder $query): Builder => $query->where('is_newsletter', true))
                    ->default(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNewsletters::route('/'),
            'edit' => Pages\EditNewsletter::route('/{record}/edit'),
        ];
    }
} 