<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\StaticPage;
use App\Models\Post;
use App\Models\Category;
use Illuminate\Support\Facades\Log;
use App\Traits\SeoTrait;
use App\Models\Brand;
use Illuminate\Support\Facades\App;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\ProductController;
use App\Models\Product;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;

use App\Helpers\TranslationHelper;

class StaticPageController extends BaseController
{
    use SeoTrait;

    /**
     * Hiển thị trang tĩnh theo slug
     *
     * @param string $slug Slug của trang
     * @return \Illuminate\View\View
     */
    public function detail($page)
    {            
        if ($page->lang !== App::getLocale()) {
            $code = TranslationHelper::getTranslatedPageCode($page->id);
            $page = TranslationHelper::getTranslatedPage($code);
            return redirect()->route('static.page', $page->slug);
        }

        $code = TranslationHelper::getTranslatedPageCode($page->id);

        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? '',
            'seo_description' => $page->seo_description ?? '',
            'seo_keywords' => $page->seo_keywords ?? '',
            'seo_author' => $page->author->name ?? '',
            'seo_canonical' => route('static.page', $page->slug),
            'seo_type' => 'website',
            'seo_image' => $page->image ?? ''
        ]);

        // Kiểm tra xem có file view riêng không
        $view = "templates.auvista.pages.{$code}";

        if (!view()->exists($view)) {
            $view = 'templates.auvista.pages.default';
        }


        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page->title,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page->title
        ];

        if ($page->slug == 'du-an' || $page->slug == 'projects') {
            // Lấy tất cả dự án
            $projects = Post::where('status', 'published')
                ->whereHas('categories', function ($query) use ($page) {
                    $query->where('slug', $page->slug);
                })
                ->where('lang', app()->getLocale())
                ->with(['categories' => function ($query) {
                    $query->where('type', 'post');
                }])
                ->orderBy('published_at', 'desc')
                ->paginate(12);

            // Lấy các lĩnh vực dự án (category con của "Dự án")
            $projectCategories = Category::where('type', 'post')
                ->where('status', 'active')
                ->whereHas('parent', function ($query) use ($page) {
                    $query->where('slug', $page->slug);
                })
                ->where('lang', app()->getLocale())
                ->orderBy('name')
                ->get();

            // Lấy featured projects cho sidebar
            $featuredProjects = Post::where('status', 'published')
                ->where('is_featured', true)
                ->whereHas('categories', function ($query) use ($page) {
                    $query->where('slug', $page->slug);
                })
                ->where('lang', app()->getLocale())
                ->with(['categories' => function ($query) {
                    $query->where('type', 'post');
                }])
                ->orderBy('published_at', 'desc')
                ->limit(5)
                ->get();

            $data = [
                'projects' => $projects,
                'projectCategories' => $projectCategories,
                'featuredProjects' => $featuredProjects,
                'currentCategory' => null
            ];

            return view($view, compact('page', 'breadcrumbs', 'data') + $seoData);
        }

        if ($page->slug == 'san-pham' || $page->slug == 'products') {
            $products = Product::where('status', 'published')
                ->where('lang', app()->getLocale())
                ->paginate(12);

            return view($view, compact('page', 'breadcrumbs', 'products') + $seoData);
        }

        return view($view, compact('page', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị trang giới thiệu
     *
     * @return \Illuminate\View\View
     */
    public function about()
    {
        // Lấy trang giới thiệu từ database
        $page = StaticPage::where('slug', 'gioi-thieu')
            ->where('lang', app()->getLocale())
            ->where('status', 'published')
            ->first();

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? '',
            'seo_description' => $page->seo_description ?? '',
            'seo_keywords' => $page->seo_keywords ?? '',
            'seo_author' => $page->author ?? '',
            'seo_canonical' => route('page.about'),
            'seo_image' => $page->image ?? ''
        ]);

        $data = [
            'pageTitle' => $page ? $page->title : 'Giới thiệu',
            'page' => $page
        ];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page ? $page->title : 'Giới thiệu',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page ? $page->title : 'Giới thiệu'
        ];

        return view('templates.auvista.pages.about', compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị trang liên hệ
     *
     * @return \Illuminate\View\View
     */
    public function contact()
    {
        // Lấy trang liên hệ từ database
        $page = StaticPage::where('slug', 'lien-he')
            ->where('lang', app()->getLocale())
            ->where('status', 'published')
            ->first();

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? '',
            'seo_description' => $page->seo_description ?? '',
            'seo_keywords' => $page->seo_keywords ?? '',
            'seo_author' => $page->author ?? '',
            'seo_canonical' => route('static.page', ['slug' => 'lien-he']),
            'seo_image' => $page->image ?? ''
        ]);

        $data = [
            'pageTitle' => $page ? $page->title : 'Liên hệ',
            'page' => $page
        ];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page ? $page->title : 'Liên hệ',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page ? $page->title : 'Liên hệ'
        ];

        return view('templates.auvista.pages.contact', compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Xử lý gửi form liên hệ
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendContact(Request $request)
    {
        // Xử lý dữ liệu gửi từ form liên hệ
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'message' => 'required|string',
        ]);

        // Lưu dữ liệu hoặc gửi email
        // ...

        return redirect()->route('contact')->with('success', 'Cảm ơn! Chúng tôi đã nhận được thông tin của bạn.');
    }

    /**
     * Hiển thị trang giải pháp
     *
     * @return \Illuminate\View\View
     */
    public function solutions()
    {
        // Lấy trang giải pháp từ database
        $page = StaticPage::where('slug', 'giai-phap')
            ->where('lang', app()->getLocale())
            ->where('status', 'published')
            ->first();

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? 'Giải pháp - Auvista',
            'seo_description' => $page->seo_description ?? 'Khám phá các giải pháp âm thanh chuyên nghiệp từ Auvista',
            'seo_keywords' => $page->seo_keywords ?? 'giải pháp âm thanh, hệ thống âm thanh, Auvista',
            'seo_author' => $page->author ?? '',
            'seo_canonical' => route('page.solutions'),
            'seo_image' => $page->image ?? ''
        ]);

        $data = [
            'pageTitle' => $page ? $page->title : 'Giải pháp',
            'page' => $page
        ];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page ? $page->title : 'Giải pháp',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page ? $page->title : 'Giải pháp'
        ];

        return view('templates.auvista.pages.solutions', compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị chi tiết giải pháp
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function solutionDetail($slug)
    {
        // Tìm bài viết solution thuộc category type='solution'
        $solution = Post::where('slug', $slug)
            ->where('status', 'published')
            ->where('lang', app()->getLocale())
            ->whereHas('categories', function($query) {
                $query->where('type', 'solution');
            })
            ->with(['categories', 'author'])
            ->first();

        // Nếu không tìm thấy bài viết thực, thử mock data
        if (!$solution) {
            $solutionData = $this->getMockSolutionData($slug);
            
            if (!$solutionData) {
                abort(404, 'Giải pháp không tồn tại');
            }
            
            $solution = (object) $solutionData;
        }

        // Lấy danh mục chính của solution
        $primaryCategory = null;
        if (isset($solution->categories) && method_exists($solution, 'categories')) {
            // Bài viết thực từ database
            $primaryCategory = $solution->categories()
                ->wherePivot('is_primary', true)
                ->first();

            if (!$primaryCategory) {
                $primaryCategory = $solution->categories()->first();
            }
        } else {
            // Mock data
            $primaryCategory = $solution->category ?? null;
        }

        // Lấy sản phẩm nổi bật liên quan
        $featuredProducts = Product::where('status', true)
            ->where('is_featured', true)
            ->with(['category', 'brand'])
            ->orderBy('created_at', 'desc')
            ->limit(8)
            ->get();

        // Nếu không có sản phẩm nổi bật, lấy sản phẩm thường
        if ($featuredProducts->isEmpty()) {
            $featuredProducts = Product::where('status', true)
                ->with(['category', 'brand'])
                ->orderBy('created_at', 'desc')
                ->limit(8)
                ->get();
        }

        // Lấy solutions liên quan 
        $relatedSolutions = collect();
        if (isset($solution->id) && is_numeric($solution->id) && !empty($primaryCategory->id)) {
            // Bài viết thực - lấy từ database
            $relatedSolutions = Post::join('post_category', 'posts.id', '=', 'post_category.post_id')
                ->where('post_category.category_id', $primaryCategory ? $primaryCategory->id : 0)
                ->where('posts.id', '!=', $solution->id)
                ->where('posts.lang', app()->getLocale())
                ->where('posts.status', 'published')
                ->orderBy('posts.published_at', 'desc')
                ->limit(4)
                ->get(['posts.*']);
        } else {
            // Mock data - tạo solutions liên quan giả
            $relatedSolutions = $this->getMockRelatedSolutions($solution->slug);
        }

        // Xử lý nội dung solution để thay thế URL ảnh (chỉ cho bài viết thực)
        if (isset($solution->content) && function_exists('processContentImages')) {
            $solution->content = processContentImages($solution->content);
        }

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $solution->seo_title ?? $solution->title,
            'seo_description' => $solution->seo_description ?? $solution->excerpt,
            'seo_keywords' => $solution->seo_keywords ?? '',
            'seo_author' => $solution->author->name ?? 'AuVista',
            'seo_canonical' => route('solutions.detail', $slug),
            'seo_image' => $solution->image ?? ''
        ]);

        $data = [
            'pageTitle' => $solution->title,
            'solution' => $solution,
            'featuredProducts' => $featuredProducts,
            'relatedSolutions' => $relatedSolutions
        ];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => __('messages.solutions'),
                    'url' => route('page.solutions'),
                    'active' => false
                ],
                [
                    'name' => $solution->title,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $solution->title
        ];

        return view('templates.auvista.pages.solution-detail', compact('solution', 'featuredProducts', 'relatedSolutions', 'data', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị trang thương hiệu
     *
     * @return \Illuminate\View\View
     */
    public function brand()
    {
        // Lấy trang thương hiệu từ database
        $page = StaticPage::where('slug', 'thuong-hieu')
            ->where('lang', app()->getLocale())
            ->where('status', 'published')
            ->first();

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? 'Thương hiệu - Auvista',
            'seo_description' => $page->seo_description ?? 'Khám phá các thương hiệu âm thanh chuyên nghiệp từ Auvista',
            'seo_keywords' => $page->seo_keywords ?? 'thương hiệu âm thanh, Auvista',
            'seo_author' => $page->author ?? '',
            'seo_canonical' => route('page.brand'),
            'seo_image' => $page->image ?? ''
        ]);

        $data = [
            'pageTitle' => $page ? $page->title : 'Thương hiệu',
            'page' => $page
        ];

        // Lấy danh sách tất cả thương hiệu từ database, phân trang
        $brands = Brand::paginate(20);

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page ? $page->title : 'Thương hiệu',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page ? $page->title : 'Thương hiệu'
        ];

        return view('templates.auvista.pages.brands', compact('data', 'breadcrumbs', 'brands') + $seoData);
    }

    /**
     * Hiển thị trang chi tiết thương hiệu và sản phẩm của thương hiệu đó.
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function brandDetail($slug)
    {
        $brand = Brand::where('slug', $slug)->where('status', 'published')->firstOrFail();
        
        $products = Product::where('brand_id', $brand->id)
            ->where('status', 'published')
            ->paginate(12);

        $featuredProducts = Product::where('brand_id', $brand->id)
            ->where('is_featured', true)
            ->where('status', 'published')
            ->limit(8)
            ->get();
            
        $categories = $brand->products()->with('category')->get()->pluck('category')->unique();

        $seoData = $this->getDataSeo([
            'seo_title' => $brand->seo_title ?? $brand->name,
            'seo_description' => $brand->seo_description ?? $brand->description,
            'seo_keywords' => $brand->seo_keywords,
            'seo_image' => $brand->getFirstMediaUrl('logo')
        ]);

        return view('templates.auvista.products.brand', compact('brand', 'products', 'featuredProducts', 'categories', 'seoData'));
    }

    /**
     * Tạo dữ liệu mock cho solution dựa trên slug
     *
     * @param string $slug
     * @return array|null
     */
    private function getMockSolutionData($slug)
    {
        $mockSolutions = [
            'hoi-nghi-truyen-hinh' => [
                'id' => 1,
                'title' => __('messages.video_conferencing'),
                'content' => '<p>' . __('messages.video_conferencing_description') . '</p>
                             <p>Hệ thống được thiết kế để phục vụ các phòng họp từ 6-20 người, tích hợp camera PTZ với zoom quang học 12x, microphone phủ sóng 360 độ và loa stereo chất lượng cao.</p>
                             <h3>Tính năng nổi bật:</h3>
                             <ul>
                                <li>Camera 4K UHD với zoom quang học 12x</li>
                                <li>Microphone phủ sóng 360 độ, khử tiếng ồn thông minh</li>
                                <li>Loa stereo chất lượng Hi-Fi</li>
                                <li>Hỗ trợ kết nối đa nền tảng: Zoom, Teams, Skype</li>
                                <li>Điều khiển từ xa và ứng dụng di động</li>
                             </ul>',
                'excerpt' => __('messages.video_conferencing_description'),
                'slug' => $slug,
                'status' => 'published',
                'image' => 'solution-1.jpg',
                'published_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
                'seo_title' => __('messages.video_conferencing') . ' - Giải pháp công nghệ Auvista',
                'seo_description' => __('messages.video_conferencing_description'),
                'seo_keywords' => 'hội nghị truyền hình, video conference, 4K, âm thanh chuyên nghiệp',
                'author' => (object) ['name' => 'Auvista'],
                'categories' => collect([(object) ['name' => __('messages.video_conferencing'), 'slug' => 'hoi-nghi-truyen-hinh']]),
                'category' => (object) ['name' => __('messages.video_conferencing'), 'slug' => 'hoi-nghi-truyen-hinh'],
                'views_count' => 156
            ],
            'am-thanh-hoi-thao' => [
                'id' => 2,
                'title' => __('messages.conference_audio'),
                'content' => '<p>' . __('messages.conference_audio_description') . '</p>
                             <p>Giải pháp tích hợp micro không dây, loa line array và bộ xử lý âm thanh số để đảm bảo chất lượng âm thanh rõ ràng, đồng đều trong toàn bộ không gian sự kiện.</p>
                             <h3>Đặc điểm vượt trội:</h3>
                             <ul>
                                <li>Micro không dây chống nhiễu, tần số UHF</li>
                                <li>Loa line array phủ sóng đều khắp hội trường</li>
                                <li>Bộ xử lý âm thanh số với DSP chuyên nghiệp</li>
                                <li>Hệ thống feedback eliminator tự động</li>
                                <li>Mixer digital 32 kênh</li>
                             </ul>',
                'excerpt' => __('messages.conference_audio_description'),
                'slug' => $slug,
                'status' => 'published',
                'image' => 'solution-2.jpg',
                'published_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
                'seo_title' => __('messages.conference_audio') . ' - Hệ thống âm thanh chuyên nghiệp',
                'seo_description' => __('messages.conference_audio_description'),
                'seo_keywords' => 'âm thanh hội thảo, micro không dây, loa line array, DSP',
                'author' => (object) ['name' => 'Auvista'],
                'categories' => collect([(object) ['name' => __('messages.conference_audio'), 'slug' => 'am-thanh-hoi-thao']]),
                'category' => (object) ['name' => __('messages.conference_audio'), 'slug' => 'am-thanh-hoi-thao'],
                'views_count' => 234
            ],
            'am-thanh-anh-sang-bieu-dien' => [
                'id' => 3,
                'title' => __('messages.stage_lighting_audio'),
                'content' => '<p>' . __('messages.stage_lighting_audio_description') . '</p>
                             <p>Hệ thống kết hợp âm thanh công suất lớn với hiệu ứng ánh sáng đa dạng, tạo nên trải nghiệm thị giác và thính giác hoàn hảo cho khán giả.</p>
                             <h3>Thiết bị hiện đại:</h3>
                             <ul>
                                <li>Loa công suất lớn dải tần rộng</li>
                                <li>Đèn LED moving head chuyên nghiệp</li>
                                <li>Bàn mixer ánh sáng DMX512</li>
                                <li>Máy khói, máy tạo bọt chuyên dụng</li>
                                <li>Laser show và hiệu ứng đặc biệt</li>
                             </ul>',
                'excerpt' => __('messages.stage_lighting_audio_description'),
                'slug' => $slug,
                'status' => 'published',
                'image' => 'solution-3.jpg',
                'published_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
                'seo_title' => __('messages.stage_lighting_audio') . ' - Giải pháp sân khấu chuyên nghiệp',
                'seo_description' => __('messages.stage_lighting_audio_description'),
                'seo_keywords' => 'âm thanh biểu diễn, ánh sáng sân khấu, concert, DMX512',
                'author' => (object) ['name' => 'Auvista'],
                'categories' => collect([(object) ['name' => __('messages.stage_lighting_audio'), 'slug' => 'am-thanh-anh-sang-bieu-dien']]),
                'category' => (object) ['name' => __('messages.stage_lighting_audio'), 'slug' => 'am-thanh-anh-sang-bieu-dien'],
                'views_count' => 189
            ]
        ];

        return $mockSolutions[$slug] ?? null;
    }

    /**
     * Tạo dữ liệu mock cho solutions liên quan
     *
     * @param string $currentSlug
     * @return \Illuminate\Support\Collection
     */
    private function getMockRelatedSolutions($currentSlug)
    {
        $allSolutions = [
            'hoi-nghi-truyen-hinh' => (object) [
                'title' => __('messages.video_conferencing'),
                'slug' => 'hoi-nghi-truyen-hinh',
                'excerpt' => __('messages.video_conferencing_description'),
                'image' => 'solution-1.jpg',
                'published_at' => now()
            ],
            'am-thanh-hoi-thao' => (object) [
                'title' => __('messages.conference_audio'),
                'slug' => 'am-thanh-hoi-thao',
                'excerpt' => __('messages.conference_audio_description'),
                'image' => 'solution-2.jpg',
                'published_at' => now()
            ],
            'am-thanh-anh-sang-bieu-dien' => (object) [
                'title' => __('messages.stage_lighting_audio'),
                'slug' => 'am-thanh-anh-sang-bieu-dien',
                'excerpt' => __('messages.stage_lighting_audio_description'),
                'image' => 'solution-3.jpg',
                'published_at' => now()
            ],
            'phong-hoc-thong-minh' => (object) [
                'title' => __('messages.smart_classroom'),
                'slug' => 'phong-hoc-thong-minh',
                'excerpt' => __('messages.smart_classroom_description'),
                'image' => 'solution-4.jpg',
                'published_at' => now()
            ]
        ];

        // Loại bỏ solution hiện tại và lấy tối đa 3 solutions khác
        $filtered = collect($allSolutions)->reject(function ($solution) use ($currentSlug) {
            return $solution->slug === $currentSlug;
        })->take(3);

        return $filtered;
    }
}
