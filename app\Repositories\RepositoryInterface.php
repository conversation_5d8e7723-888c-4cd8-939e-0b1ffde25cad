<?php

namespace App\Repositories;

interface RepositoryInterface
{
    /**
     * L<PERSON>y tất cả các bản ghi
     * 
     * @param array $columns
     * @return mixed
     */
    public function all($columns = ['*']);
    
    /**
     * L<PERSON>y tất cả các bản ghi với phân trang
     * 
     * @param int $perPage
     * @param array $columns
     * @return mixed
     */
    public function paginate($perPage = 15, $columns = ['*']);
    
    /**
     * Tạo bản ghi mới
     * 
     * @param array $data
     * @return mixed
     */
    public function create(array $data);
    
    /**
     * Cập nhật bản ghi
     * 
     * @param array $data
     * @param int $id
     * @return mixed
     */
    public function update(array $data, $id);
    
    /**
     * Xóa bản ghi
     * 
     * @param int $id
     * @return mixed
     */
    public function delete($id);
    
    /**
     * Tìm bản ghi theo ID
     * 
     * @param int $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, $columns = ['*']);
    
    /**
     * Tìm bản ghi theo điều kiện
     * 
     * @param string $field
     * @param mixed $value
     * @param array $columns
     * @return mixed
     */
    public function findBy($field, $value, $columns = ['*']);
} 