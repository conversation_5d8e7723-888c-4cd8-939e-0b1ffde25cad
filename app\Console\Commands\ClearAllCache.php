<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class ClearAllCache extends Command
{
    protected $signature = 'cache:clear-all';
    protected $description = 'Clear all types of cache including config, route, view, and application cache';

    public function handle(): void
    {
        $this->info('Clearing application cache...');
        Artisan::call('cache:clear');
        $this->info(Artisan::output());

        $this->info('Clearing route cache...');
        Artisan::call('route:clear');
        $this->info(Artisan::output());

        $this->info('Clearing config cache...');
        Artisan::call('config:clear');
        $this->info(Artisan::output());

        $this->info('Clearing view cache...');
        Artisan::call('view:clear');
        $this->info(Artisan::output());

        $this->info('Clearing event cache...');
        Artisan::call('event:clear');
        $this->info(Artisan::output());

        $this->info('Clearing OPcache...');
        if (function_exists('opcache_reset')) {
            opcache_reset();
            $this->info('OPcache cleared successfully.');
        } else {
            $this->warn('OPcache is not enabled.');
        }

        $this->info('Clearing bootstrap cache files...');
        File::delete(storage_path('framework/cache/data'));
        File::delete(base_path('bootstrap/cache/config.php'));
        File::delete(base_path('bootstrap/cache/routes.php'));

        $this->info('All caches have been cleared successfully!');
    }
}
