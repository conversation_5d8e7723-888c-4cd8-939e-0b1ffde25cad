<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Đặt cancelled_at nếu trạng thái là canceled
        if (isset($data['status']) && $data['status'] === 'cancelled' && empty($data['cancelled_at'])) {
            $data['cancelled_at'] = now();
        }
        
        // Đặt delivered_at nếu trạng thái là delivered
        if (isset($data['status']) && $data['status'] === 'delivered' && empty($data['delivered_at'])) {
            $data['delivered_at'] = now();
        }
        
        return $data;
    }
    
    protected function afterSave(): void
    {
        // Bạn có thể thêm logic sau khi lưu tại đây
        // Ví dụ: gửi email thông báo, cập nhật dữ liệu liên quan, v.v.
    }
} 