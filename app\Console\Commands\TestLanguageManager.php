<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LanguageFileService;

class TestLanguageManager extends Command
{
    protected $signature = 'test:language-manager';
    protected $description = 'Test the language file management functionality';

    public function handle()
    {
        $service = new LanguageFileService();
        
        $this->info('Testing Language File Manager...');
        
        // Test 1: Get all language files
        $this->info('1. Getting all language files...');
        $files = $service->getAllLanguageFiles();
        $this->info("Found {$files->count()} language files");
        
        foreach ($files as $file) {
            $this->line("  - {$file['locale']}/{$file['file']}.php ({$file['keys_count']} keys)");
        }
        
        // Test 2: Create a test file
        $this->info('2. Creating a test language file...');
        $testTranslations = [
            'test_key' => 'Test Value',
            'welcome' => 'Welcome to our application',
            'goodbye' => 'Goodbye!',
        ];
        
        $result = $service->createLanguageFile('en', 'test', $testTranslations);
        if ($result) {
            $this->info('✓ Test file created successfully');
        } else {
            $this->error('✗ Failed to create test file');
        }
        
        // Test 3: Find the created file
        $this->info('3. Finding the created file...');
        $foundFile = $service->findLanguageFile('en_test');
        if ($foundFile) {
            $this->info("✓ Found file: {$foundFile['locale']}/{$foundFile['file']}.php");
            $this->info("  Keys: " . implode(', ', array_keys($foundFile['translations'])));
        } else {
            $this->error('✗ Could not find the created file');
        }
        
        // Test 4: Update the file
        $this->info('4. Updating the test file...');
        $updatedTranslations = [
            'test_key' => 'Updated Test Value',
            'welcome' => 'Welcome to our updated application',
            'goodbye' => 'Goodbye and see you soon!',
            'new_key' => 'This is a new key',
        ];
        
        $updateResult = $service->updateLanguageFile('en', 'test', $updatedTranslations);
        if ($updateResult) {
            $this->info('✓ Test file updated successfully');
        } else {
            $this->error('✗ Failed to update test file');
        }
        
        // Test 5: Verify the update
        $this->info('5. Verifying the update...');
        $updatedFile = $service->findLanguageFile('en_test');
        if ($updatedFile && count($updatedFile['translations']) === 4) {
            $this->info('✓ File updated correctly with 4 keys');
        } else {
            $this->error('✗ File update verification failed');
        }
        
        // Test 6: Clean up - delete the test file
        $this->info('6. Cleaning up test file...');
        $deleteResult = $service->deleteLanguageFile('en', 'test');
        if ($deleteResult) {
            $this->info('✓ Test file deleted successfully');
        } else {
            $this->error('✗ Failed to delete test file');
        }
        
        $this->info('Language File Manager test completed!');
        
        return 0;
    }
} 