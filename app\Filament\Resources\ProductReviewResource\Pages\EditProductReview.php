<?php

namespace App\Filament\Resources\ProductReviewResource\Pages;

use App\Filament\Resources\ProductReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProductReview extends EditRecord
{
    protected static string $resource = ProductReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Nếu admin thêm phản hồi mới, cập nhật thông tin admin
        if (!empty($data['admin_reply']) && empty($this->record->admin_reply)) {
            $data['admin_id'] = auth()->id();
            $data['admin_reply_at'] = now();
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Cập nhật thống kê đánh gi<PERSON> cho sản phẩm
        $this->record->product->updateRatingStats();
    }
}
