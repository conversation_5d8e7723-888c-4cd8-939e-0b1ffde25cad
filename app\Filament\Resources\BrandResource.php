<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BrandResource\Pages;
use App\Models\Brand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Group;
use Illuminate\Support\Str;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use App\Traits\HasLanguageSync;

class BrandResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = Brand::class;
    protected static ?string $navigationIcon = 'heroicon-o-tag';
    protected static ?string $navigationGroup = 'Quản lý sản phẩm';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationLabel = 'Thương hiệu';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Thông tin thương hiệu')
                            ->schema([
                                TextInput::make('name')
                                    ->label('Tên thương hiệu')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                        if ($operation === 'create') {
                                            $set('slug', Str::slug($state));
                                        }
                                    }),

                                TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->disabled(fn (string $operation): bool => $operation === 'edit')
                                    ->dehydrated(),

                                RichEditor::make('description')
                                    ->label('Mô tả')
                                    ->columnSpanFull(),

                                Toggle::make('status')
                                    ->label('Trạng thái hiển thị')
                                    ->default(true),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Group::make()
                    ->schema([
                        Section::make('Hình ảnh')
                            ->schema([
                                FileUpload::make('image')
                                    ->label('Logo thương hiệu')
                                    ->image()
                                    ->directory('brands')
                                    ->visibility('public')
                                    ->maxSize(1024)
                                    ->imageResizeMode('cover')
                                    ->imageCropAspectRatio('1:1')
                                    ->imageResizeTargetWidth('300')
                                    ->imageResizeTargetHeight('300')
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                ImageColumn::make('image')
                    ->label('Logo')
                    ->circular(),

                TextColumn::make('name')
                    ->label('Tên thương hiệu')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('products_count')
                    ->label('Số sản phẩm')
                    ->counts('products')
                    ->sortable(),

                ToggleColumn::make('status')
                    ->label('Trạng thái')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\TernaryFilter::make('status')
                    ->label('Trạng thái hiển thị'),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('copy_to_language')
                        ->label('Copy sang ngôn ngữ khác...')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $newRecord = $record->replicate();
                                $newRecord->lang = $data['target_lang'];
                                $newRecord->slug = $newRecord->slug . '-' . $data['target_lang'];
                                $newRecord->save();
                            }

                            Notification::make()
                                ->title('Đã copy ' . count($records) . ' bản ghi')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Chọn ngôn ngữ đích')
                                ->options([
                                    'vi' => 'Tiếng Việt',
                                    'en' => 'Tiếng Anh',
                                ])
                                ->required(),
                        ]),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBrands::route('/'),
            'create' => Pages\CreateBrand::route('/create'),
            'edit' => Pages\EditBrand::route('/{record}/edit'),
            'view' => Pages\ViewBrand::route('/{record}'),
        ];
    }
} 