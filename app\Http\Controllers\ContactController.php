<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Traits\SeoTrait;

class ContactController extends Controller
{
    use SeoTrait;

    /**
     * Hiển thị trang liên hệ
     */
    public function index()
    {
        // Lấy thông tin trang tĩnh từ database
        $page = \App\Models\StaticPage::where('slug', 'lien-he')
            ->where('status', 'published')
            ->first();

        $seoData = $this->getDataSeo([
            'title' => 'Liên hệ - ' . getSetting('site_name'),
            'meta_description' => $page ? ($page->excerpt ?: $page->seo_description) : 'Thông tin liên hệ ' . getSetting('site_name'),
            'meta_keywords' => 'liên hệ auvista, địa chỉ auvista, số điện thoại auvista'
        ]);

        $data = [
            'pageTitle' => $page ? $page->title : 'Liên hệ',
            'page' => $page
        ];

        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page ? $page->title : 'Liên hệ',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page ? $page->title : 'Liên hệ'
        ];

        return view('templates.auvista.pages.lien-he', compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Xử lý gửi form liên hệ
     */
    public function store(Request $request)
    {
        // Validate dữ liệu
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'message' => 'required|string',
        ], [
            'name.required' => __('messages.full_name_required'),
            'email.required' => __('messages.email_required'),
            'email.email' => __('messages.email_invalid'),
            'message.required' => __('messages.message_required'),
        ]);

        if ($validator->fails()) {
            Log::warning('Form validation failed: ' . json_encode($validator->errors()->toArray()));

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $validator->errors()->first(),
                ]);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        Log::info('Form data received: ' . json_encode($request->except(['_token'])));

        $contactData = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => 'Form thông tin liên hệ',
            'message' => $request->message,
            'status' => \App\Models\Contact::STATUS_NEW,
        ];

        // Lưu thông tin liên hệ
        $contact = \App\Models\Contact::create($contactData);
        Log::info('Contact form saved with ID: ' . $contact->id);

        if ($request->wantsJson()) {
            return response()->json([
                'success' => true,
                'message' => __('messages.contact_success'),
            ]);
        }

        return redirect()->back()
            ->with('success', __('messages.contact_success'));
    }
}
