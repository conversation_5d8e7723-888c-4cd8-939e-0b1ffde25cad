<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductReview;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;

class ProductReviewController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $reviews = ProductReview::with('product')
            ->latest()
            ->paginate(15);
            
        return view('admin.reviews.index', compact('reviews'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created review in storage.
     */
    public function store(Request $request, $productId)
    {
        $validator = Validator::make($request->all(), [
            'reviewer_name' => 'required|string|max:255',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string|min:10',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '<PERSON>ui lòng nhập đầy đủ thông tin.',
            ]);
        }

        $request->merge([
            'reviewer_email' => $request->reviewer_email ?? '<EMAIL>',
        ]);
        
        // Tìm sản phẩm
        $product = Product::findOrFail($productId);
        // Thêm đánh giá
        $reviewId = $product->addReview($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Cảm ơn bạn đã đánh giá sản phẩm. Đánh giá của bạn sẽ được hiển thị sau khi được kiểm duyệt.',
            'review_id' => $reviewId
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Mark a review as helpful
     */
    public function markHelpful(Request $request, $id)
    {
        $review = ProductReview::findOrFail($id);
        $review->incrementHelpfulCount();
        
        return response()->json([
            'success' => true,
            'helpful_count' => $review->helpful_count
        ]);
    }

    /**
     * Phản hồi đánh giá của khách hàng (dành cho admin)
     */
    public function adminReply(Request $request, $id)
    {
        $request->validate([
            'admin_reply' => 'required|string|min:5',
        ]);
        
        $review = ProductReview::findOrFail($id);
        $review->addAdminReply($request->admin_reply);
        
        return redirect()->back()->with('success', 'Đã thêm phản hồi thành công.');
    }

    /**
     * Cập nhật trạng thái đánh giá (hiển thị/ẩn)
     */
    public function updateStatus(Request $request, $id)
    {
        $review = ProductReview::findOrFail($id);
        $review->updateStatus($request->status);
        
        return response()->json([
            'success' => true,
            'message' => 'Đã cập nhật trạng thái đánh giá thành công.'
        ]);
    }

    /**
     * Danh sách đánh giá chờ phản hồi (dành cho admin)
     */
    public function pendingReviews()
    {
        $pendingReviews = ProductReview::with('product')
            ->approved()
            ->withoutAdminReply()
            ->latest()
            ->paginate(15);
            
        return view('admin.reviews.pending', compact('pendingReviews'));
    }

    /**
     * Danh sách tất cả đánh giá (dành cho admin)
     */
    public function allReviews()
    {
        // Kiểm tra quyền admin - sử dụng email admin
        if (!Auth::check() || Auth::user()->email !== '<EMAIL>') {
            abort(403);
        }

        $reviews = ProductReview::with('product')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.reviews.index', compact('reviews'));
    }
}
