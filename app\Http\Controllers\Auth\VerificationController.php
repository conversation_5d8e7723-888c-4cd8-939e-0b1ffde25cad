<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use App\Models\Account;
use Illuminate\Support\Facades\Mail;

class VerificationController extends Controller
{
    /**
     * Hiển thị thông báo cần xác thực email
     */
    public function notice()
    {
        return view('templates.auvista.auth.verify');
    }

    /**
     * Gửi email xác thực
     */
    public function send(Request $request)
    {
        $account = $request->user('frontend');

        // Kiểm tra xem email đã xác thực chưa
        if ($account->hasVerifiedEmail()) {
            return redirect()->route('home');
        }

        // Tạo token
        $token = Str::random(60);
        
        // Lưu token vào database
        DB::table('account_verification_tokens')->updateOrInsert(
            ['email' => $account->email],
            [
                'token' => $token,
                'created_at' => now(),
                'expires_at' => now()->addMinutes(60)
            ]
        );

        // Tạo URL xác thực
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            Carbon::now()->addMinutes(60),
            [
                'id' => $account->id,
                'token' => $token
            ]
        );

        // Gửi email xác thực
        Mail::send('templates.auvista.emails.verify', ['url' => $verificationUrl], function($message) use ($account) {
            $message->to($account->email);
            $message->subject('Xác thực tài khoản auvista');
        });

        return back()->with('status', 'Email xác thực đã được gửi!');
    }

    /**
     * Xác thực email
     */
    public function verify(Request $request)
    {
        // Kiểm tra tính hợp lệ của URL
        if (!$request->hasValidSignature()) {
            return redirect()->route('verification.notice')
                ->with('error', 'Liên kết xác thực không hợp lệ hoặc đã hết hạn');
        }

        $account = Account::findOrFail($request->id);

        // Kiểm tra token
        $record = DB::table('account_verification_tokens')
            ->where('email', $account->email)
            ->where('token', $request->token)
            ->where('expires_at', '>', now())
            ->first();

        if (!$record) {
            return redirect()->route('verification.notice')
                ->with('error', 'Mã xác thực không hợp lệ hoặc đã hết hạn');
        }

        // Cập nhật trạng thái xác thực email
        $account->email_verified_at = now();
        $account->save();

        // Xóa token
        DB::table('account_verification_tokens')
            ->where('email', $account->email)
            ->delete();

        return redirect()->route('home')
            ->with('success', 'Email của bạn đã được xác thực!');
    }
}
