<?php

namespace App\Services;

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

class LanguageSyncService
{
    /**
     * Set the application locale and sync across all components
     */
    public static function setLocale(string $locale): void
    {
        if (in_array($locale, ['vi', 'en'])) {
            // Set in session
            Session::put('locale', $locale);
            Session::put('filament_locale', $locale);
            
            // Set application locale
            App::setLocale($locale);
            
            // Save session immediately
            Session::save();
        }
    }

    /**
     * Get current locale from session or app config
     */
    public static function getCurrentLocale(): string
    {
        return Session::get('locale', Session::get('filament_locale', config('app.locale', 'vi')));
    }

    /**
     * Get locale for Filament filters
     */
    public static function getFilamentLocale(): string
    {
        return Session::get('filament_locale', self::getCurrentLocale());
    }

    /**
     * Set locale specifically for Filament filters
     */
    public static function setFilamentLocale(string $locale): void
    {
        if (in_array($locale, ['vi', 'en'])) {
            Session::put('filament_locale', $locale);
            Session::put('locale', $locale);
            App::setLocale($locale);
            Session::save();
        }
    }

    /**
     * Sync locale between Language Switcher and Filament filters
     */
    public static function syncLocale(string $locale, string $source = 'switcher'): void
    {
        if (in_array($locale, ['vi', 'en'])) {
            Session::put('locale', $locale);
            Session::put('filament_locale', $locale);
            App::setLocale($locale);
            Session::save();
        }
    }
}
