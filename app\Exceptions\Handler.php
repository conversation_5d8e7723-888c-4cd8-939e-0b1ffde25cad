<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Request;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Kiểm tra xem request có phải là admin route không
     */
    private function isAdminRoute(Request $request): bool
    {
        return $request->is('admin/*') || 
               $request->is('filament/*');
    }

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });

        // Xử lý lỗi 404 - NotFoundHttpException chỉ cho frontend
        $this->renderable(function (NotFoundHttpException $e, Request $request) {
            // Nếu là API request, trả về JSON
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Không tìm thấy tài nguyên yêu cầu',
                    'status' => 404
                ], 404);
            }
            
            // Bỏ qua các route admin và filament
            if ($this->isAdminRoute($request)) {
                return;
            }
            
            // Chỉ xử lý cho frontend
            return response()->view('errors.404', [], 404);
        });
        
        // Xử lý lỗi 403 - AccessDeniedHttpException chỉ cho frontend
        $this->renderable(function (AccessDeniedHttpException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Bạn không có quyền truy cập tài nguyên này',
                    'status' => 403
                ], 403);
            }
            
            if ($this->isAdminRoute($request)) {
                return;
            }
            
            return response()->view('errors.403', [], 403);
        });
        
        // Xử lý lỗi 500 và các lỗi HTTP khác chỉ cho frontend
        $this->renderable(function (HttpException $e, Request $request) {
            if ($e instanceof NotFoundHttpException || $e instanceof AccessDeniedHttpException) {
                return; // Đã xử lý ở trên
            }
            
            $statusCode = $e->getStatusCode();
            
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => $e->getMessage() ?: 'Đã xảy ra lỗi máy chủ',
                    'status' => $statusCode
                ], $statusCode);
            }
            
            if ($this->isAdminRoute($request)) {
                return;
            }
            
            // Nếu có view tương ứng với mã lỗi
            if (view()->exists("errors.{$statusCode}")) {
                return response()->view("errors.{$statusCode}", [], $statusCode);
            }
            
            // Mặc định trả về trang 500
            if ($statusCode == 500 || $statusCode >= 400) {
                return response()->view('errors.500', [], $statusCode);
            }
        });
        
        // Xử lý các lỗi không phải HttpException (ví dụ: RuntimeException, ErrorException, ...)
        $this->renderable(function (Throwable $e, Request $request) {
            if ($e instanceof HttpException) {
                return; // Đã xử lý ở trên
            }
            
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => 'Đã xảy ra lỗi máy chủ nội bộ',
                    'status' => 500,
                    'error' => $e->getMessage()
                ], 500);
            }
            
            if ($this->isAdminRoute($request)) {
                return; // Để Laravel xử lý mặc định cho admin
            }
            
            // Chỉ xử lý cho frontend
            return response()->view('errors.500', [
                'exception' => $e
            ], 500);
        });
    }
} 