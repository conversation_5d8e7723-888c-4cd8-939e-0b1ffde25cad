<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Sửa enum type để thêm 'solution'
        DB::statement("ALTER TABLE categories MODIFY COLUMN type ENUM('post', 'product', 'solution') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Xóa 'solution' khỏi enum type
        DB::statement("ALTER TABLE categories MODIFY COLUMN type ENUM('post', 'product') NOT NULL");
    }
};
