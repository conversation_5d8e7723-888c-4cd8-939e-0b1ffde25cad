<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use Illuminate\Support\Facades\Session;
use Livewire\Attributes\On;

class CartPopup extends Component
{
    public $cartItems = [];
    public $total = 0;

    public function getListeners()
    {
        return [
            'cart-updated' => 'loadCart',
            'cart-updated-popup' => 'loadCart',
            'cart:refresh' => 'loadCart',
            'add-to-cart' => 'addToCart'
        ];
    }

    public function mount()
    {
        $this->loadCart();
    }

    #[On('cart-updated')]
    #[On('cart-updated-popup')]
    #[On('cart:refresh')]
    public function loadCart()
    {
        try {
            // Force reload session data
            $this->cartItems = Session::get('cart', []);
            $this->calculateTotals();
            
            \Illuminate\Support\Facades\Log::info('CartPopup loadCart called:', [
                'cartItems' => $this->cartItems,
                'session_cart' => Session::get('cart', []),
                'total' => $this->total,
                'session_id' => Session::getId()
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('CartPopup loadCart error:', [
                'error' => $e->getMessage()
            ]);
            $this->cartItems = [];
        }
    }

    public function updatedCartItems()
    {
        $this->calculateTotals();
        Session::put('cart', $this->cartItems);
    }

    public function calculateTotals()
    {
        $this->total = 0;
        $currentLocale = \Illuminate\Support\Facades\App::getLocale();

        foreach ($this->cartItems as $productId => $quantity) {
            $originalProduct = Product::find($productId);
            if ($originalProduct) {
                // Try to find the product in the current language
                $currentProduct = $originalProduct;
                if ($originalProduct->lang !== $currentLocale) {
                    $localeProduct = Product::where('sku', $originalProduct->sku)
                        ->where('lang', $currentLocale)
                        ->first();
                    if ($localeProduct) {
                        $currentProduct = $localeProduct;
                    }
                }
                
                // Use sale price if available and lower than regular price
                $price = (isset($currentProduct->sale_price) && $currentProduct->sale_price > 0 && $currentProduct->sale_price < $currentProduct->price) 
                    ? $currentProduct->sale_price 
                    : $currentProduct->price;
                $this->total += $price * $quantity;
            }
        }
    }

    public function addToCart($productId, $quantity = 1)
    {
        try {
            $product = Product::findOrFail($productId);
            
            if (isset($this->cartItems[$productId])) {
                $this->cartItems[$productId] += $quantity;
            } else {
                $this->cartItems[$productId] = $quantity;
            }

            Session::put('cart', $this->cartItems);
            $this->calculateTotals();
            
            $this->dispatch('cart-updated', [
                'cartCount' => count($this->cartItems),
                'cartItems' => $this->cartItems,
                'message' => __('messages.product_added_to_cart')
            ]);
            
            return response()->json([
                'success' => true,
                'message' => __('messages.product_added_to_cart'),
                'cartCount' => count($this->cartItems)
            ]);
        } catch (\Exception $e) {
            $this->dispatch('error', [
                'message' => __('Cannot add product to cart') . ': ' . $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => __('Cannot add product to cart') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    public function updateQuantity($productId, $quantity)
    {
        try {
            $product = Product::findOrFail($productId);
            
            if ($quantity < 1) {
                return;
            }
            
            $this->cartItems[$productId] = $quantity;
            Session::put('cart', $this->cartItems);
            $this->calculateTotals();
            
            $this->dispatch('cart-updated', [
                'cartCount' => count($this->cartItems),
                'cartItems' => $this->cartItems,
                'message' => __('messages.quantity_updated')
            ]);
            
            return response()->json([
                'success' => true,
                'message' => __('messages.quantity_updated'),
                'cartCount' => count($this->cartItems)
            ]);
        } catch (\Exception $e) {
            $this->dispatch('error', [
                'message' => __('messages.update_cart_error') . ': ' . $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => __('messages.update_cart_error') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    public function remove($productId)
    {
        try {
            $product = Product::findOrFail($productId);
            
            unset($this->cartItems[$productId]);
            Session::put('cart', $this->cartItems);
            $this->calculateTotals();
            
            $this->dispatch('cart-updated', [
                'cartCount' => count($this->cartItems),
                'cartItems' => $this->cartItems,
                'message' => __('messages.product_removed_from_cart')
            ]);
            
            return response()->json([
                'success' => true,
                'message' => __('messages.product_removed_from_cart'),
                'cartCount' => count($this->cartItems)
            ]);
        } catch (\Exception $e) {
            $this->dispatch('error', [
                'message' => __('messages.remove_product_error') . ': ' . $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => __('messages.remove_product_error') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    public function refreshCart()
    {
        $this->loadCart();
        $this->render();
    }

    public function clearCart()
    {
        Session::forget('cart');
        $this->cartItems = [];
        $this->calculateTotals();
        
        $this->dispatch('cart-updated', [
            'cartCount' => 0,
            'cartItems' => []
        ]);
    }
} 