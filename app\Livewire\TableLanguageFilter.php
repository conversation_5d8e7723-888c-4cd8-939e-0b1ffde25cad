<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\LanguageSyncService;

class TableLanguageFilter extends Component
{
    public $currentLocale;
    public $tableName;

    public function mount($tableName = 'default')
    {
        $this->tableName = $tableName;
        $this->currentLocale = LanguageSyncService::getCurrentLocale();
    }

    public function switchLanguage($locale)
    {
        if (in_array($locale, ['vi', 'en'])) {
            LanguageSyncService::syncLocale($locale, 'table-filter');
            $this->currentLocale = $locale;

            // Use JavaScript to reload the page to apply new language filter
            $this->js('window.location.reload()');
        }
    }

    public function render()
    {
        return view('livewire.table-language-filter', [
            'languages' => [
                'vi' => [
                    'name' => 'Tiếng Việt',
                    'flag' => '🇻🇳',
                    'code' => 'VI'
                ],
                'en' => [
                    'name' => 'English',
                    'flag' => '🇺🇸',
                    'code' => 'EN'
                ]
            ]
        ]);
    }
}
