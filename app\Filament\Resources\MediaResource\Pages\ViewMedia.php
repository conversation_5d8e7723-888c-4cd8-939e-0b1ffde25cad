<?php

namespace App\Filament\Resources\MediaResource\Pages;

use App\Filament\Resources\MediaResource;
use App\Services\MediaService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Log;

class ViewMedia extends ViewRecord
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('download')
                ->label('Tải xuống')
                ->icon('heroicon-o-arrow-down-tray')
                ->url(fn () => url('storage/' . $this->record->path))
                ->openUrlInNewTab(),
            Actions\DeleteAction::make()
                ->before(function ($record) {
                    // Log trước khi xóa
                    Log::info('Đang xóa media từ trang view', ['id' => $record->id, 'path' => $record->path]);
                    
                    // Chỉ xóa file, model sẽ tự xóa DB record
                    app(MediaService::class)->deleteFiles($record);
                }),
        ];
    }
} 