<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;
use App\Models\Post;
use App\Models\User;

class CreateTestSolution extends Command
{
    protected $signature = 'test:create-solution {--all : Tạo tất cả solutions}';
    protected $description = 'Tạo test solution data';

    public function handle()
    {
        $user = User::first();
        if (!$user) {
            $this->error('Không có user!');
            return;
        }

        if ($this->option('all')) {
            $this->createAllSolutions($user);
        } else {
            $this->createSingleSolution($user);
        }
    }

    private function createSingleSolution($user)
    {
        // Tìm hoặc tạo category
        $category = Category::where('slug', 'hoi-nghi-truyen-hinh')
            ->where('type', 'solution')
            ->first();
            
        if (!$category) {
            // Thử tạo category với slug khác
            $category = Category::create([
                'name' => 'Hội nghị truyền hình',
                'slug' => 'hoi-nghi-truyen-hinh-solution',
                'type' => 'solution',
                'description' => 'Category test',
                'status' => 'active',
                'lang' => 'vi'
            ]);
        }

        // Tạo post test
        $post = Post::firstOrCreate(
            ['slug' => 'hoi-nghi-truyen-hinh', 'lang' => 'vi'],
            [
                'title' => 'Hội nghị truyền hình',
                'content' => '<p>Test solution content</p>',
                'excerpt' => 'Test solution excerpt',
                'status' => 'published',
                'author_id' => $user->id,
                'published_at' => now(),
                'image' => 'solution-1.jpg'
            ]
        );

        // Gán category
        $post->categories()->syncWithoutDetaching([
            $category->id => ['is_primary' => true]
        ]);

        $this->info('✅ Đã tạo test solution thành công!');
        $this->info("URL: /giai-phap/{$post->slug}");
    }

    private function createAllSolutions($user)
    {
        $solutions = [
            [
                'category_name' => 'Âm thanh hội thảo',
                'category_slug' => 'am-thanh-hoi-thao',
                'title' => 'Âm thanh hội thảo',
                'slug' => 'am-thanh-hoi-thao',
                'content' => '<p>Hệ thống âm thanh hội thảo chuyên nghiệp cho sự kiện lớn.</p>',
                'excerpt' => 'Hệ thống âm thanh hội thảo chuyên nghiệp cho sự kiện 50-500 người.',
                'image' => 'solution-2.jpg'
            ],
            [
                'category_name' => 'Âm thanh biểu diễn',
                'category_slug' => 'am-thanh-bieu-dien',
                'title' => 'Âm thanh, ánh sáng biểu diễn',
                'slug' => 'am-thanh-anh-sang-bieu-dien',
                'content' => '<p>Giải pháp âm thanh và ánh sáng biểu diễn chuyên nghiệp cho concert.</p>',
                'excerpt' => 'Giải pháp âm thanh và ánh sáng biểu diễn chuyên nghiệp cho concert, show diễn.',
                'image' => 'solution-3.jpg'
            ],
            [
                'category_name' => 'Phòng học thông minh',
                'category_slug' => 'phong-hoc-thong-minh',
                'title' => 'Phòng học thông minh',
                'slug' => 'phong-hoc-thong-minh',
                'content' => '<p>Giải pháp phòng học thông minh với bảng tương tác hiện đại.</p>',
                'excerpt' => 'Giải pháp phòng học thông minh với bảng tương tác và hệ thống âm thanh.',
                'image' => 'solution-4.jpg'
            ],
            [
                'category_name' => 'Âm nhạc nền đa vùng',
                'category_slug' => 'am-nhac-nen-da-vung',
                'title' => 'Âm nhạc nền đa vùng',
                'slug' => 'am-nhac-nen-da-vung',
                'content' => '<p>Hệ thống âm nhạc nền đa vùng cho trung tâm thương mại, khách sạn.</p>',
                'excerpt' => 'Hệ thống âm nhạc nền đa vùng cho không gian lớn.',
                'image' => 'solution-5.jpg'
            ],
            [
                'category_name' => 'Âm thanh thông báo',
                'category_slug' => 'am-thanh-thong-bao',
                'title' => 'Âm thanh thông báo',
                'slug' => 'am-thanh-thong-bao',
                'content' => '<p>Hệ thống âm thanh thông báo công cộng chuyên nghiệp.</p>',
                'excerpt' => 'Hệ thống âm thanh thông báo cho khu vực công cộng.',
                'image' => 'solution-6.jpg'
            ],
            [
                'category_name' => 'Hệ thống karaoke',
                'category_slug' => 'he-thong-karaoke',
                'title' => 'Hệ thống karaoke',
                'slug' => 'he-thong-karaoke',
                'content' => '<p>Hệ thống karaoke chuyên nghiệp cho quán bar, nhà hàng.</p>',
                'excerpt' => 'Hệ thống karaoke chất lượng cao cho giải trí.',
                'image' => 'solution-7.jpg'
            ],
            [
                'category_name' => 'Hệ thống phòng chiếu phim',
                'category_slug' => 'he-thong-phong-chieu-phim',
                'title' => 'Hệ thống phòng chiếu phim',
                'slug' => 'he-thong-phong-chieu-phim',
                'content' => '<p>Hệ thống âm thanh và hình ảnh cho rạp chiếu phim.</p>',
                'excerpt' => 'Hệ thống âm thanh surround và máy chiếu 4K cho rạp phim.',
                'image' => 'solution-8.jpg'
            ]
        ];

        $this->info('🚀 Bắt đầu tạo tất cả solutions...');

        foreach ($solutions as $solution) {
            try {
                // Tạo category
                $category = Category::firstOrCreate(
                    ['slug' => $solution['category_slug'], 'type' => 'solution', 'lang' => 'vi'],
                    [
                        'name' => $solution['category_name'],
                        'description' => 'Giải pháp ' . $solution['category_name'],
                        'status' => 'active'
                    ]
                );

                // Tạo post
                $post = Post::firstOrCreate(
                    ['slug' => $solution['slug'], 'lang' => 'vi'],
                    [
                        'title' => $solution['title'],
                        'content' => $solution['content'],
                        'excerpt' => $solution['excerpt'],
                        'status' => 'published',
                        'author_id' => $user->id,
                        'published_at' => now(),
                        'image' => $solution['image']
                    ]
                );

                // Gán category
                $post->categories()->syncWithoutDetaching([
                    $category->id => ['is_primary' => true]
                ]);

                $this->line("   ✅ {$solution['title']}");
            } catch (\Exception $e) {
                $this->error("   ❌ Lỗi tạo {$solution['title']}: " . $e->getMessage());
            }
        }

        $this->info('✅ Hoàn thành tạo tất cả solutions!');
        $this->info('🌐 Kiểm tra tại: /giai-phap');
    }
}
