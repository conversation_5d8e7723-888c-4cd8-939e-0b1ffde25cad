<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Post;
use App\Models\Category;
use App\Models\User;
use Illuminate\Support\Str;

class ImportPostsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posts:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import 3 bài viết mới về MIDSUN vào hệ thống';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Bắt đầu import 3 bài viết mới về MIDSUN...');

        // Danh sách 3 bài viết cần import
        $posts = $this->getPostsList();

        $imported = 0;
        $updated = 0;
        $errors = 0;

        foreach ($posts as $postData) {
            try {
                $result = $this->importPost($postData);
                if ($result === 'created') {
                    $imported++;
                    $this->info("✅ Đã tạo: {$postData['title']}");
                } else {
                    $updated++;
                    $this->warn("⚠️  Đã cập nhật: {$postData['title']}");
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("❌ Lỗi khi import {$postData['title']}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("🎉 Hoàn thành import!");
        $this->info("📊 Thống kê:");
        $this->info("   - Đã tạo mới: {$imported} bài viết");
        $this->info("   - Đã cập nhật: {$updated} bài viết");
        $this->info("   - Lỗi: {$errors} bài viết");

        return 0;
    }

    /**
     * Danh sách 3 bài viết cần import
     */
    private function getPostsList()
    {
        return [
            // BÀI VIẾT 1: Giới thiệu về MIDSUN
            [
                'title' => 'MIDSUN Việt Nam - Cầu nối đưa sản phẩm chất lượng đến người tiêu dùng',
                'slug' => 'midsun-viet-nam-cau-noi-dua-san-pham-chat-luong-den-nguoi-tieu-dung',
                'excerpt' => 'MIDSUN Việt Nam ra đời như một nhịp cầu kết nối giữa tinh hoa dưỡng sinh phương Đông, công nghệ sinh học hiện đại và sự thấu hiểu sâu sắc nhu cầu của người Việt.',
                'content' => '<p>MIDSUN Việt Nam ra đời như một nhịp cầu kết nối giữa tinh hoa dưỡng sinh phương Đông, công nghệ sinh học hiện đại và sự thấu hiểu sâu sắc nhu cầu của người Việt. Chúng tôi tin rằng, sức khỏe không phải là điều xa xỉ, mà là quyền được chăm sóc của mỗi người – từ trẻ em đến người già, từ người khỏe mạnh đến người đang hồi phục sau bệnh tật.</p>

<h3>Sứ mệnh của MIDSUN</h3>
<p>Công Ty TNHH MID SUN Việt Nam là đối tác chiến lược và là đại diện chính thức phân phối các sản phẩm của Loving Life tại thị trường Việt Nam. Không chỉ đơn thuần nhập khẩu và phân phối sản phẩm, MID SUN còn đóng vai trò quan trọng trong việc tư vấn về chăm sóc sức khỏe, giúp người tiêu dùng hiểu rõ hơn về cách duy trì một cuộc sống khỏe mạnh, đẹp và hạnh phúc.</p>

<h3>Cam kết chất lượng</h3>
<p>MID SUN không chỉ cung cấp sản phẩm mà còn tập trung vào việc truyền tải các giá trị sống khỏe và sống trọn vẹn đến cộng đồng. Chúng tôi cam kết:</p>
<ul>
<li>Đồng hành cùng mỗi khách hàng trên hành trình sống khỏe</li>
<li>Kiên tạo hệ sinh thái sản phẩm chất lượng cao, phù hợp với thể trạng người Việt</li>
<li>Đào tạo đội ngũ tư vấn tận tâm – không chỉ hiểu sản phẩm mà còn hiểu người dùng</li>
<li>Tiếp tục đầu tư vào nghiên cứu khoa học, mở rộng ứng dụng để phục vụ tốt hơn cho cộng đồng</li>
</ul>

<p>Hành trình sống khỏe bắt đầu từ sự lựa chọn hôm nay. Hãy để MIDSUN là người bạn đồng hành tin cậy của bạn và gia đình bạn – trong từng bước đi hướng đến cuộc sống khỏe mạnh, đẹp đẽ và tràn đầy hạnh phúc.</p>',
                'category_name' => 'Tin tức',
                'category_slug' => 'tin-tuc',
                'image' => 'posts/midsun-gioi-thieu.jpg',
                'status' => 'published',
                'is_featured' => true,
                'seo_title' => 'MIDSUN Việt Nam - Cầu nối đưa sản phẩm chất lượng đến người tiêu dùng',
                'seo_description' => 'Tìm hiểu về MIDSUN Việt Nam - đối tác chiến lược phân phối sản phẩm Loving Life, cam kết mang đến giải pháp chăm sóc sức khỏe tối ưu cho người Việt.',
                'seo_keywords' => 'MIDSUN, sức khỏe, thảo dược, Loving Life, chăm sóc sức khỏe'
            ],

            // BÀI VIẾT 2: Lợi ích của Thái Tuế
            [
                'title' => 'Thái Tuế - Bí quyết sống khỏe từ thiên nhiên',
                'slug' => 'thai-tue-bi-quyet-song-khoe-tu-thien-nhien',
                'excerpt' => 'Khám phá những lợi ích tuyệt vời của Thái Tuế - loại dược liệu quý hiếm được mệnh danh là "thần dược" trong y học cổ truyền phương Đông.',
                'content' => '<p>Thái Tuế, được mệnh danh là "thần dược" trong y học cổ truyền phương Đông, là một loại dược liệu quý hiếm có khả năng hỗ trợ nâng cao sức khỏe toàn diện. MIDSUN Việt Nam tự hào mang đến cho người tiêu dùng các sản phẩm từ Thái Tuế chất lượng cao nhất.</p>

<h3>Thái Tuế là gì?</h3>
<p>Thái Tuế là một loại sinh vật đặc biệt, được hình thành từ quá trình lên men tự nhiên của các chất hữu cơ trong đất. Trong y học cổ truyền Trung Quốc, Thái Tuế được xem là "thuốc tiên" có khả năng kéo dài tuổi thọ và tăng cường sức khỏe.</p>

<h3>Lợi ích của Thái Tuế</h3>
<ul>
<li><strong>Tăng cường hệ miễn dịch:</strong> Thái Tuế chứa nhiều polysaccharide có khả năng kích thích hệ miễn dịch, giúp cơ thể chống lại các bệnh tật.</li>
<li><strong>Chống lão hóa:</strong> Các chất chống oxi hóa trong Thái Tuế giúp ngăn ngừa quá trình lão hóa tế bào.</li>
<li><strong>Hỗ trợ gan:</strong> Thái Tuế có tác dụng bảo vệ và tái tạo tế bào gan, giúp gan hoạt động hiệu quả hơn.</li>
<li><strong>Cải thiện tuần hoàn máu:</strong> Giúp máu lưu thông tốt hơn, cung cấp oxy và dưỡng chất đến các cơ quan.</li>
<li><strong>Tăng cường thể lực:</strong> Cung cấp năng lượng tự nhiên, giúp cơ thể khỏe mạnh và tràn đầy sức sống.</li>
</ul>

<h3>Sản phẩm Thái Tuế tại MIDSUN</h3>
<p>MIDSUN cung cấp đa dạng sản phẩm từ Thái Tuế, bao gồm:</p>
<ul>
<li>Rượu Thái Tuế cao cấp</li>
<li>Viên nén chứa peptide từ Thái Tuế</li>
<li>Dung dịch uống từ Thái Tuế</li>
</ul>

<p>Tất cả sản phẩm đều được sản xuất theo quy trình nghiêm ngặt, đảm bảo chất lượng và hiệu quả cao nhất cho người sử dụng.</p>

<blockquote>
<p>"Sống khỏe - Sống đẹp - Sống hạnh phúc" với Thái Tuế từ MIDSUN Việt Nam.</p>
</blockquote>',
                'category_name' => 'Kiến thức sức khỏe',
                'category_slug' => 'kien-thuc-suc-khoe',
                'image' => 'posts/thai-tue-loi-ich.jpg',
                'status' => 'published',
                'is_featured' => true,
                'seo_title' => 'Thái Tuế - Bí quyết sống khỏe từ thiên nhiên | MIDSUN',
                'seo_description' => 'Khám phá lợi ích tuyệt vời của Thái Tuế - thần dược từ thiên nhiên giúp tăng cường sức khỏe, chống lão hóa và nâng cao chất lượng cuộc sống.',
                'seo_keywords' => 'Thái Tuế, thần dược, sức khỏe, chống lão hóa, tăng cường miễn dịch, MIDSUN'
            ],

            // BÀI VIẾT 3: Hướng dẫn chăm sóc sức khỏe
            [
                'title' => 'Bí quyết chăm sóc sức khỏe toàn diện theo triết lý phương Đông',
                'slug' => 'bi-quyet-cham-soc-suc-khoe-toan-dien-theo-triet-ly-phuong-dong',
                'excerpt' => 'Tìm hiểu cách chăm sóc sức khỏe toàn diện theo triết lý y học cổ truyền phương Đông, kết hợp với lối sống hiện đại để có một cuộc sống khỏe mạnh và hạnh phúc.',
                'content' => '<p>Trong triết lý y học cổ truyền phương Đông, sức khỏe không chỉ là việc không bị bệnh mà là trạng thái cân bằng hoàn hảo giữa thể chất, tinh thần và môi trường xung quanh. MIDSUN Việt Nam chia sẻ những bí quyết chăm sóc sức khỏe toàn diện để bạn có thể "Sống khỏe - Sống đẹp - Sống hạnh phúc".</p>

<h3>1. Cân bằng âm dương trong cơ thể</h3>
<p>Theo y học cổ truyền, sức khỏe là sự cân bằng giữa âm và dương trong cơ thể. Khi cân bằng này bị phá vỡ, cơ thể sẽ xuất hiện các triệu chứng bệnh tật.</p>

<h4>Cách duy trì cân bằng âm dương:</h4>
<ul>
<li>Ăn uống điều độ, kết hợp thực phẩm âm và dương</li>
<li>Duy trì giấc ngủ đầy đủ, đi ngủ và thức dậy đúng giờ</li>
<li>Tập luyện thể dục đều đặn nhưng không quá sức</li>
<li>Kiểm soát cảm xúc, tránh căng thẳng kéo dài</li>
</ul>

<h3>2. Chăm sóc theo 12 kinh mạch</h3>
<p>Cơ thể có 12 kinh mạch chính, mỗi kinh mạch hoạt động mạnh nhất vào một thời điểm cụ thể trong ngày:</p>

<table border="1" style="width: 100%; border-collapse: collapse;">
<tr><th>Thời gian</th><th>Kinh mạch</th><th>Cách chăm sóc</th></tr>
<tr><td>5h-7h</td><td>Đại Tràng</td><td>Uống nước ấm, đi vệ sinh</td></tr>
<tr><td>7h-9h</td><td>Dạ Dày</td><td>Ăn sáng đầy đủ</td></tr>
<tr><td>9h-11h</td><td>Tỳ</td><td>Làm việc tập trung</td></tr>
<tr><td>11h-13h</td><td>Tim</td><td>Nghỉ ngơi, thiền định</td></tr>
<tr><td>17h-19h</td><td>Thận</td><td>Uống sản phẩm bổ thận</td></tr>
<tr><td>19h-21h</td><td>Tâm Bào</td><td>Thư giãn, giao lưu gia đình</td></tr>
</table>

<h3>3. Dinh dưỡng theo ngũ hành</h3>
<p>Ngũ hành (Kim, Mộc, Thủy, Hỏa, Thổ) tương ứng với 5 tạng phủ và 5 màu sắc thực phẩm:</p>
<ul>
<li><strong>Kim (Phổi):</strong> Thực phẩm màu trắng - củ cải, bí ngô, hạnh nhân</li>
<li><strong>Mộc (Gan):</strong> Thực phẩm màu xanh - rau xanh, bông cải xanh</li>
<li><strong>Thủy (Thận):</strong> Thực phẩm màu đen - đậu đen, mè đen, nấm đen</li>
<li><strong>Hỏa (Tim):</strong> Thực phẩm màu đỏ - cà chua, ớt đỏ, táo đỏ</li>
<li><strong>Thổ (Tỳ):</strong> Thực phẩm màu vàng - ngô, khoai lang, chuối</li>
</ul>

<h3>4. Bổ sung thảo dược tự nhiên</h3>
<p>Các sản phẩm thảo dược từ MIDSUN có thể hỗ trợ chăm sóc sức khỏe theo từng cơ quan:</p>
<ul>
<li><strong>Thái Tuế:</strong> Tăng cường miễn dịch tổng thể</li>
<li><strong>Trần Bì:</strong> Hỗ trợ phổi và đường hô hấp</li>
<li><strong>Trà Phổ Nhĩ:</strong> Hỗ trợ tiêu hóa và thải độc</li>
<li><strong>Sinh Lực Vương:</strong> Bổ thận, tăng cường sinh lực</li>
</ul>

<h3>5. Lối sống hài hòa</h3>
<p>Cuối cùng, sức khỏe không chỉ đến từ những gì ta ăn hay uống, mà còn từ cách ta sống:</p>
<ul>
<li>Duy trì thái độ tích cực, lạc quan</li>
<li>Kết nối với thiên nhiên, thường xuyên tiếp xúc với không khí trong lành</li>
<li>Xây dựng mối quan hệ tốt với gia đình và bạn bè</li>
<li>Tìm thấy ý nghĩa và mục đích trong cuộc sống</li>
</ul>

<p>MIDSUN Việt Nam luôn đồng hành cùng bạn trên hành trình chăm sóc sức khỏe toàn diện. Hãy bắt đầu từ những thay đổi nhỏ mỗi ngày để có một cuộc sống "Sống khỏe - Sống đẹp - Sống hạnh phúc".</p>',
                'category_name' => 'Kiến thức sức khỏe',
                'category_slug' => 'kien-thuc-suc-khoe',
                'image' => 'posts/cham-soc-suc-khoe-phuong-dong.jpg',
                'status' => 'published',
                'is_featured' => false,
                'seo_title' => 'Bí quyết chăm sóc sức khỏe toàn diện theo triết lý phương Đông',
                'seo_description' => 'Khám phá cách chăm sóc sức khỏe toàn diện theo triết lý y học cổ truyền phương Đông, kết hợp lối sống hiện đại cho cuộc sống khỏe mạnh.',
                'seo_keywords' => 'chăm sóc sức khỏe, y học cổ truyền, âm dương, ngũ hành, kinh mạch, MIDSUN'
            ]
        ];
    }

    /**
     * Import một bài viết
     */
    private function importPost($data)
    {
        // Tìm hoặc tạo category
        $category = $this->getOrCreateCategory($data['category_name'], $data['category_slug']);
        
        // Tìm user để gán làm tác giả (hoặc tạo user mặc định)
        $author = User::first();
        if (!$author) {
            $author = User::create([
                'name' => 'Admin MIDSUN',
                'email' => '<EMAIL>',
                'password' => bcrypt('password123'),
                'email_verified_at' => now()
            ]);
        }

        // Chuẩn bị dữ liệu bài viết
        $postData = [
            'title' => $data['title'],
            'slug' => $data['slug'],
            'content' => $data['content'],
            'excerpt' => $data['excerpt'],
            'status' => $data['status'] ?? 'published',
            'is_featured' => $data['is_featured'] ?? false,
            'author_id' => $author->id,
            'seo_title' => $data['seo_title'] ?? null,
            'seo_description' => $data['seo_description'] ?? null,
            'seo_keywords' => $data['seo_keywords'] ?? null,
            'image' => $data['image'] ?? null,
            'published_at' => $data['status'] === 'published' ? now() : null,
        ];

        // Kiểm tra bài viết đã tồn tại chưa
        $existingPost = Post::where('slug', $postData['slug'])->first();
        
        if ($existingPost) {
            $existingPost->update($postData);
            // Cập nhật danh mục
            $existingPost->categories()->sync([$category->id => ['is_primary' => true]]);
            return 'updated';
        } else {
            $post = Post::create($postData);
            // Gán danh mục cho bài viết
            $post->categories()->attach($category->id, ['is_primary' => true]);
            return 'created';
        }
    }

    /**
     * Tìm hoặc tạo danh mục bài viết
     */
    private function getOrCreateCategory($name, $slug)
    {
        $category = Category::where('slug', $slug)->where('type', 'post')->first();
        
        if (!$category) {
            $category = Category::create([
                'name' => $name,
                'slug' => $slug,
                'description' => 'Danh mục ' . $name,
                'type' => 'post',
                'parent_id' => 0,
                'status' => 'active'
            ]);
        }
        
        return $category;
    }
} 