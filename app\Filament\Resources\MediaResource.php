<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MediaResource\Pages;
use App\Models\Media;
use App\Services\MediaService;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Filament\Support\Enums\ActionSize;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class MediaResource extends BaseResource
{
    protected static ?string $model = Media::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Quản lý Media';

    protected static ?string $navigationGroup = 'Hệ thống';

    protected static ?int $navigationSort = 90;
    
    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $modelLabel = 'Media';
    protected static ?string $pluralModelLabel = 'Media';
    protected static ?string $recordTitleAttribute = 'name';
    protected static ?string $recordRouteKeyName = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Chi tiết Media')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên')
                            ->required()
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('alt')
                            ->label('Alt text')
                            ->helperText('Văn bản thay thế cho ảnh khi không thể hiển thị')
                            ->maxLength(255),
                        
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->maxLength(255),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->maxLength(1000)
                            ->columnSpanFull(),
                        
                        Forms\Components\Placeholder::make('preview')
                            ->label('Xem trước')
                            ->content(fn ($record) => view('filament.components.media-preview', ['media' => $record]))
                            ->visible(fn ($record) => $record !== null)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail_url')
                    ->label('Ảnh')
                    ->square()
                    ->size(70)
                    ->defaultImageUrl(asset('images/placeholder.png')),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('mime_type')
                    ->label('Loại file')
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        str_contains($state, 'image/') => 'success',
                        str_contains($state, 'pdf') => 'danger',
                        str_contains($state, 'excel') || str_contains($state, 'spreadsheet') => 'warning',
                        str_contains($state, 'document') || str_contains($state, 'word') => 'info',
                        default => 'gray',
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('size')
                    ->label('Kích thước')
                    ->formatStateUsing(function (int $state) {
                        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
                        $size = $state;
                        $i = 0;
                        while ($size >= 1024 && $i < count($units) - 1) {
                            $size /= 1024;
                            $i++;
                        }
                        return round($size, 2) . ' ' . $units[$i];
                    })
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime()
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->iconButton()
                    ->modalWidth('md')
                    ->modalHeading(fn (Media $record) => "Xem chi tiết: {$record->name}"),
                
                Tables\Actions\EditAction::make()
                    ->iconButton()
                    ->modalWidth('md')
                    ->modalHeading(fn (Media $record) => "Chỉnh sửa: {$record->name}"),
                
                Tables\Actions\Action::make('download')
                    ->label('Tải xuống')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn (Model $record) => url('storage/' . $record->path))
                    ->openUrlInNewTab()
                    ->iconButton(),
                
                Tables\Actions\DeleteAction::make()
                    ->iconButton()
                    ->before(function (Media $record, MediaService $mediaService) {
                        Log::info('Đang xóa media từ danh sách', ['id' => $record->id, 'path' => $record->path]);
                        $mediaService->deleteFiles($record);
                    })
                    ->after(function ($livewire) {
                        // Cập nhật badge sau khi xóa
                        static::refreshNavigationBadge();
                        
                        // Chuyển hướng để làm mới trang
                        $livewire->redirect(static::getUrl('index'));
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function ($records, MediaService $mediaService) {
                            foreach ($records as $record) {
                                Log::info('Đang xóa media từ bulk action', ['id' => $record->id, 'path' => $record->path]);
                                $mediaService->deleteFiles($record);
                            }
                        })
                        ->after(function ($livewire) {
                            // Cập nhật badge sau khi xóa hàng loạt
                            static::refreshNavigationBadge();
                            
                            // Chuyển hướng để làm mới trang
                            $livewire->redirect(static::getUrl('index'));
                        }),
                ]),
            ])
            ->emptyStateHeading('Chưa có Media')
            ->emptyStateDescription('Bắt đầu bằng việc tải lên ảnh hoặc file.')
            ->emptyStateIcon('heroicon-o-photo');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMedia::route('/'),
        ];
    }
}