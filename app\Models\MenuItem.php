<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'menu_id',
        'parent_id',
        'title',
        'navigation_label',
        'url',
        'target',
        'icon',
        'class',
        'css_classes',
        'description',
        'order',
        'status',
        'type',
        'model_type',
        'model_id',
    ];

    protected $casts = [
        'status' => 'boolean',
        'order' => 'integer',
        'menu_id' => 'integer',
        'model_id' => 'integer',
    ];

    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(MenuItem::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(MenuItem::class, 'parent_id')->orderBy('order');
    }

    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function isActive(): bool
    {
        return request()->url() === $this->url;
    }

    public function getUrlAttribute($value)
    {
        if ($this->type === 'custom') {
            return $value;
        }

        if ($this->model_type && $this->model_id) {
            $model = $this->model;
            
            if ($model) {
                return $model->url ?? $value;
            }
        }

        return $value;
    }

    /**
     * Xử lý parent_id đặc biệt để hỗ trợ NULL
     *
     * @param  mixed  $value
     * @return void
     */
    public function setParentIdAttribute($value)
    {
        if ($value === null || $value === '' || $value === '0' || $value === 0) {
            $this->attributes['parent_id'] = null;
        } else {
            $this->attributes['parent_id'] = (int) $value;
        }
    }
}
