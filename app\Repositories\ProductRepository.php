<?php

namespace App\Repositories;

use App\Models\Product;

class ProductRepository
{
    protected $model;

    public function __construct(Product $model)
    {
        $this->model = $model;
    }

    /**
     * L<PERSON>y sản phẩm mới
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getNewProducts($limit = 8)
    {
        return $this->model->with(['category', 'brand'])
            ->where('is_new', true)
            ->where('status', true)
            ->latest()
            ->take($limit)
            ->get();
    }

    /**
     * L<PERSON>y sản phẩm bán chạy
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBestSellers($limit = 8)
    {
        return $this->model->with(['category', 'brand'])
            ->where('is_best_seller', true)
            ->where('status', true)
            ->latest()
            ->take($limit)
            ->get();
    }

    /**
     * <PERSON><PERSON><PERSON> sản phẩm nổi bật
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedProducts($limit = 8)
    {
        return $this->model->with(['category', 'brand'])
            ->where('is_featured', true)
            ->where('status', true)
            ->latest()
            ->take($limit)
            ->get();
    }

    /**
     * Lấy sản phẩm theo danh mục
     *
     * @param string $categorySlug
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProductsByCategory($categorySlug, $limit = 12)
    {
        return $this->model->with(['category', 'brand'])
            ->whereHas('category', function($query) use ($categorySlug) {
                $query->where('slug', $categorySlug);
            })
            ->where('status', true)
            ->latest()
            ->take($limit)
            ->get();
    }
} 