<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Filament\Notifications\Notification;
use App\Models\Permission as PermissionModel;

class RoleResource extends BaseResource
{
    protected static ?string $model = Role::class;
    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationGroup = 'Quản lý tài khoản';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationLabel = 'Vai trò';

    public static function form(Form $form): Form
    {
        // Get permissions grouped by their function
        $permissionGroups = PermissionModel::all()->groupBy('group');
        
        // Group translation mapping
        $groupLabels = [
            'admin' => 'Quản trị hệ thống',
            'user' => 'Quản lý người dùng',
            'role' => 'Quản lý vai trò',
            'permission' => 'Quản lý quyền hạn',
            'content' => 'Quản lý nội dung',
            'media' => 'Quản lý tập tin',
            'product' => 'Quản lý sản phẩm',
        ];
        
        // Build tab objects for each permission group
        $permissionTabs = [];
        
        foreach ($permissionGroups as $groupName => $permissions) {
            $permissionTabs[] = Forms\Components\Tabs\Tab::make($groupLabels[$groupName] ?? ucfirst($groupName))
                ->schema([
                    Forms\Components\CheckboxList::make("permissions_{$groupName}")
                        ->label(false)
                        ->options(function () use ($permissions) {
                            $options = [];
                            foreach ($permissions as $permission) {
                                $label = $permission->description ?? $permission->name;
                                if ($permission->description) {
                                    $label .= " ({$permission->name})";
                                }
                                $options[$permission->id] = $label;
                            }
                            return $options;
                        })
                        ->afterStateHydrated(function ($component, $state, $record) use ($groupName) {
                            if (!$record) return;
                            
                            $permissionIds = $record->permissions()
                                ->where('group', $groupName)
                                ->pluck('id')
                                ->toArray();
                                
                            $component->state($permissionIds);
                        })
                        ->saveRelationshipsUsing(function ($record, $state) use ($groupName) {
                            // Get all current permissions from other groups
                            $otherGroups = $record->permissions()
                                ->where('group', '!=', $groupName)
                                ->pluck('id')
                                ->toArray();
                                
                            // Combine with newly selected permissions from this group
                            $allPermissions = array_merge($otherGroups, $state ?: []);
                            
                            // Sync all permissions
                            $record->permissions()->sync($allPermissions);
                        })
                        ->bulkToggleable()
                        ->columns(1)
                        ->disabled(fn (?Model $record) => $record && $record->id === 1)
                ]);
        }

        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin vai trò')
                    ->schema([
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tên vai trò')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->disabled(fn (?Model $record) => $record && $record->id === 1)
                                    ->columnSpan(['lg' => 1]),
                                Forms\Components\Textarea::make('description')
                                    ->label('Mô tả')
                                    ->rows(2)
                                    ->columnSpan(['lg' => 1]),
                                Forms\Components\Hidden::make('guard_name')
                                    ->default('web'),
                            ])
                            ->columns(2),
                    ]),
                    
                Forms\Components\Section::make('Phân quyền')
                    ->schema([
                        Forms\Components\Tabs::make('Permission Groups')
                            ->tabs($permissionTabs),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Vai trò')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('permissions_count')
                    ->label('Số quyền')
                    ->counts('permissions')
                    ->sortable(),
                Tables\Columns\TextColumn::make('users_count')
                    ->label('Số người dùng')
                    ->counts('users')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime(Config::get('app.displayFormat', 'd/m/Y H:i'))
                    ->sortable(),
            ])
            ->filters([
                // 
            ])
            ->actions([
                ViewAction::make()->modalWidth('7xl'),
                EditAction::make()
                    ->modalWidth('7xl')
                    ->hidden(fn (Model $record): bool => $record->id === 1),
                DeleteAction::make()
                    ->hidden(fn (Model $record): bool => 
                        in_array($record->name, ['admin', 'Super Admin']) || 
                        $record->id === 1 || 
                        $record->id === 2
                    )
                    ->before(function (Model $record) {
                        if (in_array($record->name, ['admin', 'Super Admin']) || 
                            $record->id === 1 || 
                            $record->id === 2) {
                            Notification::make()
                                ->warning()
                                ->title('Vai trò hệ thống')
                                ->body('Không thể xóa vai trò hệ thống.')
                                ->send();
                            
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Collection $records) {
                            $systemRoles = $records->filter(fn ($record) => 
                                in_array($record->name, ['admin', 'Super Admin']) || 
                                $record->id === 1 || 
                                $record->id === 2
                            );
                            
                            if ($systemRoles->count()) {
                                Notification::make()
                                    ->warning()
                                    ->title('Vai trò hệ thống')
                                    ->body('Không thể xóa vai trò hệ thống.')
                                    ->send();
                                
                                return $records->filter(fn ($record) => 
                                    !(in_array($record->name, ['admin', 'Super Admin']) || 
                                    $record->id === 1 || 
                                    $record->id === 2)
                                );
                            }
                        }),
                ]),
            ])
            ->headerActions([
                CreateAction::make()->modalWidth('7xl'),
            ])
            ->recordAction(null);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/')
        ];
    }
}
