<?php

namespace Database\Seeders;

use App\Models\Banner;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BannerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // PHẦN 1: Tạo banner vị trí slider
        $sliderPosition = 'slider';
        
        // Danh sách slider từ ảnh có sẵn trong storage/banners
        $sliders = [
            [
                'name' => 'Chất lượng',
                'image' => 'banners/slider1.jpg',
                'link' => '/gioi-thieu',
                'order' => 1,
                'description' => 'Cung cấp sản phẩm<br>Chất lượng hàng đầu'
            ],
            [
                'name' => 'Trách nhiệm',
                'image' => 'banners/slider2.jpg',
                'link' => '/dich-vu.html',
                'order' => 2,
                'description' => 'Trách Nghiệm<br>Cộng đồng - Môi <PERSON>rườ<PERSON>'
            ],
            [
                'name' => 'Trung thực',
                'image' => 'banners/slider3.jpg',
                'link' => '/san-pham.html',
                'order' => 3,
                'description' => '<PERSON> bạch<br>Toàn diện - Hiện đại'
            ]
        ];
        
        foreach ($sliders as $slider) {
            Banner::updateOrCreate(
                [
                    'position' => $sliderPosition,
                    'name' => $slider['name']
                ],
                [
                    'type' => 'slider',
                    'slug' => Str::slug($slider['name']),
                    'image' => $slider['image'],
                    'url' => $slider['link'],
                    'order' => $slider['order'],
                    'status' => true,
                    'description' => $slider['description']
                ]
            );
        }
        
        // PHẦN 2: Tạo banner vị trí partner
        $partnerPosition = 'partner';
        
        // Danh sách đối tác
        $partners = [
            [
                'name' => 'Đối tác 1',
                'image' => 'banners/partner1.png',
                'link' => '#',
                'order' => 1
            ],
            [
                'name' => 'Đối tác 2',
                'image' => 'banners/partner2.png',
                'link' => '#',
                'order' => 2
            ],
            [
                'name' => 'Đối tác 3',
                'image' => 'banners/partner3.png',
                'link' => '#',
                'order' => 3
            ],
            [
                'name' => 'Đối tác 4',
                'image' => 'banners/partner4.png',
                'link' => '#',
                'order' => 4
            ]
        ];
        
        foreach ($partners as $partner) {
            Banner::updateOrCreate(
                [
                    'position' => $partnerPosition,
                    'name' => $partner['name']
                ],
                [
                    'type' => 'partner',
                    'slug' => Str::slug($partner['name']),
                    'image' => $partner['image'],
                    'url' => $partner['link'],
                    'order' => $partner['order'],
                    'status' => true,
                    'description' => 'Đối tác ' . $partner['name']
                ]
            );
        }
    }
} 