<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email : Email để nhận thư test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gửi email thử nghiệm để kiểm tra cấu hình SMTP';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $recipient = $this->argument('email');
        $this->info("Đang gửi email test đến: {$recipient}");

        try {
            Mail::raw('<PERSON><PERSON><PERSON> là email thử nghiệm từ hệ thống Midsun Việt Nam. Nếu bạn nhận được email này, cấu hình SMTP đã hoạt động tốt!', function ($message) use ($recipient) {
                $message->to($recipient)
                        ->subject('Test Email từ Midsun Việt Nam');
            });

            $this->info('Email đã được gửi thành công!');
        } catch (\Exception $e) {
            $this->error('Gửi email thất bại: ' . $e->getMessage());
        }

        return Command::SUCCESS;
    }
}
