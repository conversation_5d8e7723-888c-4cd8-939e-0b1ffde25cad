{"__meta": {"id": "01K0930NJJ9YMKM6HJ3WFCZGBE", "datetime": "2025-07-16 14:43:34", "utime": **********.482975, "method": "GET", "uri": "/admin", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.500564, "end": **********.483005, "duration": 22.982440948486328, "duration_str": "22.98s", "measures": [{"label": "Booting", "start": **********.500564, "relative_start": 0, "end": **********.971985, "relative_end": **********.971985, "duration": 0.****************, "duration_str": "471ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.971996, "relative_start": 0.****************, "end": **********.483007, "relative_end": 1.9073486328125e-06, "duration": 22.***************, "duration_str": "22.51s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.326784, "relative_start": 0.****************, "end": **********.332892, "relative_end": **********.332892, "duration": 0.*****************, "duration_str": "6.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::pages.dashboard", "start": **********.476707, "relative_start": 0.****************, "end": **********.476707, "relative_end": **********.476707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::widgets.account-widget", "start": **********.987963, "relative_start": 1.****************, "end": **********.987963, "relative_end": **********.987963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.639986, "relative_start": 2.****************, "end": **********.639986, "relative_end": **********.639986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.013339, "relative_start": 2.**************, "end": **********.013339, "relative_end": **********.013339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.079232, "relative_start": 2.****************, "end": **********.079232, "relative_end": **********.079232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651795.087501, "relative_start": 3.5869369506835938, "end": 1752651795.087501, "relative_end": 1752651795.087501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651795.721737, "relative_start": 4.22117280960083, "end": 1752651795.721737, "relative_end": 1752651795.721737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651795.935287, "relative_start": 4.434722900390625, "end": 1752651795.935287, "relative_end": 1752651795.935287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.index", "start": 1752651795.936018, "relative_start": 4.43545389175415, "end": 1752651795.936018, "relative_end": 1752651795.936018, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651797.545129, "relative_start": 6.044564962387085, "end": 1752651797.545129, "relative_end": 1752651797.545129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::widgets.filament-info-widget", "start": 1752651797.613826, "relative_start": 6.113261938095093, "end": 1752651797.613826, "relative_end": 1752651797.613826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651798.12274, "relative_start": 6.622175931930542, "end": 1752651798.12274, "relative_end": 1752651798.12274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651799.280095, "relative_start": 7.779531002044678, "end": 1752651799.280095, "relative_end": 1752651799.280095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.link", "start": 1752651799.289286, "relative_start": 7.788721799850464, "end": 1752651799.289286, "relative_end": 1752651799.289286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651799.290994, "relative_start": 7.7904298305511475, "end": 1752651799.290994, "relative_end": 1752651799.290994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.index", "start": 1752651799.291362, "relative_start": 7.79079794883728, "end": 1752651799.291362, "relative_end": 1752651799.291362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651799.29288, "relative_start": 7.79231595993042, "end": 1752651799.29288, "relative_end": 1752651799.29288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-section", "start": 1752651799.297129, "relative_start": 7.796564817428589, "end": 1752651799.297129, "relative_end": 1752651799.297129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.index", "start": 1752651799.569763, "relative_start": 8.069198846817017, "end": 1752651799.569763, "relative_end": 1752651799.569763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651799.571265, "relative_start": 8.070700883865356, "end": 1752651799.571265, "relative_end": 1752651799.571265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-section", "start": 1752651799.572904, "relative_start": 8.07234001159668, "end": 1752651799.572904, "relative_end": 1752651799.572904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.index", "start": 1752651799.573703, "relative_start": 8.073138952255249, "end": 1752651799.573703, "relative_end": 1752651799.573703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651799.574924, "relative_start": 8.074359893798828, "end": 1752651799.574924, "relative_end": 1752651799.574924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-section", "start": 1752651799.576215, "relative_start": 8.075650930404663, "end": 1752651799.576215, "relative_end": 1752651799.576215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.index", "start": 1752651799.576674, "relative_start": 8.076109886169434, "end": 1752651799.576674, "relative_end": 1752651799.576674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752651799.577528, "relative_start": 8.076963901519775, "end": 1752651799.577528, "relative_end": 1752651799.577528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": 1752651799.578794, "relative_start": 8.078229904174805, "end": 1752651799.578794, "relative_end": 1752651799.578794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": 1752651799.617915, "relative_start": 8.117350816726685, "end": 1752651799.617915, "relative_end": 1752651799.617915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": 1752651801.045909, "relative_start": 9.545344829559326, "end": 1752651801.045909, "relative_end": 1752651801.045909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651802.156261, "relative_start": 10.655696868896484, "end": 1752651802.156261, "relative_end": 1752651802.156261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651802.74627, "relative_start": 11.245705842971802, "end": 1752651802.74627, "relative_end": 1752651802.74627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752651802.747605, "relative_start": 11.24704098701477, "end": 1752651802.747605, "relative_end": 1752651802.747605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": 1752651802.748963, "relative_start": 11.248399019241333, "end": 1752651802.748963, "relative_end": 1752651802.748963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1752651802.826871, "relative_start": 11.326306819915771, "end": 1752651802.826871, "relative_end": 1752651802.826871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": 1752651802.902959, "relative_start": 11.402395009994507, "end": 1752651802.902959, "relative_end": 1752651802.902959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": 1752651803.3921, "relative_start": 11.891535997390747, "end": 1752651803.3921, "relative_end": 1752651803.3921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651805.302153, "relative_start": 13.801589012145996, "end": 1752651805.302153, "relative_end": 1752651805.302153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651805.303823, "relative_start": 13.803258895874023, "end": 1752651805.303823, "relative_end": 1752651805.303823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651805.314105, "relative_start": 13.813540935516357, "end": 1752651805.314105, "relative_end": 1752651805.314105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651805.315638, "relative_start": 13.81507396697998, "end": 1752651805.315638, "relative_end": 1752651805.315638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.index", "start": 1752651805.366169, "relative_start": 13.865604877471924, "end": 1752651805.366169, "relative_end": 1752651805.366169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.field", "start": 1752651805.668085, "relative_start": 14.167520999908447, "end": 1752651805.668085, "relative_end": 1752651805.668085, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": 1752651806.05405, "relative_start": 14.553485870361328, "end": 1752651806.05405, "relative_end": 1752651806.05405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752651806.109491, "relative_start": 14.608927011489868, "end": 1752651806.109491, "relative_end": 1752651806.109491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651806.790061, "relative_start": 15.289496898651123, "end": 1752651806.790061, "relative_end": 1752651806.790061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752651806.800014, "relative_start": 15.299449920654297, "end": 1752651806.800014, "relative_end": 1752651806.800014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fc6af43a80d1f8feb6964b2b41596895", "start": 1752651806.833232, "relative_start": 15.332667827606201, "end": 1752651806.833232, "relative_end": 1752651806.833232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": 1752651806.904567, "relative_start": 15.404002904891968, "end": 1752651806.904567, "relative_end": 1752651806.904567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": 1752651807.008442, "relative_start": 15.507877826690674, "end": 1752651807.008442, "relative_end": 1752651807.008442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": 1752651808.512769, "relative_start": 17.012204885482788, "end": 1752651808.512769, "relative_end": 1752651808.512769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": 1752651808.513644, "relative_start": 17.01307988166809, "end": 1752651808.513644, "relative_end": 1752651808.513644, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": 1752651808.51436, "relative_start": 17.013795852661133, "end": 1752651808.51436, "relative_end": 1752651808.51436, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651808.750116, "relative_start": 17.249552011489868, "end": 1752651808.750116, "relative_end": 1752651808.750116, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": 1752651808.760851, "relative_start": 17.260286808013916, "end": 1752651808.760851, "relative_end": 1752651808.760851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651809.152977, "relative_start": 17.65241289138794, "end": 1752651809.152977, "relative_end": 1752651809.152977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651809.381705, "relative_start": 17.88114094734192, "end": 1752651809.381705, "relative_end": 1752651809.381705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651809.392668, "relative_start": 17.892103910446167, "end": 1752651809.392668, "relative_end": 1752651809.392668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651809.393546, "relative_start": 17.892982006072998, "end": 1752651809.393546, "relative_end": 1752651809.393546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": 1752651809.402594, "relative_start": 17.902029991149902, "end": 1752651809.402594, "relative_end": 1752651809.402594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651809.403209, "relative_start": 17.902644872665405, "end": 1752651809.403209, "relative_end": 1752651809.403209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": 1752651809.414216, "relative_start": 17.913651943206787, "end": 1752651809.414216, "relative_end": 1752651809.414216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": 1752651809.460199, "relative_start": 17.959635019302368, "end": 1752651809.460199, "relative_end": 1752651809.460199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651810.58453, "relative_start": 19.08396601676941, "end": 1752651810.58453, "relative_end": 1752651810.58453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": 1752651810.588466, "relative_start": 19.087901830673218, "end": 1752651810.588466, "relative_end": 1752651810.588466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": 1752651810.589662, "relative_start": 19.08909797668457, "end": 1752651810.589662, "relative_end": 1752651810.589662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": 1752651810.80526, "relative_start": 19.30469584465027, "end": 1752651810.80526, "relative_end": 1752651810.80526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": 1752651811.719893, "relative_start": 20.21932888031006, "end": 1752651811.719893, "relative_end": 1752651811.719893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651811.811265, "relative_start": 20.3107008934021, "end": 1752651811.811265, "relative_end": 1752651811.811265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.021733, "relative_start": 21.52116894721985, "end": 1752651813.021733, "relative_end": 1752651813.021733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.509541, "relative_start": 22.008976936340332, "end": 1752651813.509541, "relative_end": 1752651813.509541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.518408, "relative_start": 22.0178439617157, "end": 1752651813.518408, "relative_end": 1752651813.518408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.519882, "relative_start": 22.019317865371704, "end": 1752651813.519882, "relative_end": 1752651813.519882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.520869, "relative_start": 22.020304918289185, "end": 1752651813.520869, "relative_end": 1752651813.520869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.529008, "relative_start": 22.028443813323975, "end": 1752651813.529008, "relative_end": 1752651813.529008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.530396, "relative_start": 22.029831886291504, "end": 1752651813.530396, "relative_end": 1752651813.530396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.541054, "relative_start": 22.04048991203308, "end": 1752651813.541054, "relative_end": 1752651813.541054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.54242, "relative_start": 22.041855812072754, "end": 1752651813.54242, "relative_end": 1752651813.54242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.543406, "relative_start": 22.042841911315918, "end": 1752651813.543406, "relative_end": 1752651813.543406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.543886, "relative_start": 22.04332184791565, "end": 1752651813.543886, "relative_end": 1752651813.543886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.544566, "relative_start": 22.044001817703247, "end": 1752651813.544566, "relative_end": 1752651813.544566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.552767, "relative_start": 22.052202939987183, "end": 1752651813.552767, "relative_end": 1752651813.552767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.553728, "relative_start": 22.05316400527954, "end": 1752651813.553728, "relative_end": 1752651813.553728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.562667, "relative_start": 22.062102794647217, "end": 1752651813.562667, "relative_end": 1752651813.562667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.563482, "relative_start": 22.062917947769165, "end": 1752651813.563482, "relative_end": 1752651813.563482, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.571425, "relative_start": 22.070860862731934, "end": 1752651813.571425, "relative_end": 1752651813.571425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.57246, "relative_start": 22.071895837783813, "end": 1752651813.57246, "relative_end": 1752651813.57246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.581048, "relative_start": 22.08048391342163, "end": 1752651813.581048, "relative_end": 1752651813.581048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.581849, "relative_start": 22.081284999847412, "end": 1752651813.581849, "relative_end": 1752651813.581849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.582399, "relative_start": 22.08183479309082, "end": 1752651813.582399, "relative_end": 1752651813.582399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.583522, "relative_start": 22.082957983016968, "end": 1752651813.583522, "relative_end": 1752651813.583522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.584622, "relative_start": 22.084057807922363, "end": 1752651813.584622, "relative_end": 1752651813.584622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.585093, "relative_start": 22.084528923034668, "end": 1752651813.585093, "relative_end": 1752651813.585093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.586023, "relative_start": 22.085458993911743, "end": 1752651813.586023, "relative_end": 1752651813.586023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.595824, "relative_start": 22.09525990486145, "end": 1752651813.595824, "relative_end": 1752651813.595824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.597111, "relative_start": 22.09654688835144, "end": 1752651813.597111, "relative_end": 1752651813.597111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.598173, "relative_start": 22.09760880470276, "end": 1752651813.598173, "relative_end": 1752651813.598173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.59863, "relative_start": 22.098065853118896, "end": 1752651813.59863, "relative_end": 1752651813.59863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.599429, "relative_start": 22.098864793777466, "end": 1752651813.599429, "relative_end": 1752651813.599429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.608104, "relative_start": 22.107539892196655, "end": 1752651813.608104, "relative_end": 1752651813.608104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.608937, "relative_start": 22.108372926712036, "end": 1752651813.608937, "relative_end": 1752651813.608937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.617208, "relative_start": 22.11664390563965, "end": 1752651813.617208, "relative_end": 1752651813.617208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.617992, "relative_start": 22.117427825927734, "end": 1752651813.617992, "relative_end": 1752651813.617992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.626278, "relative_start": 22.12571382522583, "end": 1752651813.626278, "relative_end": 1752651813.626278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.627209, "relative_start": 22.12664484977722, "end": 1752651813.627209, "relative_end": 1752651813.627209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.636337, "relative_start": 22.135772943496704, "end": 1752651813.636337, "relative_end": 1752651813.636337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.637449, "relative_start": 22.136884927749634, "end": 1752651813.637449, "relative_end": 1752651813.637449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.638475, "relative_start": 22.137910842895508, "end": 1752651813.638475, "relative_end": 1752651813.638475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.639249, "relative_start": 22.13868498802185, "end": 1752651813.639249, "relative_end": 1752651813.639249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.640069, "relative_start": 22.13950490951538, "end": 1752651813.640069, "relative_end": 1752651813.640069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.649108, "relative_start": 22.14854383468628, "end": 1752651813.649108, "relative_end": 1752651813.649108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.649854, "relative_start": 22.149289846420288, "end": 1752651813.649854, "relative_end": 1752651813.649854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.658685, "relative_start": 22.15812087059021, "end": 1752651813.658685, "relative_end": 1752651813.658685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.65971, "relative_start": 22.159145832061768, "end": 1752651813.65971, "relative_end": 1752651813.65971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.669342, "relative_start": 22.16877794265747, "end": 1752651813.669342, "relative_end": 1752651813.669342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.670813, "relative_start": 22.170248985290527, "end": 1752651813.670813, "relative_end": 1752651813.670813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.672311, "relative_start": 22.171746969223022, "end": 1752651813.672311, "relative_end": 1752651813.672311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.673065, "relative_start": 22.17250084877014, "end": 1752651813.673065, "relative_end": 1752651813.673065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.674041, "relative_start": 22.173476934432983, "end": 1752651813.674041, "relative_end": 1752651813.674041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.684579, "relative_start": 22.184014797210693, "end": 1752651813.684579, "relative_end": 1752651813.684579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.685706, "relative_start": 22.185141801834106, "end": 1752651813.685706, "relative_end": 1752651813.685706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.694838, "relative_start": 22.194273948669434, "end": 1752651813.694838, "relative_end": 1752651813.694838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.695954, "relative_start": 22.195389986038208, "end": 1752651813.695954, "relative_end": 1752651813.695954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.697012, "relative_start": 22.19644784927368, "end": 1752651813.697012, "relative_end": 1752651813.697012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.697414, "relative_start": 22.196849822998047, "end": 1752651813.697414, "relative_end": 1752651813.697414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.698046, "relative_start": 22.197481870651245, "end": 1752651813.698046, "relative_end": 1752651813.698046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.706858, "relative_start": 22.20629382133484, "end": 1752651813.706858, "relative_end": 1752651813.706858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.707727, "relative_start": 22.207162857055664, "end": 1752651813.707727, "relative_end": 1752651813.707727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": 1752651813.71678, "relative_start": 22.21621584892273, "end": 1752651813.71678, "relative_end": 1752651813.71678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752651813.718353, "relative_start": 22.21778893470764, "end": 1752651813.718353, "relative_end": 1752651813.718353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.71975, "relative_start": 22.219185829162598, "end": 1752651813.71975, "relative_end": 1752651813.71975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.720419, "relative_start": 22.219854831695557, "end": 1752651813.720419, "relative_end": 1752651813.720419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.72137, "relative_start": 22.220805883407593, "end": 1752651813.72137, "relative_end": 1752651813.72137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": 1752651813.730101, "relative_start": 22.22953701019287, "end": 1752651813.730101, "relative_end": 1752651813.730101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752651813.731007, "relative_start": 22.230443000793457, "end": 1752651813.731007, "relative_end": 1752651813.731007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": 1752651813.740822, "relative_start": 22.24025797843933, "end": 1752651813.740822, "relative_end": 1752651813.740822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.035051, "relative_start": 22.534487009048462, "end": **********.035051, "relative_end": **********.035051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27df4909b5b8fbf02f3a65ba2f758414", "start": **********.131545, "relative_start": 22.630980968475342, "end": **********.131545, "relative_end": **********.131545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.418732, "relative_start": 22.91816782951355, "end": **********.418732, "relative_end": **********.418732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97c5322e947dbf60ae5ca480928dd519", "start": **********.419663, "relative_start": 22.91909885406494, "end": **********.419663, "relative_end": **********.419663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.478795, "relative_start": 22.978230953216553, "end": **********.478947, "relative_end": **********.478947, "duration": 0.0001518726348876953, "duration_str": "152μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.480579, "relative_start": 22.98001480102539, "end": **********.480642, "relative_end": **********.480642, "duration": 6.318092346191406e-05, "duration_str": "63μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 44138552, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 139, "nb_templates": 139, "templates": [{"name": "1x filament-panels::pages.dashboard", "param_count": null, "params": [], "start": **********.476683, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/pages/dashboard.blade.phpfilament-panels::pages.dashboard", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fpages%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::pages.dashboard"}, {"name": "1x filament-panels::widgets.account-widget", "param_count": null, "params": [], "start": **********.987941, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/widgets/account-widget.blade.phpfilament-panels::widgets.account-widget", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fwidgets%2Faccount-widget.blade.php&line=1", "ajax": false, "filename": "account-widget.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::widgets.account-widget"}, {"name": "2x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.639958, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament-panels::components.avatar.user"}, {"name": "2x filament::components.avatar", "param_count": null, "params": [], "start": **********.013316, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.button.index", "param_count": null, "params": [], "start": **********.079205, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.button.index"}, {"name": "11x filament::components.icon-button", "param_count": null, "params": [], "start": 1752651795.087475, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 11, "name_original": "filament::components.icon-button"}, {"name": "41x filament::components.icon", "param_count": null, "params": [], "start": 1752651795.721708, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 41, "name_original": "filament::components.icon"}, {"name": "5x filament::components.section.index", "param_count": null, "params": [], "start": 1752651795.935996, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.section.index"}, {"name": "5x filament::components.grid.column", "param_count": null, "params": [], "start": 1752651797.545089, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}, "render_count": 5, "name_original": "filament::components.grid.column"}, {"name": "1x filament-panels::widgets.filament-info-widget", "param_count": null, "params": [], "start": 1752651797.613808, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/widgets/filament-info-widget.blade.phpfilament-panels::widgets.filament-info-widget", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fwidgets%2Ffilament-info-widget.blade.php&line=1", "ajax": false, "filename": "filament-info-widget.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::widgets.filament-info-widget"}, {"name": "2x filament::components.link", "param_count": null, "params": [], "start": 1752651798.122711, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/link.blade.phpfilament::components.link", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Flink.blade.php&line=1", "ajax": false, "filename": "link.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.link"}, {"name": "3x filament::components.loading-section", "param_count": null, "params": [], "start": 1752651799.297109, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-section.blade.phpfilament::components.loading-section", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-section.blade.php&line=1", "ajax": false, "filename": "loading-section.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.loading-section"}, {"name": "1x filament::components.grid.index", "param_count": null, "params": [], "start": 1752651799.578773, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.grid.index"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": 1752651799.617888, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": 1752651801.045887, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "3x filament::components.modal.index", "param_count": null, "params": [], "start": 1752651802.156233, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament::components.modal.index"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": 1752651802.748936, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1752651802.826844, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": 1752651802.902929, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": 1752651803.392079, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x filament-panels::components.global-search.index", "param_count": null, "params": [], "start": 1752651805.366146, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/index.blade.phpfilament-panels::components.global-search.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.index"}, {"name": "1x filament-panels::components.global-search.field", "param_count": null, "params": [], "start": 1752651805.668054, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/field.blade.phpfilament-panels::components.global-search.field", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.field"}, {"name": "1x filament::components.input.index", "param_count": null, "params": [], "start": 1752651806.054029, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.index"}, {"name": "1x filament::components.input.wrapper", "param_count": null, "params": [], "start": 1752651806.109451, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.wrapper"}, {"name": "1x filament::components.loading-indicator", "param_count": null, "params": [], "start": 1752651806.799986, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.loading-indicator"}, {"name": "1x __components::fc6af43a80d1f8feb6964b2b41596895", "param_count": null, "params": [], "start": 1752651806.83321, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/fc6af43a80d1f8feb6964b2b41596895.blade.php__components::fc6af43a80d1f8feb6964b2b41596895", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Ffc6af43a80d1f8feb6964b2b41596895.blade.php&line=1", "ajax": false, "filename": "fc6af43a80d1f8feb6964b2b41596895.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fc6af43a80d1f8feb6964b2b41596895"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": 1752651806.904537, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": 1752651807.00842, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": 1752651808.514315, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": 1752651808.76082, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": 1752651809.152947, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": 1752651809.414188, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "1x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": 1752651809.46017, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament::components.dropdown.index", "param_count": null, "params": [], "start": 1752651810.589615, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.index"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": 1752651810.80521, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.logo", "param_count": null, "params": [], "start": 1752651811.719867, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.logo"}, {"name": "9x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": 1752651811.811246, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 9, "name_original": "filament-panels::components.sidebar.group"}, {"name": "21x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": 1752651813.02171, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 21, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": 1752651813.740798, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.035023, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::27df4909b5b8fbf02f3a65ba2f758414", "param_count": null, "params": [], "start": **********.131517, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/27df4909b5b8fbf02f3a65ba2f758414.blade.php__components::27df4909b5b8fbf02f3a65ba2f758414", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F27df4909b5b8fbf02f3a65ba2f758414.blade.php&line=1", "ajax": false, "filename": "27df4909b5b8fbf02f3a65ba2f758414.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27df4909b5b8fbf02f3a65ba2f758414"}, {"name": "1x __components::97c5322e947dbf60ae5ca480928dd519", "param_count": null, "params": [], "start": **********.419645, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/97c5322e947dbf60ae5ca480928dd519.blade.php__components::97c5322e947dbf60ae5ca480928dd519", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F97c5322e947dbf60ae5ca480928dd519.blade.php&line=1", "ajax": false, "filename": "97c5322e947dbf60ae5ca480928dd519.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97c5322e947dbf60ae5ca480928dd519"}]}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00893, "accumulated_duration_str": "8.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.414616, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 32.699}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/BaseResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\BaseResource.php", "line": 14}, {"index": 21, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/filament/filament/src/Panel/Concerns/HasNavigation.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasNavigation.php", "line": 101}], "start": 1752651803.366531, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "BaseResource.php:14", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/BaseResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\BaseResource.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FBaseResource.php&line=14", "ajax": false, "filename": "BaseResource.php", "line": "14"}, "connection": "auvista", "explain": null, "start_percent": 32.699, "width_percent": 17.805}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1752651803.37339, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 50.504, "width_percent": 10.75}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": 1752651803.377645, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 61.254, "width_percent": 18.701}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = 'new'", "type": "query", "params": [], "bindings": ["new"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1752651803.385952, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ContactFormResource.php:225", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FContactFormResource.php&line=225", "ajax": false, "filename": "ContactFormResource.php", "line": "225"}, "connection": "auvista", "explain": null, "start_percent": 79.955, "width_percent": 9.071}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = 'new'", "type": "query", "params": [], "bindings": ["new"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 171}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 156}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 141}, {"index": 20, "namespace": null, "name": "vendor/filament/filament/src/Navigation/NavigationManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Navigation\\NavigationManager.php", "line": 50}], "start": 1752651811.714283, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ContactFormResource.php:225", "source": {"index": 16, "namespace": null, "name": "app/Filament/Resources/ContactFormResource.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\ContactFormResource.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FContactFormResource.php&line=225", "ajax": false, "filename": "ContactFormResource.php", "line": "225"}, "connection": "auvista", "explain": null, "start_percent": 89.026, "width_percent": 10.974}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 91, "is_counter": true}, "livewire": {"data": {"filament.pages.dashboard #e310WKUGGdJ2SJ882N1C": "array:4 [\n  \"data\" => array:15 [\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"filament.pages.dashboard\"\n  \"component\" => \"Filament\\Pages\\Dashboard\"\n  \"id\" => \"e310WKUGGdJ2SJ882N1C\"\n]", "filament.widgets.account-widget #y4D9VpxPijicHvpaOQl7": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.account-widget\"\n  \"component\" => \"Filament\\Widgets\\AccountWidget\"\n  \"id\" => \"y4D9VpxPijicHvpaOQl7\"\n]", "filament.widgets.filament-info-widget #mUT0meY38ElZF1GBHARE": "array:4 [\n  \"data\" => []\n  \"name\" => \"filament.widgets.filament-info-widget\"\n  \"component\" => \"Filament\\Widgets\\FilamentInfoWidget\"\n  \"id\" => \"mUT0meY38ElZF1GBHARE\"\n]", "filament.livewire.global-search #Vwgdnw1dw1L2j4c4fROh": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"Vwgdnw1dw1L2j4c4fROh\"\n]", "language-switcher #MkjrQKwBGbLGc2XXoyBj": "array:4 [\n  \"data\" => []\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"MkjrQKwBGbLGc2XXoyBj\"\n]", "filament.livewire.notifications #yCemzTRRcdB9rVTgprKE": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2509\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"yCemzTRRcdB9rVTgprKE\"\n]"}, "count": 6}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard", "uri": "GET admin", "controller": "Filament\\Pages\\Dashboard@render<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\LogLogoutMiddleware, App\\Http\\Middleware\\LocaleMiddleware, Filament\\Http\\Middleware\\Authenticate", "duration": "22.98s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ims0WVVyWGNObnZIaW5LM0VGMjdGdUE9PSIsInZhbHVlIjoiYXBpckxpN3FpaTlUV1V6cjAyYlYvdUlxV3FkR3djc0kyQXpHcUtnbHhzSnB6ZGVCVjEwMHRQdHVQR0o2U1pBdENITThTVWlvZWlWL0NiaTNuc1NlZHZkYjA4QzVQR0ovam5sTFd2MXhGVWdQRW91MGpUN3RrL0Z4UWNTQU1mS20iLCJtYWMiOiI3YjE0ZGFiZWU2OTI3Yzk2MzI4MzRkYjkwZGIzNzhhNmJkNzhjMWUwYWU1NTg0MjJjODM0Y2I1MjI2N2FkMTdmIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ik1PdEhRTFlaejZ4bDB4U1p0bjNNWUE9PSIsInZhbHVlIjoiYmI2eldxWUt6QUxTWmFyRGZtZXJTdW1xQnk3eGhRMHdSL2QvaWo0cHV5ZmFDMW82SHF3eGJTemFETTRuOGxQajJKTUFnRzlGUHE1YVkzRHZsVEtaQ3ZSMmhmcmlSenpZOVVMakFsMDVaV3o0QldtaktjN3ErL3lGYlJ2eG5LWmwiLCJtYWMiOiJkMTU2ZjZiNjgzZWU5MGUxM2ZjMjYxNGJhNjFmZmZkNTEwM2NkNGMxMTU3M2YzMDA0YTVhNDg4MzVjNDFiM2M5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1527580655 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxYEiPXdkNcyfymjyeN8wk4RbgrpbsugedMWghRH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1527580655\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1146089799 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:43:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkdUNW1nRmUzVDhTNFVYTGJwVFRqWlE9PSIsInZhbHVlIjoia2lNOHNsNWU1N21uM0hZSjFqTmIraUEzRnpBR01xLzFwdm91TXB5MVgySWdqTSt3dVVxZ2JPK3lGZ280aWRNVmN3RUdVT2o1cG1ET1dpbHEwNGViWlhPVitnUXVaUk41NmswUS9POTd2UnhqNmZ1eC9BSE9peUlSeisrNkFIZ1MiLCJtYWMiOiJiOGQ1NjRiMTQ3MjBhZDY1YzMwZDk1N2U4OWIwYmYwOWQyMjBiMGJiOGIyOGZhYTI5ZGMxOThiZDAwNTc4NjNjIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 09:43:34 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InVmL1FSSUdOb3Z3ODRmNUJiSkZnVkE9PSIsInZhbHVlIjoiOXBFUVBHM3oxZ3M5Z0FBb3RYTUZ4VnpSZ0gvMEJWVURPWlhhSGJpbS9IT0wrSDRMY1ArK2pZT21uRlpjZDA2WTI0UlJLWER2U3Bvb1Z5U0JpMWFCdDI1Z3k0MmNJSFBobHlOQ0JhVzRmZXA5REN6ajJaRTdobzh6UHdnSXhFU3UiLCJtYWMiOiJmMzg3ZmFhYzg3MGQ4NzU1OGI3NmRlNGYzNTcyY2Y2NDA5YjQwMGM1NGVlY2Y4MGVkZTJlYTFmNzRmNTcxZGIyIiwidGFnIjoiIn0%3D; expires=Wed, 16 Jul 2025 09:43:34 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkdUNW1nRmUzVDhTNFVYTGJwVFRqWlE9PSIsInZhbHVlIjoia2lNOHNsNWU1N21uM0hZSjFqTmIraUEzRnpBR01xLzFwdm91TXB5MVgySWdqTSt3dVVxZ2JPK3lGZ280aWRNVmN3RUdVT2o1cG1ET1dpbHEwNGViWlhPVitnUXVaUk41NmswUS9POTd2UnhqNmZ1eC9BSE9peUlSeisrNkFIZ1MiLCJtYWMiOiJiOGQ1NjRiMTQ3MjBhZDY1YzMwZDk1N2U4OWIwYmYwOWQyMjBiMGJiOGIyOGZhYTI5ZGMxOThiZDAwNTc4NjNjIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 09:43:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InVmL1FSSUdOb3Z3ODRmNUJiSkZnVkE9PSIsInZhbHVlIjoiOXBFUVBHM3oxZ3M5Z0FBb3RYTUZ4VnpSZ0gvMEJWVURPWlhhSGJpbS9IT0wrSDRMY1ArK2pZT21uRlpjZDA2WTI0UlJLWER2U3Bvb1Z5U0JpMWFCdDI1Z3k0MmNJSFBobHlOQ0JhVzRmZXA5REN6ajJaRTdobzh6UHdnSXhFU3UiLCJtYWMiOiJmMzg3ZmFhYzg3MGQ4NzU1OGI3NmRlNGYzNTcyY2Y2NDA5YjQwMGM1NGVlY2Y4MGVkZTJlYTFmNzRmNTcxZGIyIiwidGFnIjoiIn0%3D; expires=Wed, 16-Jul-2025 09:43:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146089799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-579471427 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$GgKaDmjdfWlNzTUrcO1K5.P1hXVA9J4EX3bYW.ogCY4SrBpQeXIn2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579471427\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/admin", "action_name": "filament.admin.pages.dashboard", "controller_action": "Filament\\Pages\\Dashboard"}, "badge": null}}