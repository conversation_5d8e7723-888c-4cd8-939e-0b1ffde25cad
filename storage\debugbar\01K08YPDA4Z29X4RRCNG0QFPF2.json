{"__meta": {"id": "01K08YPDA4Z29X4RRCNG0QFPF2", "datetime": "2025-07-16 13:28:04", "utime": **********.036939, "method": "GET", "uri": "/san-pham/he-thong-hoi-nghi-truyen-hinh-poly-studio-x50", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[13:27:59] LOG.info: ProductController@show {\n    \"slug\": \"he-thong-hoi-nghi-truyen-hinh-poly-studio-x50\",\n    \"locale\": \"vi\",\n    \"available_locales\": [\n        \"vi\",\n        \"en\"\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.266412, "xdebug_link": null, "collector": "log"}, {"message": "[13:27:59] LOG.info: Product found {\n    \"product_id\": 122,\n    \"product_lang\": \"vi\",\n    \"product_name\": \"H\\u1ec7 th\\u1ed1ng h\\u1ed9i ngh\\u1ecb truy\\u1ec1n h\\u00ecnh Poly Studio X50\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.281954, "xdebug_link": null, "collector": "log"}, {"message": "[13:27:59] LOG.error: syntax error, unexpected token \"{\" (View: H:\\laragon\\www\\auvista\\resources\\views\\templates\\auvista\\products\\products-detail.blade.php) {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.70823, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752647278.770529, "end": **********.036985, "duration": 5.266455888748169, "duration_str": "5.27s", "measures": [{"label": "Booting", "start": 1752647278.770529, "relative_start": 0, "end": **********.114901, "relative_end": **********.114901, "duration": 0.***************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.114912, "relative_start": 0.*****************, "end": **********.036987, "relative_end": 2.1457672119140625e-06, "duration": 4.***************, "duration_str": "4.92s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.251992, "relative_start": 0.****************, "end": **********.254738, "relative_end": **********.254738, "duration": 0.002746105194091797, "duration_str": "2.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.287832, "relative_start": 0.****************, "end": **********.036997, "relative_end": 1.2159347534179688e-05, "duration": 4.***************, "duration_str": "4.75s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.auvista.products.products-detail", "start": **********.289742, "relative_start": 0.****************, "end": **********.289742, "relative_end": **********.289742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.0657, "relative_start": 1.2951710224151611, "end": **********.0657, "relative_end": **********.0657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.637046, "relative_start": 1.8665170669555664, "end": **********.637046, "relative_end": **********.637046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.756962, "relative_start": 1.9864330291748047, "end": **********.756962, "relative_end": **********.756962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.342995, "relative_start": 2.5724658966064453, "end": **********.342995, "relative_end": **********.342995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.3645, "relative_start": 2.593971014022827, "end": **********.3645, "relative_end": **********.3645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.384304, "relative_start": 2.6137750148773193, "end": **********.384304, "relative_end": **********.384304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.384579, "relative_start": 2.6140499114990234, "end": **********.384579, "relative_end": **********.384579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.384815, "relative_start": 2.614285945892334, "end": **********.384815, "relative_end": **********.384815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.403957, "relative_start": 2.633427858352661, "end": **********.403957, "relative_end": **********.403957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.541054, "relative_start": 2.7705249786376953, "end": **********.541054, "relative_end": **********.541054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.567261, "relative_start": 2.796731948852539, "end": **********.567261, "relative_end": **********.567261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.900248, "relative_start": 4.129719018936157, "end": **********.900248, "relative_end": **********.900248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.412216, "relative_start": 4.641686916351318, "end": **********.412216, "relative_end": **********.412216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.432909, "relative_start": 4.66237998008728, "end": **********.432909, "relative_end": **********.432909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.452779, "relative_start": 4.682250022888184, "end": **********.452779, "relative_end": **********.452779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.45345, "relative_start": 4.682920932769775, "end": **********.45345, "relative_end": **********.45345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.472665, "relative_start": 4.702136039733887, "end": **********.472665, "relative_end": **********.472665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.544054, "relative_start": 4.77352499961853, "end": **********.544054, "relative_end": **********.544054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.544582, "relative_start": 4.774052858352661, "end": **********.544582, "relative_end": **********.544582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.875622, "relative_start": 5.105093002319336, "end": **********.875622, "relative_end": **********.875622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.876625, "relative_start": 5.106096029281616, "end": **********.876625, "relative_end": **********.876625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.877285, "relative_start": 5.106755971908569, "end": **********.877285, "relative_end": **********.877285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45141856, "peak_usage_str": "43MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "syntax error, unexpected token \"{\" (View: H:\\laragon\\www\\auvista\\resources\\views\\templates\\auvista\\products\\products-detail.blade.php)", "code": 0, "file": "storage/framework/views/01715d72ef4c4b09769e2184565045cc.php", "line": 711, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1908628183 data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">[object ParseError]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">[object ParseError]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23221 title=\"4 occurrences\">#3221</a><samp data-depth=5 id=sf-dump-1908628183-ref23221 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23187 title=\"4 occurrences\">#3187</a><samp data-depth=5 id=sf-dump-1908628183-ref23187 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>122</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"45 characters\">he-thong-hoi-nghi-truyen-hinh-poly-studio-x50</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ZS8UAC</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Gi&#7843;i ph&#225;p video bar t&#7845;t c&#7843; trong m&#7897;t cho ph&#242;ng h&#7885;p v&#7915;a v&#224; nh&#7887;, t&#237;ch h&#7907;p camera 4K, micro v&#224; loa ch&#7845;t l&#432;&#7907;ng cao.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>57</span>\n            \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Trung Qu&#7889;c</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">B&#7897;</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>122</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"45 characters\">he-thong-hoi-nghi-truyen-hinh-poly-studio-x50</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ZS8UAC</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Gi&#7843;i ph&#225;p video bar t&#7845;t c&#7843; trong m&#7897;t cho ph&#242;ng h&#7885;p v&#7915;a v&#224; nh&#7887;, t&#237;ch h&#7907;p camera 4K, micro v&#224; loa ch&#7845;t l&#432;&#7907;ng cao.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>57</span>\n            \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Trung Qu&#7889;c</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">B&#7897;</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>category</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3161</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoi-nghi-truyen-hinh</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"164 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p, ph&#249; h&#7907;p cho c&#225;c cu&#7897;c h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa v&#224; &#273;&#224;o t&#7841;o tr&#7921;c tuy&#7871;n.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"30 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"70 characters\">h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh, video conference, h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoi-nghi-truyen-hinh</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"164 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p, ph&#249; h&#7907;p cho c&#225;c cu&#7897;c h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa v&#224; &#273;&#224;o t&#7841;o tr&#7921;c tuy&#7871;n.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"30 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"70 characters\">h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh, video conference, h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">seo_title</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"15 characters\">seo_description</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">seo_keywords</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">thumbnail</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            \"<span class=sf-dump-key>brand</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Brand\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Brand</span></span> {<a class=sf-dump-ref>#3149</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Polycom</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">polycom</span>\"\n                \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Polycom</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">polycom</span>\"\n                \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n              +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3157</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n            <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n            <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23143 title=\"4 occurrences\">#3143</a><samp data-depth=5 id=sf-dump-1908628183-ref23143 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23190 title=\"4 occurrences\">#3190</a><samp data-depth=5 id=sf-dump-1908628183-ref23190 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3133</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>123</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Poly Studio X50 Video Conferencing System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"41 characters\">poly-studio-x50-video-conferencing-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-2SC9VI</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"130 characters\">All-in-one video bar solution for small and medium-sized meeting rooms, featuring 4K camera, high-quality microphone and speakers.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>45</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">China</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Set</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>123</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Poly Studio X50 Video Conferencing System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"41 characters\">poly-studio-x50-video-conferencing-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-2SC9VI</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"130 characters\">All-in-one video bar solution for small and medium-sized meeting rooms, featuring 4K camera, high-quality microphone and speakers.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>45</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">China</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Set</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3158</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>124</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Camera h&#7897;i ngh&#7883; Logitech Rally Bar</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"34 characters\">camera-hoi-nghi-logitech-rally-bar</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-5BRZFW</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"106 characters\">Thi&#7871;t b&#7883; h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh cao c&#7845;p cho ph&#242;ng h&#7885;p l&#7899;n, v&#7899;i ch&#7845;t l&#432;&#7907;ng video Ultra-HD v&#224; &#226;m thanh r&#245; n&#233;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>55</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Th&#7909;y S&#297;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>124</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Camera h&#7897;i ngh&#7883; Logitech Rally Bar</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"34 characters\">camera-hoi-nghi-logitech-rally-bar</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-5BRZFW</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"106 characters\">Thi&#7871;t b&#7883; h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh cao c&#7845;p cho ph&#242;ng h&#7885;p l&#7899;n, v&#7899;i ch&#7845;t l&#432;&#7907;ng video Ultra-HD v&#224; &#226;m thanh r&#245; n&#233;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>55</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Th&#7909;y S&#297;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3122</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>125</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Logitech Rally Bar Conference Camera</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"36 characters\">logitech-rally-bar-conference-camera</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-UFQKJ6</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Premium video conferencing device for large rooms, with Ultra-HD video quality and crystal-clear audio.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>90</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Switzerland</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Piece</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>125</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Logitech Rally Bar Conference Camera</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"36 characters\">logitech-rally-bar-conference-camera</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-UFQKJ6</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Premium video conferencing device for large rooms, with Ultra-HD video quality and crystal-clear audio.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>90</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Switzerland</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Piece</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23143 title=\"4 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23143 title=\"4 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23143 title=\"4 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1908628183-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908628183\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            \n", "            @endcomponent\n", "\n", "            {{-- Related Products --}}\n", "            <div class=\"container mx-auto related-slider mb-12 lg:mb-[40px]\">\n", "                <h2 class=\"text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]\"><PERSON><PERSON>n phẩm liên quan</h2>\n", "                @if(isset($related_products) && $related_products->count() > 0)\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F01715d72ef4c4b09769e2184565045cc.php&line=711", "ajax": false, "filename": "01715d72ef4c4b09769e2184565045cc.php", "line": "711"}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "syntax error, unexpected token \"{\"", "code": 0, "file": "storage/framework/views/01715d72ef4c4b09769e2184565045cc.php", "line": 711, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a><samp data-depth=5 id=sf-dump-*********-ref23221 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a><samp data-depth=5 id=sf-dump-*********-ref23187 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>122</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"45 characters\">he-thong-hoi-nghi-truyen-hinh-poly-studio-x50</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ZS8UAC</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Gi&#7843;i ph&#225;p video bar t&#7845;t c&#7843; trong m&#7897;t cho ph&#242;ng h&#7885;p v&#7915;a v&#224; nh&#7887;, t&#237;ch h&#7907;p camera 4K, micro v&#224; loa ch&#7845;t l&#432;&#7907;ng cao.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>57</span>\n            \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Trung Qu&#7889;c</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">B&#7897;</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>122</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"45 characters\">he-thong-hoi-nghi-truyen-hinh-poly-studio-x50</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ZS8UAC</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Gi&#7843;i ph&#225;p video bar t&#7845;t c&#7843; trong m&#7897;t cho ph&#242;ng h&#7885;p v&#7915;a v&#224; nh&#7887;, t&#237;ch h&#7907;p camera 4K, micro v&#224; loa ch&#7845;t l&#432;&#7907;ng cao.</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>57</span>\n            \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Trung Qu&#7889;c</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">B&#7897;</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>category</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3161</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoi-nghi-truyen-hinh</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"164 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p, ph&#249; h&#7907;p cho c&#225;c cu&#7897;c h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa v&#224; &#273;&#224;o t&#7841;o tr&#7921;c tuy&#7871;n.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"30 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"70 characters\">h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh, video conference, h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"20 characters\">hoi-nghi-truyen-hinh</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"164 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p, ph&#249; h&#7907;p cho c&#225;c cu&#7897;c h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa v&#224; &#273;&#224;o t&#7841;o tr&#7921;c tuy&#7871;n.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"30 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"88 characters\">Gi&#7843;i ph&#225;p h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh chuy&#234;n nghi&#7879;p v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh v&#224; h&#236;nh &#7843;nh cao c&#7845;p</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"70 characters\">h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh, video conference, h&#7885;p tr&#7921;c tuy&#7871;n, h&#7897;i ngh&#7883; t&#7915; xa</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">seo_title</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"15 characters\">seo_description</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">seo_keywords</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">thumbnail</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            \"<span class=sf-dump-key>brand</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Brand\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Brand</span></span> {<a class=sf-dump-ref>#3149</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Polycom</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">polycom</span>\"\n                \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Polycom</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"7 characters\">polycom</span>\"\n                \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n              +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3157</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n            <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n            <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23143 title=\"5 occurrences\">#3143</a><samp data-depth=5 id=sf-dump-*********-ref23143 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a><samp data-depth=5 id=sf-dump-*********-ref23190 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3133</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>123</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Poly Studio X50 Video Conferencing System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"41 characters\">poly-studio-x50-video-conferencing-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-2SC9VI</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"130 characters\">All-in-one video bar solution for small and medium-sized meeting rooms, featuring 4K camera, high-quality microphone and speakers.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>45</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">China</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Set</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>123</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Poly Studio X50 Video Conferencing System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"41 characters\">poly-studio-x50-video-conferencing-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-2SC9VI</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"130 characters\">All-in-one video bar solution for small and medium-sized meeting rooms, featuring 4K camera, high-quality microphone and speakers.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">55000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>45</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"5 characters\">China</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Set</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"342 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i Camera\\&quot;,\\&quot;value\\&quot;:\\&quot;4K UHD\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom quang h\\\\u1ecdc\\&quot;,\\&quot;value\\&quot;:\\&quot;5x\\&quot;},{\\&quot;key\\&quot;:\\&quot;G\\\\u00f3c quay\\&quot;,\\&quot;value\\&quot;:\\&quot;120 \\\\u0111\\\\u1ed9\\&quot;},{\\&quot;key\\&quot;:\\&quot;Microphone\\&quot;,\\&quot;value\\&quot;:\\&quot;T\\\\u00edch h\\\\u1ee3p 4 micro\\&quot;},{\\&quot;key\\&quot;:\\&quot;K\\\\u1ebft n\\\\u1ed1i\\&quot;,\\&quot;value\\&quot;:\\&quot;HDMI, USB-C, Wi-Fi, Bluetooth\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3158</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>124</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Camera h&#7897;i ngh&#7883; Logitech Rally Bar</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"34 characters\">camera-hoi-nghi-logitech-rally-bar</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-5BRZFW</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"106 characters\">Thi&#7871;t b&#7883; h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh cao c&#7845;p cho ph&#242;ng h&#7885;p l&#7899;n, v&#7899;i ch&#7845;t l&#432;&#7907;ng video Ultra-HD v&#224; &#226;m thanh r&#245; n&#233;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>55</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Th&#7909;y S&#297;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>124</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Camera h&#7897;i ngh&#7883; Logitech Rally Bar</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"34 characters\">camera-hoi-nghi-logitech-rally-bar</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-5BRZFW</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"106 characters\">Thi&#7871;t b&#7883; h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh cao c&#7845;p cho ph&#242;ng h&#7885;p l&#7899;n, v&#7899;i ch&#7845;t l&#432;&#7907;ng video Ultra-HD v&#224; &#226;m thanh r&#245; n&#233;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>55</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Th&#7909;y S&#297;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3122</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>125</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Logitech Rally Bar Conference Camera</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"36 characters\">logitech-rally-bar-conference-camera</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-UFQKJ6</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Premium video conferencing device for large rooms, with Ultra-HD video quality and crystal-clear audio.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>90</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Switzerland</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Piece</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>125</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Logitech Rally Bar Conference Camera</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"36 characters\">logitech-rally-bar-conference-camera</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-UFQKJ6</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"103 characters\">Premium video conferencing device for large rooms, with Ultra-HD video quality and crystal-clear audio.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">89000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>90</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Switzerland</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Piece</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"366 characters\">&quot;[{\\&quot;key\\&quot;:\\&quot;\\\\u0110\\\\u1ed9 ph\\\\u00e2n gi\\\\u1ea3i\\&quot;,\\&quot;value\\&quot;:\\&quot;4K, 1440p, 1080p, 900p, 720p, and SD at 30fps\\&quot;},{\\&quot;key\\&quot;:\\&quot;Pan\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b125\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Tilt\\&quot;,\\&quot;value\\&quot;:\\&quot;Motorized \\\\u00b115\\\\u00b0\\&quot;},{\\&quot;key\\&quot;:\\&quot;Zoom\\&quot;,\\&quot;value\\&quot;:\\&quot;15X HD zoom (5X optical, 3X digital)\\&quot;},{\\&quot;key\\&quot;:\\&quot;T\\\\u1ea7m thu \\\\u00e2m micro\\&quot;,\\&quot;value\\&quot;:\\&quot;4.5 m\\&quot;}]&quot;</span>\"\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23143 title=\"5 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23143 title=\"5 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23143 title=\"5 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23143 title=\"5 occurrences\">#3143</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"20 characters\">H&#7897;i ngh&#7883; truy&#7873;n h&#236;nh</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">https://auvista.test/danh-muc-san-pham/hoi-nghi-truyen-hinh</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"45 characters\">H&#7879; th&#7889;ng h&#7897;i ngh&#7883; truy&#7873;n h&#236;nh Poly Studio X50</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            \n", "            @endcomponent\n", "\n", "            {{-- Related Products --}}\n", "            <div class=\"container mx-auto related-slider mb-12 lg:mb-[40px]\">\n", "                <h2 class=\"text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]\"><PERSON><PERSON>n phẩm liên quan</h2>\n", "                @if(isset($related_products) && $related_products->count() > 0)\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F01715d72ef4c4b09769e2184565045cc.php&line=711", "ajax": false, "filename": "01715d72ef4c4b09769e2184565045cc.php", "line": "711"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "templates.auvista.products.products-detail", "param_count": null, "params": [], "start": **********.289722, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.phptemplates.auvista.products.products-detail", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fproducts%2Fproducts-detail.blade.php&line=1", "ajax": false, "filename": "products-detail.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.065677, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.637023, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.756932, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.342969, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.364475, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.384274, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.384556, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.384797, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.403922, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.541021, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.567234, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.900206, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.412132, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.432774, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.452672, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.453369, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.472622, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.544031, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.544535, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.875584, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.876598, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.877236, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0069500000000000004, "accumulated_duration_str": "6.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `products` where `slug` = 'he-thong-hoi-nghi-truyen-hinh-poly-studio-x50' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["he-thong-hoi-nghi-truyen-hinh-poly-studio-x50", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2672331, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 21.871}, {"sql": "select * from `categories` where `categories`.`id` in (3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2724721, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 21.871, "width_percent": 9.496}, {"sql": "select * from `brands` where `brands`.`id` in (11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2759218, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 31.367, "width_percent": 10.791}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (122)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.278843, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 42.158, "width_percent": 32.95}, {"sql": "select * from `products` where `category_id` = 3 and `id` != 122 and `status` = 1 limit 4", "type": "query", "params": [], "bindings": [3, 122, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 244}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.282122, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:244", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=244", "ajax": false, "filename": "ProductController.php", "line": "244"}, "connection": "auvista", "explain": null, "start_percent": 75.108, "width_percent": 13.525}, {"sql": "select count(*) as aggregate from `product_reviews` where `product_reviews`.`product_id` = 122 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 247}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.283814, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:247", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 247}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=247", "ajax": false, "filename": "ProductController.php", "line": "247"}, "connection": "auvista", "explain": null, "start_percent": 88.633, "width_percent": 6.043}, {"sql": "select count(*) as aggregate from `product_reviews` where `product_reviews`.`product_id` = 122 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [122], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 275}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.284817, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:275", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=275", "ajax": false, "filename": "ProductController.php", "line": "275"}, "connection": "auvista", "explain": null, "start_percent": 94.676, "width_percent": 5.324}]}, "models": {"data": {"App\\Models\\Product": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 6, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://auvista.test/san-pham/he-thong-hoi-nghi-truyen-hinh-poly-studio-x50", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\ProductController@show", "uri": "GET san-pham/{slug}", "controller": "App\\Http\\Controllers\\ProductController@show<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=136\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/san-pham", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=136\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ProductController.php:136-314</a>", "middleware": "web", "duration": "5.3s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-697255817 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-697255817\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1722172370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1722172370\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-917999799 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://auvista.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktHazM2SDVuZjY2RTROT3AwZHZaV2c9PSIsInZhbHVlIjoiK0d2QXVGd3VMaWRzb3RDMkpCdk05MjZYVU5teWRIQkRRbkduQ1E0elpMQWFQOExMbWwwaFEvZ3RvS3RPVHNDM2ZmU2sxTkpqMnFDekI4dlVubmxuTkV3UGticEtZaUYzRjIzUEVmdWpwTFlHM2ZlMTVpTUplcnh6c3loQ2ZFOTgiLCJtYWMiOiJjOTk4OTVhMGE4ZGNjMjI0ZTMyMjRhOTY0YmFjNjRkYjNjNDNhOGZiYmY0MzViZDAwY2RkMWRmY2E3ZjM5NTViIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlBVektRRWp5RnQzeDhLQU1uNm9oRkE9PSIsInZhbHVlIjoiRzNROThxZ1FwVlNzMUdDOFJ3Z1hFRlB3YkZ1aXZXU0QvQUppcW1LREc4UXJ4SzJOTmNzQjZ4OXp6dllQTTRENkErcDR2Y3BTdENXQ3pVSmFaN0U4c21lVU9CNjFqYnRXTWwvZ1RIYWVWZnExbXNaUXlFZ1FkM29BenpJQlNsbWUiLCJtYWMiOiJmYmYzMTRhMjU1NmJkOGUwOTE4OTUwZDQxZDgwZDJiOTA1ZjM4NmI4OTE2NzU0MjEyNzEzNmVkMTc4ZDA1ZGM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917999799\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1365356068 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1365356068\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-129261851 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 06:28:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129261851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2043132352 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>152</span> => <span class=sf-dump-num>1</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043132352\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://auvista.test/san-pham/he-thong-hoi-nghi-truyen-hinh-poly-studio-x50", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\ProductController@show"}, "badge": "500 Internal Server Error"}}