<?php

namespace App\Helpers;

use Carbon\Carbon;
use Illuminate\Support\Facades\App;

class FormatHelper
{
    /**
     * Định dạng ngày tháng theo ngôn ngữ hiện tại
     *
     * @param string|Carbon $date Ngày cần định dạng
     * @param string $format Format hiển thị, mặc định là 'd/m/Y'
     * @return string Chuỗi ngày đã được định dạng
     */
    public static function formatDateLocalized($date, $format = 'd/m/Y')
    {
        if (!$date) {
            return '';
        }
        
        if (!($date instanceof Carbon)) {
            $date = Carbon::parse($date);
        }
        
        // Nếu sử dụng tiếng Việt, format theo kiểu Việt Nam
        if (App::getLocale() === 'vi') {
            return $date->format($format);
        }
        
        return $date->format($format);
    }
} 