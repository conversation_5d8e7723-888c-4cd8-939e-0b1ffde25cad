<?php

namespace App\Observers;

use App\Models\ProductReview;
use Filament\Notifications\Notification;
use Filament\Notifications\Actions\Action;

class ProductReviewObserver
{
    /**
     * Handle the ProductReview "created" event.
     */
    public function created(ProductReview $productReview): void
    {
        // Gửi notification cho admin khi có bình luận mới
        $this->sendNewReviewNotification($productReview);
    }

    /**
     * Handle the ProductReview "updated" event.
     */
    public function updated(ProductReview $productReview): void
    {
        // Nếu admin vừa thêm phản hồi
        if ($productReview->wasChanged('admin_reply') && !empty($productReview->admin_reply)) {
            $this->sendReplyAddedNotification($productReview);
        }
    }

    /**
     * Gửi thông báo khi có bình luận mới
     */
    private function sendNewReviewNotification(ProductReview $productReview): void
    {
        // Kiểm tra xem có admin nào đang đăng nhập không
        $adminUsers = $this->getAdminUsers();

        if ($adminUsers->isEmpty()) {
            return; // Không có admin nào để gửi thông báo
        }

        // Chỉ gửi notification cho bình luận có rating thấp hoặc cần chú ý
        if ($productReview->rating <= 2) {
            Notification::make()
                ->title('Bình luận đánh giá thấp mới!')
                ->body("Sản phẩm '{$productReview->product->name}' nhận được đánh giá {$productReview->rating} sao từ {$productReview->reviewer_name}")
                ->icon('heroicon-o-exclamation-triangle')
                ->iconColor('danger')
                ->actions([
                    Action::make('view')
                        ->label('Xem chi tiết')
                        ->url(route('filament.admin.resources.product-reviews.view', $productReview))
                        ->button(),
                    Action::make('reply')
                        ->label('Phản hồi ngay')
                        ->url(route('filament.admin.resources.product-reviews.edit', $productReview))
                        ->button()
                        ->color('primary'),
                ])
                ->persistent()
                ->sendToDatabase($adminUsers);
        } else {
            // Notification thông thường cho bình luận tích cực
            Notification::make()
                ->title('Bình luận mới')
                ->body("Sản phẩm '{$productReview->product->name}' có bình luận mới từ {$productReview->reviewer_name}")
                ->icon('heroicon-o-chat-bubble-left')
                ->iconColor('info')
                ->actions([
                    Action::make('view')
                        ->label('Xem')
                        ->url(route('filament.admin.resources.product-reviews.view', $productReview)),
                ])
                ->sendToDatabase($adminUsers);
        }
    }

    /**
     * Gửi thông báo khi admin thêm phản hồi
     */
    private function sendReplyAddedNotification(ProductReview $productReview): void
    {
        // Chỉ gửi thông báo nếu có user đăng nhập
        if (auth()->check()) {
            Notification::make()
                ->title('Đã phản hồi bình luận')
                ->body("Bạn đã phản hồi bình luận của {$productReview->reviewer_name} cho sản phẩm '{$productReview->product->name}'")
                ->icon('heroicon-o-check-circle')
                ->iconColor('success')
                ->send();
        }
    }

    /**
     * Lấy danh sách admin users để gửi thông báo
     */
    private function getAdminUsers()
    {
        // Lấy tất cả users có role admin hoặc có quyền quản lý
        $adminUsers = \App\Models\User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super-admin', 'editor']);
        })->orWhere('id', 1) // Admin user với ID = 1
        ->get();

        // Nếu không có admin nào, lấy user đầu tiên làm fallback
        if ($adminUsers->isEmpty()) {
            $firstUser = \App\Models\User::first();
            if ($firstUser) {
                $adminUsers = collect([$firstUser]);
            }
        }

        return $adminUsers;
    }
}
