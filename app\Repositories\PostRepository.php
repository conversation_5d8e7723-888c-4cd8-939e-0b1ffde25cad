<?php

namespace App\Repositories;

use App\Models\Post;

class PostRepository extends BaseRepository
{
    /**
     * @return string
     */
    public function getModel()
    {
        return Post::class;
    }
    
    /**
     * <PERSON><PERSON><PERSON> bài viết đã xuất bản
     *
     * @param int $limit
     * @return mixed
     */
    public function getPublishedPosts($limit = 10)
    {
        return $this->model->where('status', 'published')
                      ->whereNotNull('published_at')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories'])
                      ->limit($limit)
                      ->get();
    }
    
    /**
     * <PERSON><PERSON>y bài viết nổi bật
     *
     * @param int $limit
     * @return mixed
     */
    public function getFeaturedPosts($limit = 6)
    {
        return $this->model->where('status', 'published')
                    //   ->where('is_featured', true)
                      ->whereNotNull('published_at')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories'])
                      ->limit($limit)
                      ->get();
    }
    
    /**
     * L<PERSON>y dự án nổi bật
     *
     * @param int $limit
     * @return mixed
     */
    public function getFeaturedProjects($limit = 6)
    {
        return $this->model->whereHas('categories', function($query) {
                        $query->where('slug', 'du-an');
                    })
                    ->where('status', 'published')
                    ->where('is_featured', true)
                    ->whereNotNull('published_at')
                    ->orderBy('published_at', 'desc')
                    ->with(['author', 'categories'])
                    ->limit($limit)
                    ->get();
    }
    
    /**
     * Lấy bài viết theo danh mục
     *
     * @param int $categoryId
     * @param int $limit
     * @return mixed
     */
    public function getPostsByCategory($categoryId, $limit = 10)
    {
        return $this->model->whereHas('categories', function($query) use ($categoryId) {
                        $query->where('categories.id', $categoryId);
                      })
                      ->where('status', 'published')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories'])
                      ->limit($limit)
                      ->get();
    }
    
    /**
     * Lấy bài viết theo slug
     *
     * @param string $slug
     * @return mixed
     */
    public function getPostBySlug($slug)
    {
        return $this->model->where('slug', $slug)
                      ->where('status', 'published')
                      ->with(['author', 'categories'])
                      ->first();
    }
    
    /**
     * Lấy bài viết liên quan theo danh mục
     *
     * @param int $postId
     * @param int $categoryId
     * @param int $limit
     * @return mixed
     */
    public function getRelatedPosts($postId, $categoryId, $limit = 5)
    {
        return $this->model->where('id', '!=', $postId)
                      ->whereHas('categories', function($query) use ($categoryId) {
                          $query->where('categories.id', $categoryId);
                      })
                      ->where('status', 'published')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories'])
                      ->limit($limit)
                      ->get();
    }
    
    /**
     * Lấy bài viết mới nhất
     *
     * @param int|null $categoryId
     * @param int $limit
     * @return mixed
     */
    public function getPosts($categoryId = null, $limit = 5)
    {
        $query = $this->model->where('status', 'published')
                      ->whereNotNull('published_at')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories']);

        if ($categoryId) {
            $query->whereHas('categories', function($q) use ($categoryId) {
                $q->where('categories.id', $categoryId);
            });
        }

        return $query->limit($limit)->get();
    }
    
    /**
     * Lấy bài viết mới nhất
     *
     * @param int $limit
     * @return mixed
     */
    public function getLatestPosts($limit = 3)
    {
        return $this->model->where('status', 'published')
                      ->whereNotNull('published_at')
                      ->orderBy('published_at', 'desc')
                      ->with(['author', 'categories'])
                      ->limit($limit)
                      ->get();
    }
} 