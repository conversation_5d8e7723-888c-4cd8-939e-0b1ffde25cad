<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;

class CreateOrder extends CreateRecord
{
    protected static string $resource = OrderResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Tạo mã đơn hàng nếu chưa có
        if (empty($data['order_number'])) {
            $data['order_number'] = 'ORD-' . strtoupper(Str::random(8));
        }
        
        // Thêm trạng thái mặc định nếu không có
        if (empty($data['status'])) {
            $data['status'] = 'pending';
        }
        
        // Thêm trạng thái thanh toán mặc định nếu không có
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'pending';
        }
        
        return $data;
    }
    
    protected function afterCreate(): void
    {
        // Bạn có thể thêm logic sau khi tạo tại đây
        // Ví dụ: gửi email xác nhận, cập nhật tồn kho, v.v.
    }
    
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
} 