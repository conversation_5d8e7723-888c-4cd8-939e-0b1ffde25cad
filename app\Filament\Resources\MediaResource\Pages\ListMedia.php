<?php

namespace App\Filament\Resources\MediaResource\Pages;

use App\Filament\Resources\MediaResource;
use App\Models\Media;
use App\Services\MediaService;
use Filament\Actions;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Pages\ListRecords;
use Filament\Forms\Components\Section;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class ListMedia extends ListRecords
{
    protected static string $resource = MediaResource::class;
    
    // ID tĩnh cho action upload
    public const UPLOAD_ACTION_ID = 'media-upload-action';
    
    /**
     * Thêm scripts cho trang
     */
    protected function getHeaderWidgets(): array
    {
        return [];
    }
    
    /**
     * Thêm JavaScript để kiểm tra xem có nên kích hoạt modal khi từ trang trống không
     */
    protected function getFooterWidgets(): array
    {
        // Thêm script để kiểm tra URL parameter
        echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            // Tìm nút upload trên header
            var uploadBtn = document.querySelector(".fi-ac-btn");
            if (uploadBtn) {
                uploadBtn.id = "media-upload-button";
                
                // Kiểm tra xem có đang từ trang trống không
                const urlParams = new URLSearchParams(window.location.search);
                if (urlParams.has("empty") && urlParams.get("empty") === "1") {
                    setTimeout(function() {
                        uploadBtn.click();
                    }, 500);
                }
            }
        });
        </script>';
        
        return [];
    }
    
    public function mount(): void
    {
        parent::mount();
        
        // Đặt ID cho action để có thể gọi từ emptyState
        $this->mountAction(static::UPLOAD_ACTION_ID);
        
        // Kiểm tra nếu có tham số URL để xử lý file từ thông báo xác nhận
        $overwrite = request()->has('overwrite');
        $skip = request()->has('skip');
        
        if (($overwrite || $skip) && session()->has('media_upload_files')) {
            $files = session()->get('media_upload_files', []);
            if (!empty($files)) {
                $this->processUploadedFiles($files, $overwrite);
            }
            // Xóa session sau khi xử lý
            session()->forget('media_upload_files');
        }
    }
    
    /**
     * Được gọi sau khi component đã được mount
     */
    protected function afterMount(): void
    {
        // Nếu không có dữ liệu, hiển thị upload modal tự động
        if (Media::count() === 0) {
            $this->mountAction(static::UPLOAD_ACTION_ID);
        }
    }
    
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make(static::UPLOAD_ACTION_ID)
                ->label('Tải lên')
                ->icon('heroicon-o-arrow-up-tray')
                ->button()
                ->extraAttributes([
                    'id' => 'media-upload-button',
                ])
                ->modalWidth('md')
                ->modalHeading('Tải lên ảnh')
                ->form([
                    Section::make()
                        ->schema([
                            FileUpload::make('file')
                                ->label('Chọn ảnh')
                                ->disk('public')
                                ->directory('media/uploads')
                                ->visibility('public')
                                ->preserveFilenames()
                                ->image()
                                ->imageEditor()
                                ->required()
                                ->multiple()
                                ->maxFiles(5)
                                ->columnSpanFull()
                                ->hint('Hỗ trợ JPG, PNG, GIF, WEBP - Tối đa 5 file')
                                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
                        ]),
                ])
                ->action(function (array $data): void {
                    // Log thông tin đầu vào
                    Log::info('Upload action data:', ['data' => $data]);
                    
                    try {
                        // Nếu không có file nào, hiển thị thông báo
                        if (!isset($data['file']) || empty($data['file'])) {
                            Notification::make()
                                ->title('Không có file nào được chọn')
                                ->danger()
                                ->send();
                            return;
                        }
                        
                        $mediaService = app(MediaService::class);
                        $fileUploaded = 0;
                        $fileSkipped = 0;
                        $fileReplaced = 0;
                        $duplicateFiles = [];
                        
                        // Kiểm tra các file trùng lặp trước
                        foreach ((array) $data['file'] as $file) {
                            $fileName = basename($file);
                            $existingMedia = Media::where('file_name', $fileName)->first();
                            
                            if ($existingMedia) {
                                $duplicateFiles[] = $fileName;
                            }
                        }
                        
                        // Nếu có file trùng lặp, hiển thị thông báo xác nhận
                        if (!empty($duplicateFiles)) {
                            $fileNames = implode(', ', $duplicateFiles);
                            $confirmText = "File đã tồn tại: {$fileNames}. Bạn có muốn ghi đè không?";
                            
                            // Lưu session để tiếp tục xử lý sau khi có xác nhận
                            session()->put('media_upload_files', $data['file']);
                            
                            Notification::make()
                                ->title('Phát hiện file trùng tên')
                                ->body($confirmText)
                                ->persistent()
                                ->actions([
                                    \Filament\Notifications\Actions\Action::make('overwrite')
                                        ->label('Đè lên')
                                        ->color('danger')
                                        ->url($this->getResource()::getUrl('index') . '?overwrite=1'),
                                    \Filament\Notifications\Actions\Action::make('skipDuplicate')
                                        ->label('Bỏ qua file trùng')
                                        ->color('gray')
                                        ->url($this->getResource()::getUrl('index') . '?skip=1'),
                                    \Filament\Notifications\Actions\Action::make('cancel')
                                        ->label('Hủy bỏ')
                                        ->close(),
                                ])
                                ->send();
                            
                            // Không tiếp tục xử lý, đợi người dùng xác nhận
                            return;
                        }
                        
                        // Xử lý files (không có trùng lặp)
                        $this->processUploadedFiles($data['file'], false);
                    } catch (\Exception $e) {
                        Log::error('Lỗi khi upload file:', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                        
                        Notification::make()
                            ->title('Có lỗi xảy ra khi tải file')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }
    
    protected function processUploadedFiles(array $files, bool $overwriteExisting = false): void
    {
        $fileUploaded = 0;
        $fileSkipped = 0;
        $fileReplaced = 0;
        
        // Xử lý từng file một
        foreach ($files as $file) {
            Log::info('Processing file:', ['file' => $file]);
            
            // Kiểm tra file có tồn tại trong storage không
            if (!Storage::disk('public')->exists($file)) {
                Log::error('File không tồn tại trong storage:', ['path' => $file]);
                continue;
            }
            
            try {
                $fileName = basename($file);
                
                // Kiểm tra xem file đã tồn tại trong DB chưa
                $existingMedia = Media::where('file_name', $fileName)->first();
                
                // Media object để xử lý tiếp theo
                $media = null;
                
                if ($existingMedia) {
                    // Nếu không được phép ghi đè, bỏ qua
                    if (!$overwriteExisting) {
                        $fileSkipped++;
                        Log::info('Skipping duplicate file: ' . $fileName);
                        continue;
                    }
                    
                    // File đã tồn tại, cập nhật thông tin thay vì tạo mới
                    $oldPath = $existingMedia->path;
                    
                    // Cập nhật thông tin media hiện có
                    $existingMedia->update([
                        'mime_type' => Storage::disk('public')->mimeType($file),
                        'size' => Storage::disk('public')->size($file),
                        'disk' => 'public',
                        'directory' => dirname($file),
                        'path' => $file,
                        'extension' => pathinfo($file, PATHINFO_EXTENSION),
                    ]);
                    
                    // Xóa file cũ nếu khác với file mới
                    if ($oldPath !== $file && Storage::disk('public')->exists($oldPath)) {
                        Storage::disk('public')->delete($oldPath);
                    }
                    
                    $fileReplaced++;
                    $media = $existingMedia;
                    
                    Log::info('Media updated:', [
                        'id' => $media->id,
                        'name' => $media->name,
                    ]);
                } else {
                    // Tạo media mới
                    $media = Media::create([
                        'name' => pathinfo($file, PATHINFO_FILENAME),
                        'file_name' => $fileName,
                        'mime_type' => Storage::disk('public')->mimeType($file),
                        'size' => Storage::disk('public')->size($file),
                        'disk' => 'public',
                        'directory' => dirname($file),
                        'path' => $file,
                        'extension' => pathinfo($file, PATHINFO_EXTENSION),
                    ]);
                    
                    $fileUploaded++;
                    Log::info('Media created:', [
                        'id' => $media->id,
                        'name' => $media->name,
                    ]);
                }
                
                // Tạo thumbnail nếu là ảnh và media tồn tại
                if ($media && str_starts_with($media->mime_type, 'image/')) {
                    try {
                        // Tạo thư mục thumbnail nếu chưa có
                        $thumbnailDir = dirname($media->path) . '/thumbnails';
                        if (!Storage::disk($media->disk)->exists($thumbnailDir)) {
                            Storage::disk($media->disk)->makeDirectory($thumbnailDir);
                        }
                        
                        // Đường dẫn đến file gốc 
                        $originalPath = Storage::disk($media->disk)->path($media->path);
                        
                        // Đường dẫn thumbnail
                        $pathInfo = pathinfo($media->path);
                        $thumbnailFileName = $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
                        $thumbnailPath = $thumbnailDir . '/' . $thumbnailFileName;
                        $thumbnailFullPath = Storage::disk($media->disk)->path($thumbnailPath);
                        
                        // Xử lý ảnh trực tiếp thay vì dùng Intervention\Image
                        $img = imagecreatefromstring(file_get_contents($originalPath));
                        $width = imagesx($img);
                        $height = imagesy($img);
                        
                        // Cập nhật kích thước
                        $media->update([
                            'width' => $width,
                            'height' => $height,
                        ]);
                        
                        // Tạo thumbnail
                        $thumbWidth = 50;
                        $thumbHeight = 50;
                        $thumb = imagecreatetruecolor($thumbWidth, $thumbHeight);
                        
                        // Giữ độ trong suốt cho PNG
                        if ($media->mime_type === 'image/png') {
                            imagealphablending($thumb, false);
                            imagesavealpha($thumb, true);
                            $transparent = imagecolorallocatealpha($thumb, 255, 255, 255, 127);
                            imagefilledrectangle($thumb, 0, 0, $thumbWidth, $thumbHeight, $transparent);
                        }
                        
                        // Resize giữ tỷ lệ
                        imagecopyresampled($thumb, $img, 0, 0, 0, 0, $thumbWidth, $thumbHeight, $width, $height);
                        
                        // Lưu thumbnail
                        switch ($media->mime_type) {
                            case 'image/jpeg':
                                imagejpeg($thumb, $thumbnailFullPath, 90);
                                break;
                            case 'image/png':
                                imagepng($thumb, $thumbnailFullPath, 9);
                                break;
                            case 'image/gif':
                                imagegif($thumb, $thumbnailFullPath);
                                break;
                            default:
                                imagejpeg($thumb, $thumbnailFullPath, 90);
                        }
                        
                        // Giải phóng bộ nhớ
                        imagedestroy($img);
                        imagedestroy($thumb);
                    } catch (\Exception $e) {
                        Log::error('Lỗi khi tạo thumbnail: ' . $e->getMessage(), [
                            'media_id' => $media->id,
                            'file' => $file,
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Lỗi khi tạo media: ' . $e->getMessage(), [
                    'file' => $file,
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        
        // Hiển thị thông báo thành công
        $message = "Đã tải lên $fileUploaded file thành công";
        if ($fileReplaced > 0) {
            $message .= ", $fileReplaced file đã được cập nhật";
        }
        if ($fileSkipped > 0) {
            $message .= ", $fileSkipped file bị bỏ qua";
        }
        
        Notification::make()
            ->title($message)
            ->success()
            ->send();
            
        // Cập nhật badge và làm mới danh sách
        $this->getResource()::refreshNavigationBadge(); 
        
        // Chuyển hướng để làm mới trang
        $this->redirect(static::getResource()::getUrl('index'));
    }
} 