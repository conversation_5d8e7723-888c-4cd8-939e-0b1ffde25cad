<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                // Nếu đường dẫn bắt đầu với 'admin' và guard là 'web', chuyển hướng đến trang admin
                if (str_starts_with($request->path(), 'admin') && $guard === 'web') {
                    return redirect(RouteServiceProvider::ADMIN_HOME);
                }
                
                // Nếu là guard frontend, chuyển hướng đến trang chủ frontend
                if ($guard === 'frontend') {
                    return redirect(RouteServiceProvider::HOME);
                }
                
                // Mặc định
                return redirect(RouteServiceProvider::HOME);
            }
        }

        return $next($request);
    }
}
