<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LogActionResource\Pages;
use App\Models\LogAction;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Config;

class LogActionResource extends BaseResource
{
    protected static ?string $model = LogAction::class;

    protected static ?string $navigationGroup = 'Cài đặt hệ thống';
    protected static ?int $navigationGroupSort = 9999; // Add this line for group sorting
    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    protected static ?string $navigationLabel = 'Log Actions';
    protected static ?int $navigationSort = 9999;

    public static function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                Columns\TextColumn::make('user.name')->label('User')->searchable(),
                Columns\TextColumn::make('model_type')->label('Model')->searchable(),
                Columns\TextColumn::make('model_id')->label('Model ID'),
                Columns\TextColumn::make('action')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'created' => 'Tạo mới',
                        'updated' => 'Cập nhật',
                        'deleted' => 'Xóa',
                        'login_success' => 'Đăng nhập thành công',
                        'login_failed' => 'Đăng nhập thất bại',
                        'logout' => 'Đăng xuất',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'created' => 'success',
                        'updated' => 'warning',
                        'deleted' => 'danger',
                        'login_success' => 'success',
                        'login_failed' => 'danger',
                        'logout' => 'info',
                        default => 'gray',
                    }),
                Columns\TextColumn::make('ip_address')->label('IP Address'),
                Columns\TextColumn::make('created_at')->label('Time')->since(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('action')
                    ->options([
                        'created' => 'Tạo mới',
                        'updated' => 'Cập nhật',
                        'deleted' => 'Xóa',
                        'login_success' => 'Đăng nhập thành công',
                        'login_failed' => 'Đăng nhập thất bại',
                        'logout' => 'Đăng xuất',
                    ])
                    ->label('Action'),
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->label('User'),
            ])
            ->actions([
                Action::make('view')
                    ->label('View')
                    ->modalHeading('Log Details')
                    ->modalButton('Close')
                    ->modalWidth('2xl')
                    ->form(fn (LogAction $record) => [
                        Forms\Components\TextInput::make('user.name')
                            ->label('User')
                            ->disabled()
                            ->afterStateHydrated(fn ($component, $state, $record) => $component->state($record->user?->name ?? 'N/A')),
                        
                        Forms\Components\TextInput::make('model_type')
                            ->label('Model')
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('model_id')
                            ->label('Model ID')
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('action')
                            ->label('Action')
                            ->disabled()
                            ->afterStateHydrated(fn ($component, $state) => $component->state(match ($state) {
                                'created' => 'Tạo mới',
                                'updated' => 'Cập nhật',
                                'deleted' => 'Xóa',
                                'login_success' => 'Đăng nhập thành công',
                                'login_failed' => 'Đăng nhập thất bại',
                                'logout' => 'Đăng xuất',
                                default => $state,
                            })),
                            
                        Forms\Components\TextInput::make('ip_address')
                            ->label('IP Address')
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('user_agent')
                            ->label('User Agent')
                            ->disabled(),
                            
                        Forms\Components\TextInput::make('created_at')
                            ->label('Created At')
                            ->disabled()
                            ->afterStateHydrated(fn ($component, $state, $record) => $component->state($record->created_at?->diffForHumans() ?? 'N/A')),
                        
                        Forms\Components\Textarea::make('old_data')
                            ->label('Old Data')
                            ->disabled()
                            ->afterStateHydrated(function ($component, $state) {
                                if (empty($state)) {
                                    $component->state('No data');
                                } else {
                                    $component->state(json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                }
                            })
                            ->columnSpanFull()
                            ->hidden(fn ($record) => empty($record->old_data)),
                            
                        Forms\Components\Textarea::make('new_data')
                            ->label('New Data')
                            ->disabled()
                            ->afterStateHydrated(function ($component, $state) {
                                if (empty($state)) {
                                    $component->state('No data');
                                } else {
                                    $component->state(json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                                }
                            })
                            ->columnSpanFull()
                            ->hidden(fn ($record) => empty($record->new_data)),
                    ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->searchable();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLogActions::route('/'),
        ];
    }
}
