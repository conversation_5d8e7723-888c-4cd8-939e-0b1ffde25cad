<?php
namespace Database\Factories;

use App\Models\StaticPage;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class StaticPageFactory extends Factory
{
    protected $model = StaticPage::class;

    public function definition(): array
    {
        $title = $this->faker->sentence();
        $createdAt = $this->faker->dateTimeBetween('-1 year', 'now');
        
        return [
            'title' => $title,
            'slug' => Str::slug($title),
            'content' => $this->faker->paragraphs(3, true),
            'excerpt' => $this->faker->paragraph(),
            'seo_title' => $this->faker->words(6, true),
            'seo_description' => $this->faker->sentence(),
            'seo_keywords' => implode(', ', $this->faker->words(5)),
            'status' => $this->faker->randomElement(['draft', 'published']),
            'author_id' => 1,
            'published_at' => $createdAt,
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
        ];
    }
} 