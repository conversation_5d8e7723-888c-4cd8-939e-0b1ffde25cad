<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\URL;
use App\Models\ProductReview;
use App\Observers\ProductReviewObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema::defaultStringLength(191);
        if (app()->environment('local')) {
            URL::forceScheme('https');
        }

        // Register Model Observers
        ProductReview::observe(ProductReviewObserver::class);

        // Register Livewire components
        Livewire::component('cart', \App\Livewire\CartPage::class);
        Livewire::component('cart-popup', \App\Livewire\CartPopup::class);
        Livewire::component('add-to-cart-button', \App\Livewire\AddToCartButton::class);
        Livewire::component('cart-count', \App\Livewire\CartCount::class);
        Livewire::component('notification', \App\Livewire\Notification::class);
        Livewire::component('language-switcher', \App\Livewire\LanguageSwitcher::class);
        Livewire::component('table-language-filter', \App\Livewire\TableLanguageFilter::class);
    }
}
