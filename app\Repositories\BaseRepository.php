<?php

namespace App\Repositories;

use Illuminate\Database\Eloquent\Model;

abstract class BaseRepository implements RepositoryInterface
{
    /**
     * @var Model
     */
    protected $model;
    
    /**
     * BaseRepository constructor.
     */
    public function __construct()
    {
        $this->setModel();
    }
    
    /**
     * Khởi tạo model repository
     *
     * @return void
     */
    abstract public function getModel();
    
    /**
     * Set model
     *
     * @return void
     */
    public function setModel()
    {
        $this->model = app()->make($this->getModel());
    }
    
    /**
     * L<PERSON>y tất cả các bản ghi
     *
     * @param array $columns
     * @return mixed
     */
    public function all($columns = ['*'])
    {
        return $this->model->all($columns);
    }
    
    /**
     * L<PERSON>y tất cả các bản ghi với phân trang
     *
     * @param int $perPage
     * @param array $columns
     * @return mixed
     */
    public function paginate($perPage = 15, $columns = ['*'])
    {
        return $this->model->paginate($perPage, $columns);
    }
    
    /**
     * Tạo bản ghi mới
     *
     * @param array $data
     * @return mixed
     */
    public function create(array $data)
    {
        return $this->model->create($data);
    }
    
    /**
     * Cập nhật bản ghi
     *
     * @param array $data
     * @param int $id
     * @return mixed
     */
    public function update(array $data, $id)
    {
        $record = $this->find($id);
        return $record->update($data);
    }
    
    /**
     * Xóa bản ghi
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id)
    {
        return $this->model->destroy($id);
    }
    
    /**
     * Tìm bản ghi theo ID
     *
     * @param int $id
     * @param array $columns
     * @return mixed
     */
    public function find($id, $columns = ['*'])
    {
        return $this->model->findOrFail($id, $columns);
    }
    
    /**
     * Tìm bản ghi theo điều kiện
     *
     * @param string $field
     * @param mixed $value
     * @param array $columns
     * @return mixed
     */
    public function findBy($field, $value, $columns = ['*'])
    {
        return $this->model->where($field, $value)->first($columns);
    }
    
    /**
     * Tìm nhiều bản ghi theo điều kiện
     *
     * @param string $field
     * @param mixed $value
     * @param array $columns
     * @return mixed
     */
    public function findAllBy($field, $value, $columns = ['*'])
    {
        return $this->model->where($field, $value)->get($columns);
    }
} 