<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix posts production issue
        // 1. Set status = 'published' for all posts that don't have it
        DB::table('posts')
            ->where(function($query) {
                $query->where('status', '!=', 'published')
                      ->orWhereNull('status');
            })
            ->update(['status' => 'published']);

        // 2. Set published_at = created_at for posts that don't have published_at
        DB::table('posts')
            ->whereNull('published_at')
            ->update(['published_at' => DB::raw('created_at')]);

        // Log the results
        $totalPosts = DB::table('posts')->count();
        $readyPosts = DB::table('posts')
            ->where('status', 'published')
            ->whereNotNull('published_at')
            ->count();
            
        Log::info("Posts migration completed: {$readyPosts}/{$totalPosts} posts ready to display");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần reverse vì đây là fix data
    }
};
