<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ViewAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Đảm bảo technical_specs và downloads là arrays
        if (isset($data['technical_specs'])) {
            $data['technical_specs'] = is_array($data['technical_specs']) ? $data['technical_specs'] : [];
        }
        
        if (isset($data['downloads'])) {
            $data['downloads'] = is_array($data['downloads']) ? $data['downloads'] : [];
        }
        
        // Kiểm tra trùng lặp slug và tự động điều chỉnh nếu cần
        $originalSlug = $data['slug'];
        $slug = $originalSlug;
        $count = 1;
        
        while (\App\Models\Product::where('slug', $slug)->where('id', '!=', $record->id)->exists()) {
            $slug = $originalSlug . '-' . $count++;
        }
        
        if ($slug !== $originalSlug) {
            $data['slug'] = $slug;
            
            // Hiển thị thông báo nếu slug đã được điều chỉnh
            Notification::make()
                ->title('Thông báo về Slug')
                ->body("Slug '{$originalSlug}' đã tồn tại và đã được tự động điều chỉnh thành '{$slug}'.")
                ->success()
                ->send();
        }
        
        $record->update($data);
        
        return $record;
    }
} 