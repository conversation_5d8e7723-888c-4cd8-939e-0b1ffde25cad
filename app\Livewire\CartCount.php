<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Session;

class CartCount extends Component
{
    public $count = 0;

    public function mount()
    {
        $this->count = count(Session::get('cart', []));
    }

    public function render()
    {
        return view('livewire.cart-count');
    }

    public function getListeners()
    {
        return [
            'cart-updated' => 'updateCount',
            'add-to-cart' => 'updateCount'
        ];
    }

    public function updateCount()
    {
        $this->count = count(Session::get('cart', []));
    }
} 