<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropUnique('posts_slug_unique');
            $table->unique(['slug', 'lang']);
        });

        Schema::table('static_pages', function (Blueprint $table) {
            $table->dropUnique('static_pages_slug_unique');
            $table->unique(['slug', 'lang']);
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropUnique('products_slug_unique');
            $table->unique(['slug', 'lang']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            $table->dropUnique(['slug', 'lang']);
            $table->unique('slug');
        });

        Schema::table('static_pages', function (Blueprint $table) {
            $table->dropUnique(['slug', 'lang']);
            $table->unique('slug');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropUnique(['slug', 'lang']);
            $table->unique('slug');
        });
    }
};
