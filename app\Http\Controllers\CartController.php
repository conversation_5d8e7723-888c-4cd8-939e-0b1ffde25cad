<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    public function index()
    {
        $cartSession = Session::get('cart', []);
        $cartItems = [];
        $subtotal = 0;

        // Lặp qua session giỏ hàng để lấy thông tin sản phẩm
        foreach ($cartSession as $productId => $quantity) {
            $product = Product::find($productId);
            if ($product) {
                // Tính tổng tiền cho mỗi sản phẩm
                $item_total = ($product->sale_price ?? $product->price) * $quantity;
                
                // Thêm vào mảng để gửi sang view
                $cartItems[] = [
                    'product' => $product,
                    'quantity' => $quantity,
                    'item_total' => $item_total,
                ];

                // Tính tổng tiền của giỏ hàng
                $subtotal += $item_total;
            }
        }

        // Trả về view với dữ liệu giỏ hàng
        return view('templates.auvista.pages.cart', compact('cartItems', 'subtotal'));
    }

    public function update(Request $request, $productId)
    {
        $cart = Session::get('cart', []);

        if (!isset($cart[$productId])) {
            return back()->with('error', 'Sản phẩm không có trong giỏ hàng!');
        }

        $currentQuantity = $cart[$productId];
        $operation = $request->input('operation'); // 'plus', 'minus', or null

        if ($operation === 'plus') {
            $newQuantity = $currentQuantity + 1;
        } elseif ($operation === 'minus') {
            $newQuantity = $currentQuantity - 1;
        } else {
            // This handles the onchange event from the input field
            $newQuantity = (int) $request->input('quantity');
        }

        if ($newQuantity > 0) {
            $cart[$productId] = $newQuantity;
            Session::put('cart', $cart);
            Session::save(); // Force save session
            return back()->with('success', 'Cập nhật giỏ hàng thành công!');
        } else {
            // If new quantity is 0 or less, remove the product
            return $this->remove($productId);
        }
    }

    public function remove($productId)
    {
        $cart = Session::get('cart', []);

        if (isset($cart[$productId])) {
            unset($cart[$productId]);
            Session::put('cart', $cart);
            Session::save(); // Force save session
            return back()->with('success', 'Sản phẩm đã được xóa khỏi giỏ hàng!');
        }

        return back()->with('error', 'Không thể xóa sản phẩm!');
    }

    // AJAX Methods
    public function updateAjax(Request $request, $productId)
    {
        // Validate product exists
        $product = Product::find($productId);
        if (!$product) {
            return response()->json(['success' => false, 'message' => __('Product not found')]);
        }

        $cart = Session::get('cart', []);

        if (!isset($cart[$productId])) {
            return response()->json(['success' => false, 'message' => __('Product not in cart')]);
        }

        $currentQuantity = $cart[$productId];
        $operation = $request->input('operation');

        if ($operation === 'plus') {
            $newQuantity = $currentQuantity + 1;
        } elseif ($operation === 'minus') {
            $newQuantity = $currentQuantity - 1;
        } else {
            $newQuantity = (int) $request->input('quantity');
        }

        if ($newQuantity > 0) {
            $cart[$productId] = $newQuantity;
            Session::put('cart', $cart);
            Session::save(); // Force save session

            // Tính toán lại giỏ hàng
            $cartData = $this->getCartData();

            return response()->json([
                'success' => true,
                'message' => __('messages.cart_updated_successfully'),
                'quantity' => $newQuantity,
                'subtotal' => $cartData['subtotal'],
                'cart_count' => $cartData['cart_count'],
                'item_total' => $cartData['items'][$productId]['item_total'] ?? 0
            ]);
        } else {
            return $this->removeAjax($productId);
        }
    }

    public function removeAjax($productId)
    {
        // Validate product exists
        $product = Product::find($productId);
        if (!$product) {
            return response()->json(['success' => false, 'message' => __('Product not found')]);
        }

        $cart = Session::get('cart', []);

        if (isset($cart[$productId])) {
            unset($cart[$productId]);
            Session::put('cart', $cart);
            Session::save(); // Force save session

            $cartData = $this->getCartData();

            return response()->json([
                'success' => true,
                'message' => __('messages.product_removed_from_cart'),
                'subtotal' => $cartData['subtotal'],
                'cart_count' => $cartData['cart_count'],
                'removed' => true
            ]);
        }

        return response()->json(['success' => false, 'message' => __('messages.remove_product_error')]);
    }

    private function getCartData()
    {
        $cartSession = Session::get('cart', []);
        $cartItems = [];
        $subtotal = 0;

        foreach ($cartSession as $productId => $quantity) {
            $product = \App\Models\Product::find($productId);
            if ($product) {
                $item_total = ($product->sale_price ?? $product->price) * $quantity;
                $cartItems[$productId] = [
                    'product' => $product,
                    'quantity' => $quantity,
                    'item_total' => $item_total,
                ];
                $subtotal += $item_total;
            }
        }

        return [
            'items' => $cartItems,
            'subtotal' => $subtotal,
            'cart_count' => count($cartSession)
        ];
    }
} 