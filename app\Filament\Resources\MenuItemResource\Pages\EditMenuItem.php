<?php

namespace App\Filament\Resources\MenuItemResource\Pages;

use App\Filament\Resources\MenuItemResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Models\Menu;

class EditMenuItem extends EditRecord
{
    protected static string $resource = MenuItemResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        $menuId = request()->route('menu');
        return MenuItemResource::getUrl('index', ['menu' => $menuId]);
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $data['menu_id'] = request()->route('menu');
        return $data;
    }
}
