<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryFactory extends Factory
{
    protected $model = Category::class;

    public function definition(): array
    {
        $name = fake()->words(2, true);
        return [
            'name' => $name,
            'slug' => Str::slug($name),
            'description' => fake()->paragraph(),
            'type' => 'post',
            'status' => 'active',
            'seo_title' => fake()->sentence(),
            'seo_description' => fake()->paragraph(),
            'seo_keywords' => implode(',', fake()->words(5)),
        ];
    }
}