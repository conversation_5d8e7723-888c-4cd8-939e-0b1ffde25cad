<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    public function edit()
    {
        // Breadcrumbs cho trang thông tin tài khoản
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Thông tin tài khoản',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Thông tin tài khoản'
        ];

        // L<PERSON>y thông tin người dùng từ guard frontend
        $user = Auth::guard('frontend')->user();
        return view('templates.auvista.account.profile', compact('user', 'breadcrumbs'));
    }

    public function update(Request $request)
    {
        // Lấy thông tin người dùng từ guard frontend
        $user = Auth::guard('frontend')->user();
        
        // <PERSON><PERSON><PERSON> thực dữ liệu nhập vào
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:accounts,email,'.$user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'current_password' => 'nullable|required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);
        
        // Cập nhật thông tin cơ bản
        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'] ?? $user->phone;
        $user->address = $validated['address'] ?? $user->address;
        
        // Cập nhật mật khẩu nếu được cung cấp
        if (!empty($validated['password'])) {
            // Kiểm tra mật khẩu hiện tại
            if (!Hash::check($request->current_password, $user->password)) {
                return back()->withErrors(['current_password' => 'Mật khẩu hiện tại không chính xác']);
            }
            
            $user->password = Hash::make($validated['password']);
        }
        
        $user->save();
        
        return redirect()->route('profile.edit')->with('success', 'Thông tin tài khoản đã được cập nhật thành công.');
    }
}
