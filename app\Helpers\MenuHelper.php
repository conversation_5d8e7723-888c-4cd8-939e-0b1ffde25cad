<?php

namespace App\Helpers;

use App\Models\Menu;
use App\Models\MenuItem;

class MenuHelper
{
    /**
     * Get menu items by location
     */
    public static function getMenuItems($location)
    {
        $menu = Menu::where('location', $location)
            ->where('lang', app()->getLocale())
            ->where('status', true)
            ->first();

        if (!$menu) {
            return collect();
        }

        return MenuItem::where('menu_id', $menu->id)
            ->where('parent_id', null)
            ->where('status', true)
            ->orderBy('order')
            ->get()
            ->map(function ($item) {
                // Process URL to ensure it's a full URL
                $item->url = self::processUrl($item->url);
                return $item;
            });
    }

    public static function renderMenu($location, $options = [])
    {
        $menu = Menu::where('location', $location)
            ->where('lang', app()->getLocale())
            ->where('status', true)
            ->first();

        if (!$menu) {
            return '';
        }

        $menuItems = MenuItem::where('menu_id', $menu->id)
            ->where('parent_id', null)
            ->where('status', true)
            ->orderBy('order')
            ->get();

        $html = '<ul class="navbar-nav me-auto">';

        foreach ($menuItems as $item) {
            $hasChildren = MenuItem::where('parent_id', $item->id)
                ->where('status', true)
                ->exists();

            // Determine if this is the active menu item
            $isCurrent = self::isCurrentUrl($item->url);
            
            $html .= '<li class="nav-item' . ($hasChildren ? ' dropdown' : '') . ($isCurrent ? ' active' : '') . '">';
            
            // Process URL to ensure it's a full URL
            $url = self::processUrl($item->url);
            
            if ($hasChildren) {
                $html .= '<a class="nav-link dropdown-toggle' . ($isCurrent ? ' active' : '') . '" href="' . $url . '" data-bs-toggle="dropdown">';
            } else {
                $html .= '<a class="nav-link' . ($isCurrent ? ' active' : '') . '" href="' . $url . '">';
            }

            // Thêm icons dựa vào title hoặc icon property
            if ($item->icon) {
                $html .= '<i class="' . $item->icon . ' me-1"></i> ';
            } else {
                // Tự động thêm icon dựa vào tiêu đề
                if (stripos($item->title, 'sản phẩm') !== false) {
                    $html .= '<i class="fas fa-box-open me-1"></i> ';
                } elseif (stripos($item->title, 'liên hệ') !== false) {
                    $html .= '<i class="fas fa-envelope me-1"></i> ';
                } elseif (stripos($item->title, 'giới thiệu') !== false) {
                    $html .= '<i class="fas fa-info-circle me-1"></i> ';
                } elseif (stripos($item->title, 'tin tức') !== false) {
                    $html .= '<i class="fas fa-newspaper me-1"></i> ';
                } elseif (stripos($item->title, 'trang chủ') !== false) {
                    $html .= '<i class="fas fa-home me-1"></i> ';
                }
            }

            $html .= $item->title . '</a>';

            if ($hasChildren) {
                $children = MenuItem::where('parent_id', $item->id)
                    ->where('status', true)
                    ->orderBy('order')
                    ->get();

                $html .= '<ul class="dropdown-menu">';
                foreach ($children as $child) {
                    // Process child URL
                    $childUrl = self::processUrl($child->url);
                    $isChildCurrent = self::isCurrentUrl($child->url);
                    
                    $html .= '<li><a class="dropdown-item' . ($isChildCurrent ? ' active' : '') . '" href="' . $childUrl . '">';
                    
                    if ($child->icon) {
                        $html .= '<i class="' . $child->icon . ' me-1"></i> ';
                    }
                    
                    $html .= $child->title . '</a></li>';
                }
                $html .= '</ul>';
            }

            $html .= '</li>';
        }

        $html .= '</ul>';

        return $html;
    }

    /**
     * Check if the given URL matches the current URL path
     */
    private static function isCurrentUrl($url)
    {
        $currentPath = request()->path();
        $urlPath = parse_url($url, PHP_URL_PATH);
        
        // Remove leading/trailing slashes for comparison
        $currentPath = trim($currentPath, '/');
        $urlPath = trim($urlPath ?? '', '/');
        
        return $currentPath === $urlPath;
    }

    /**
     * Process a URL to ensure it's a full URL
     */
    private static function processUrl($url)
    {
        // Common route mappings
        $routeMappings = [
            'home' => '/',
            'trang-chu' => '/',
            'san-pham' => '/san-pham',
            'products' => '/san-pham',
            'gioi-thieu' => '/gioi-thieu',
            'tin-tuc' => '/tin-tuc',
            'lien-he' => '/lien-he',
        ];
        
        // If empty URL, default to home
        if (empty($url)) {
            return url('/');
        }
        
        // If it's already a full URL (starts with http:// or https://)
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            return $url;
        }
        
        // If it's a mapped route
        if (isset($routeMappings[$url])) {
            return url($routeMappings[$url]);
        }
        
        // Check if URL starts with 'products/' and map to 'san-pham/'
        if (str_starts_with($url, 'products/')) {
            return url('/san-pham/' . substr($url, 9));
        }
        
        // If it starts with a slash, it's a relative URL
        if (substr($url, 0, 1) === '/') {
            return url($url);
        }
        
        // Default: assume it's a relative URL
        return url('/' . $url);
    }
} 