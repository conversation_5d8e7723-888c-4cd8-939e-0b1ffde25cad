<?php
 
namespace App\Filament\Widgets;
 
use App\Models\Order;
use App\Models\Post;
use App\Models\Product;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;
 
class StatsOverview extends BaseWidget
{
    protected static ?int $sort = 1;
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Số đơn hàng và tổng doanh thu
        $totalOrders = Order::count();
        
        // Số lượng đơn hàng tháng này và tháng trước
        $currentMonthOrders = Order::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $lastMonthOrders = Order::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();
            
        $orderChangePercentage = $lastMonthOrders > 0 
            ? round((($currentMonthOrders - $lastMonthOrders) / $lastMonthOrders) * 100) 
            : 100;
        
        $orderChangeDirection = $orderChangePercentage >= 0 ? 'increase' : 'decrease';
        $orderChangeColor = $orderChangePercentage >= 0 ? 'success' : 'danger';
        $orderChangeIcon = $orderChangePercentage >= 0 
            ? 'heroicon-m-arrow-trending-up' 
            : 'heroicon-m-arrow-trending-down';
            
        // Doanh thu
        $totalRevenue = Order::where('status', '!=', 'cancelled')
            ->where('payment_status', 'paid')
            ->sum('total_amount');
            
        $currentMonthRevenue = Order::where('status', '!=', 'cancelled')
            ->where('payment_status', 'paid')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_amount');
            
        $lastMonthRevenue = Order::where('status', '!=', 'cancelled')
            ->where('payment_status', 'paid')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_amount');
            
        $revenueChangePercentage = $lastMonthRevenue > 0 
            ? round((($currentMonthRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100) 
            : 100;
            
        $revenueChangeDirection = $revenueChangePercentage >= 0 ? 'increase' : 'decrease';
        $revenueChangeColor = $revenueChangePercentage >= 0 ? 'success' : 'danger';
        $revenueChangeIcon = $revenueChangePercentage >= 0 
            ? 'heroicon-m-arrow-trending-up' 
            : 'heroicon-m-arrow-trending-down';
        
        // Số sản phẩm
        $totalProducts = Product::count();
        
        $currentMonthProducts = Product::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $lastMonthProducts = Product::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();
            
        $productsChangePercentage = $lastMonthProducts > 0 
            ? round((($currentMonthProducts - $lastMonthProducts) / $lastMonthProducts) * 100) 
            : 100;
            
        $productsChangeDirection = $productsChangePercentage >= 0 ? 'increase' : 'decrease';
        $productsChangeColor = $productsChangePercentage >= 0 ? 'success' : 'danger';
        $productsChangeIcon = $productsChangePercentage >= 0 
            ? 'heroicon-m-arrow-trending-up' 
            : 'heroicon-m-arrow-trending-down';
        
        // Số bài viết
        $totalPosts = Post::count();
        
        $currentMonthPosts = Post::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $lastMonthPosts = Post::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();
            
        $postsChangePercentage = $lastMonthPosts > 0 
            ? round((($currentMonthPosts - $lastMonthPosts) / $lastMonthPosts) * 100) 
            : 100;
            
        $postsChangeDirection = $postsChangePercentage >= 0 ? 'increase' : 'decrease';
        $postsChangeColor = $postsChangePercentage >= 0 ? 'success' : 'danger';
        $postsChangeIcon = $postsChangePercentage >= 0 
            ? 'heroicon-m-arrow-trending-up' 
            : 'heroicon-m-arrow-trending-down';
        
        return [
            Stat::make('Số đơn hàng', number_format($totalOrders))
                ->description(abs($orderChangePercentage) . '% ' . $orderChangeDirection)
                ->descriptionIcon($orderChangeIcon)
                ->color($orderChangeColor)
                ->chart([0, 0, $lastMonthOrders, $currentMonthOrders])
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                ])
                ->url(route('filament.admin.resources.orders.index')),
                
            Stat::make('Tổng doanh thu', number_format($totalRevenue, 0, ',', '.') . ' VNĐ')
                ->description(abs($revenueChangePercentage) . '% ' . $revenueChangeDirection)
                ->descriptionIcon($revenueChangeIcon)
                ->color($revenueChangeColor)
                ->chart([0, 0, $lastMonthRevenue/1000, $currentMonthRevenue/1000])
                ->icon('heroicon-o-banknotes')
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                ])
                ->url(route('filament.admin.resources.orders.index')),
                
            Stat::make('Số sản phẩm', number_format($totalProducts))
                ->description(abs($productsChangePercentage) . '% ' . $productsChangeDirection)
                ->descriptionIcon($productsChangeIcon)
                ->color($productsChangeColor)
                ->chart([0, 0, $lastMonthProducts, $currentMonthProducts])
                ->icon('heroicon-o-shopping-bag')
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                ])
                ->url(route('filament.admin.resources.products.index')),
                
            Stat::make('Số bài viết', number_format($totalPosts))
                ->description(abs($postsChangePercentage) . '% ' . $postsChangeDirection)
                ->descriptionIcon($postsChangeIcon)
                ->color($postsChangeColor)
                ->chart([0, 0, $lastMonthPosts, $currentMonthPosts])
                ->icon('heroicon-o-document-text')
                ->extraAttributes([
                    'class' => 'cursor-pointer',
                ])
                ->url(route('filament.admin.resources.posts.index')),
        ];
    }
}