<?php

namespace App\Repositories;

use App\Models\Category;

class CategoryRepository
{
    protected $model;

    public function __construct(Category $model)
    {
        $this->model = $model;
    }

    /**
     * L<PERSON>y danh mục nổi bật
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedCategories($limit = 3)
    {
        return $this->model
            // ->where('is_featured', true)
            ->where('status', true)
            ->orderBy('updated_at')
            ->take($limit)
            ->get();
    }

    /**
     * Lấy tất cả danh mục
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllCategories()
    {
        return $this->model->where('status', true)
            ->orderBy('updated_at')
            ->get();
    }
} 