<?php

namespace App\Filament\Resources\MediaResource\Pages;

use App\Filament\Resources\MediaResource;
use App\Services\MediaService;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateMedia extends CreateRecord
{
    protected static string $resource = MediaResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Lấy file upload từ form
        $file = $data['file_upload'];
        
        // Lấy MediaService từ service container
        $mediaService = app(MediaService::class);
        
        // Upload file và tạo media record
        $media = $mediaService->upload($file, 'media/uploads');
        
        // Cập nhật thêm thông tin từ form
        $media->update([
            'name' => $data['name'] ?? $media->name,
            'alt' => $data['alt'] ?? null,
            'title' => $data['title'] ?? null,
            'description' => $data['description'] ?? null,
        ]);
        
        return $media;
    }
} 