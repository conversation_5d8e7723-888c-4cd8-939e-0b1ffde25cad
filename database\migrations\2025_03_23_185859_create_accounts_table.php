<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('avatar')->nullable(); // Ảnh đại diện
            $table->string('phone')->nullable(); // Số điện thoại
            $table->enum('status', ['active', 'inactive', 'banned'])->default('active'); // Trạng thái tài khoản
            $table->enum('account_type', ['regular', 'distributor'])->default('regular'); // Phân loại tài khoản
            $table->string('id_number')->nullable(); // CMND/CCCD (dành cho đại lý)
            $table->string('address')->nullable(); // Địa chỉ
            $table->string('bank_name')->nullable(); // Tên ngân hàng (dành cho đại lý)
            $table->string('bank_account')->nullable(); // Số tài khoản (dành cho đại lý)
            $table->string('bank_branch')->nullable(); // Chi nhánh ngân hàng (dành cho đại lý)
            $table->string('distributor_code')->nullable(); // Mã nhà phân phối giới thiệu
            $table->boolean('newsletter')->default(false); // Đăng ký nhận bản tin
            $table->rememberToken();
            $table->timestamps();
        });

        Schema::create('account_password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('account_verification_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('expires_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_verification_tokens');
        Schema::dropIfExists('account_password_reset_tokens');
        Schema::dropIfExists('accounts');
    }
};
