{{-- <PERSON><PERSON> v<PERSON><PERSON> CSS - <PERSON><PERSON><PERSON><PERSON> đ<PERSON>i từ HTML component --}}
<header data-property-1="Default"
    class="bg-primary-main fixed top-0 left-0 right-0 z-50 shadow-lg transition-all duration-300">
    <div class="header-wrapper container mx-auto">
        <div class="self-stretch flex flex-col justify-start items-start">
            <div class="self-stretch inline-flex justify-between items-center pt-4 pb-4 md:pb-0 gap-4">
                {{-- Logo --}}
                <div class="logo">
                    <a href="{{ route('home') }}">
                        <img class="w-41 h-14 rounded" src="{{ asset('storage/images/logo.png') }}"
                            alt="{{ getSetting('site_name', 'AVPlus') }} Logo" />
                    </a>
                </div>

                {{-- Search Form --}}
                <div class="hidden md:flex justify-start items-center w-full max-w-[880px]">
                    <form class="flex items-center w-full focus:outline-none focus:ring-0 focus:border-none"
                        action="{{ route('products.search') }}" method="GET">
                        <label for="search" class="sr-only">{{ __('messages.search_label') }}</label>
                        <input type="text" id="search" name="q"
                            class="block w-full md:max-w-[836px] h-11 px-3 py-2 focus:right-0 rounded-l-md border-gray-300 md:text-lg"
                            placeholder="{{ __('messages.search_placeholder') }}" autocomplete="off" />
                        <button type="submit"
                            class="inline-flex items-center px-3 py-3 h-11 bg-brand-gradient text-white rounded-r-md focus:outline-none focus:ring-0 focus:border-none">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z" />
                            </svg>
                        </button>
                    </form>
                </div>

                {{-- Right Section: Cart, Language, Mobile Menu --}}
                <div class="flex justify-start items-center gap-6">
                    {{-- Cart Link --}}
                    <a href="#" data-bs-toggle="modal" data-bs-target="#cartModal"
                        class="flex justify-start items-center gap-2" title="{{ __('messages.cart_title') }}">
                        <div class="w-6 h-6 relative">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z"
                                    fill="white" />
                                <path
                                    d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z"
                                    fill="white" />
                                <path
                                    d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z"
                                    fill="white" />
                                <path
                                    d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z"
                                    fill="white" />
                            </svg>
                        </div>
                        <span
                            class="hidden lg:flex whitespace-nowrap justify-start text-white md:text-lg font-medium font-neue leading-relaxed">
                            {{ __('messages.cart') }}
                        </span>
                        {{-- Cart Count --}}
                        <livewire:cart-count />
                    </a>

                    {{-- Language Flags --}}
                    <div class="flex justify-start items-center gap-3">
                        <a href="{{ route('lang', ['lang' => 'vi']) }}"
                            class="w-[34px] h-6 {{ app()->getLocale() == 'vi' ? 'opacity-100' : 'opacity-50' }}">
                            <img src="{{ asset('images/flag-vi.png') }}" alt="Flag VN Logo" />
                        </a>
                        <a href="{{ route('lang', ['lang' => 'en']) }}"
                            class="w-[34px] h-6 {{ app()->getLocale() == 'en' ? 'opacity-100' : 'opacity-50' }}">
                            <img src="{{ asset('images/flag-en.png') }}" alt="Flag EN Logo" />
                        </a>
                    </div>

                    {{-- Mobile Menu Button --}}
                    <button
                        class="menu-mobile-toggle md:hidden flex items-center justify-center bg-white w-8 h-8 text-secondary-main rounded-md"
                        onclick="toggleMobileMenu()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            {{-- Navigation Menu --}}
            <div id="site-navigation"
                class="navigation self-stretch hidden md:inline-flex pb-3 -mt-4 justify-center items-center"
                style="margin-top: -16px !important;">
                <div class="flex justify-start items-center gap-10 lg:text-lg mainMenu">
                    {{-- Dynamic Menu từ MenuHelper --}}
                    @php
                        $mainMenu = \App\Helpers\MenuHelper::getMenuItems('main-menu');
                    @endphp

                    @if ($mainMenu)
                        @foreach ($mainMenu as $menuItem)
                            <div class="relative group">
                                <a href="{{ $menuItem->url }}"
                                    class="nav-link text-white hover:text-gray-200 transition-colors flex items-center"
                                    {{ $menuItem->target == '_blank' ? 'target="_blank"' : '' }}>
                                    {{ $menuItem->title }}
                                    @if ($menuItem->children && $menuItem->children->count() > 0)
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    @endif
                                </a>

                                {{-- Dropdown Menu --}}
                                @if ($menuItem->children && $menuItem->children->count() > 0)
                                    <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                                        <div class="py-2">
                                            @foreach ($menuItem->children as $childItem)
                                                <a href="{{ $childItem->url }}"
                                                    class="block px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-primary-main transition-colors"
                                                    {{ $childItem->target == '_blank' ? 'target="_blank"' : '' }}>
                                                    {{ $childItem->title }}
                                                </a>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    @else
                        {{-- Fallback static menu nếu không có dynamic menu --}}
                        <a href="{{ route('page.about') }}"
                            class="nav-link text-white">{{ __('messages.about_us') }}</a>
                        <a href="{{ route('products.index') }}"
                            class="nav-link text-white">{{ __('messages.products') }}</a>
                        <a href="#" class="nav-link text-white">{{ __('messages.solutions') }}</a>
                        <a href="#" class="nav-link text-white">{{ __('messages.brands') }}</a>
                        <a href="#" class="nav-link text-white">{{ __('messages.projects') }}</a>
                        <a href="{{ route('posts.index') }}" class="nav-link text-white">{{ __('messages.news') }}</a>
                        <a href="{{ route('static.page', ['slug' => 'lien-he']) }}" class="nav-link text-white">{{ __('messages.contact') }}</a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</header>

{{-- Mobile Menu Overlay --}}
<div class="mobile-menu-overlay fixed inset-0 bg-black/50 z-40 hidden"></div>

{{-- Mobile Menu Sidebar --}}
<div
    class="mobile-menu-sidebar fixed top-0 left-0 h-full w-80 bg-primary-main z-50 transform -translate-x-full transition-transform duration-300 ease-in-out hidden">
    <div class="flex flex-col h-full">
        {{-- Header --}}
        <div class="flex items-center justify-between p-4 border-b border-white/20">
            <div class="logo">
                <img class="w-32 h-10 rounded" src="{{ asset('storage/images/logo.png') }}"
                    alt="{{ getSetting('site_name', 'AVPlus') }} Logo" />
            </div>
            <button class="close-mobile-menu text-white p-2" onclick="closeMobileMenu()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        {{-- Search Bar --}}
        <div class="p-4 border-b border-white/20">
            <form class="flex items-center" action="{{ route('products.search') }}" method="GET">
                <input type="text" name="q"
                    class="block w-full h-11 px-3 py-2 rounded-l-md border-gray-300 text-gray-900"
                    placeholder="{{ __('messages.search_placeholder') }}" autocomplete="off" />
                <button type="submit"
                    class="inline-flex items-center px-3 py-3 h-11 bg-brand-gradient text-white rounded-r-md">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z" />
                    </svg>
                </button>
            </form>
        </div>

        {{-- Menu Items --}}
        <div class="flex-1 p-4">
            <nav class="space-y-4">
                @if ($mainMenu)
                    @foreach ($mainMenu as $menuItem)
                        <a href="{{ $menuItem->url }}"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                            {{ $menuItem->target == '_blank' ? 'target="_blank"' : '' }}>
                            {{ $menuItem->title }}
                        </a>
                    @endforeach
                @else
                    <a href="{{ route('page.about') }}"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.about_us') }}
                    </a>
                    <a href="{{ route('products.index') }}"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.products') }}
                    </a>
                    <a href="#"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.solutions') }}
                    </a>
                    <a href="#"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.brands') }}
                    </a>
                    <a href="#"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.projects') }}
                    </a>
                    <a href="{{ route('posts.index') }}"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.news') }}
                    </a>
                    <a href="{{ route('static.page', ['slug' => 'lien-he']) }}"
                        class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors">
                        {{ __('messages.contact') }}
                    </a>
                @endif
            </nav>
        </div>

        <div class="p-4 mt-auto border-t border-white/20">
            <a href="#" data-bs-toggle="modal" data-bs-target="#cartModal"
                class="flex items-center gap-2 text-white">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z"
                        fill="white"></path>
                    <path
                        d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z"
                        fill="white"></path>
                    <path
                        d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z"
                        fill="white"></path>
                    <path
                        d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z"
                        fill="white"></path>
                </svg>
                <span class="font-medium">{{ __('messages.cart') }}</span>
                <livewire:cart-count />
            </a>
        </div>
    </div>
</div>

{{-- Mobile Menu JavaScript --}}
<script>
    function toggleMobileMenu() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        const sidebar = document.querySelector('.mobile-menu-sidebar');

        if (overlay && sidebar) {
            overlay.classList.toggle('hidden');
            sidebar.classList.toggle('hidden');
            setTimeout(() => {
                sidebar.classList.toggle('-translate-x-full');
            }, 10);
        }
    }

    function closeMobileMenu() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        const sidebar = document.querySelector('.mobile-menu-sidebar');

        if (overlay && sidebar) {
            sidebar.classList.add('-translate-x-full');
            setTimeout(() => {
                overlay.classList.add('hidden');
                sidebar.classList.add('hidden');
            }, 300);
        }
    }

    // Close mobile menu when clicking overlay
    document.addEventListener('DOMContentLoaded', function() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        if (overlay) {
            overlay.addEventListener('click', closeMobileMenu);
        }
    });
</script>
