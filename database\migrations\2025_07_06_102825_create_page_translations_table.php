<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_translations', function (Blueprint $table) {
            $table->id();
            $table->string('type')->comment('Type of page: static_page, post, category, etc.');
            $table->string('code')->comment('Unique code to identify the same content across languages');
            $table->unsignedBigInteger('page_id')->comment('ID of the page in the respective table');
            $table->string('lang', 2)->comment('Language code: vi, en, etc.');
            $table->timestamps();

            // Indexes for better performance
            $table->index(['type', 'code']);
            $table->index(['type', 'page_id']);
            $table->index(['code', 'lang']);
            
            // Unique constraint to prevent duplicate translations
            $table->unique(['type', 'code', 'lang']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('page_translations');
    }
};
