{"__meta": {"id": "01K092W929KX2FVNGZ2B6S3HR8", "datetime": "2025-07-16 14:41:10", "utime": **********.601876, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[14:41:10] LOG.info: CartPopup loadCart called: {\n    \"cartItems\": [],\n    \"session_cart\": [],\n    \"total\": 0,\n    \"session_id\": \"wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.574944, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752651669.927827, "end": **********.601911, "duration": 0.6740841865539551, "duration_str": "674ms", "measures": [{"label": "Booting", "start": 1752651669.927827, "relative_start": 0, "end": **********.317205, "relative_end": **********.317205, "duration": 0.*****************, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.317223, "relative_start": 0.*****************, "end": **********.601914, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.475883, "relative_start": 0.****************, "end": **********.478485, "relative_end": **********.478485, "duration": 0.002602100372314453, "duration_str": "2.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.544084, "relative_start": 0.****************, "end": **********.599409, "relative_end": **********.599409, "duration": 0.*****************, "duration_str": "55.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.auvista.pages.home", "start": **********.546222, "relative_start": 0.****************, "end": **********.546222, "relative_end": **********.546222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.layouts.default", "start": **********.549679, "relative_start": 0.****************, "end": **********.549679, "relative_end": **********.549679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.components.product_styles", "start": **********.552948, "relative_start": 0.6251211166381836, "end": **********.552948, "relative_end": **********.552948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.header", "start": **********.553199, "relative_start": 0.6253721714019775, "end": **********.553199, "relative_end": **********.553199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.561081, "relative_start": 0.6332540512084961, "end": **********.561081, "relative_end": **********.561081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.570599, "relative_start": 0.6427721977233887, "end": **********.570599, "relative_end": **********.570599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.breadcrumb", "start": **********.571431, "relative_start": 0.643604040145874, "end": **********.571431, "relative_end": **********.571431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "start": **********.575583, "relative_start": 0.6477560997009277, "end": **********.575583, "relative_end": **********.575583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.footer", "start": **********.57847, "relative_start": 0.6506431102752686, "end": **********.57847, "relative_end": **********.57847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 42992568, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "templates.auvista.pages.home", "param_count": null, "params": [], "start": **********.546199, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/pages/home.blade.phptemplates.auvista.pages.home", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fpages%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "templates.auvista.layouts.default", "param_count": null, "params": [], "start": **********.549662, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/layouts/default.blade.phptemplates.auvista.layouts.default", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "templates.auvista.components.product_styles", "param_count": null, "params": [], "start": **********.552928, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/components/product_styles.blade.phptemplates.auvista.components.product_styles", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fcomponents%2Fproduct_styles.blade.php&line=1", "ajax": false, "filename": "product_styles.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.header", "param_count": null, "params": [], "start": **********.553184, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.phptemplates.auvista.blocks.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.561063, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.570583, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.breadcrumb", "param_count": null, "params": [], "start": **********.571414, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/breadcrumb.blade.phptemplates.auvista.blocks.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "param_count": null, "params": [], "start": **********.575566, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.phpH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-popup.blade.php&line=1", "ajax": false, "filename": "cart-popup.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.footer", "param_count": null, "params": [], "start": **********.57845, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.phptemplates.auvista.blocks.footer", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03160999999999999, "accumulated_duration_str": "31.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `banners` where `position` = 'slider' and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": ["slider", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BannerRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.49422, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "BannerRepository.php:42", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BannerRepository.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FBannerRepository.php&line=42", "ajax": false, "filename": "BannerRepository.php", "line": "42"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 10.693}, {"sql": "select * from `categories` where `status` = 1 order by `updated_at` asc limit 3", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CategoryRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\CategoryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.4990041, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CategoryRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\CategoryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FCategoryRepository.php&line=29", "ajax": false, "filename": "CategoryRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 10.693, "width_percent": 3.227}, {"sql": "select * from `products` where `is_new` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5009851, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 13.92, "width_percent": 3.069}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.504009, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 16.988, "width_percent": 2.847}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.50577, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 19.835, "width_percent": 3.195}, {"sql": "select * from `products` where `is_best_seller` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.507633, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 23.031, "width_percent": 2.183}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.509151, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 25.214, "width_percent": 1.297}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5103922, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 26.511, "width_percent": 1.455}, {"sql": "select * from `products` where `is_featured` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.511598, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 27.966, "width_percent": 2.056}, {"sql": "select * from `categories` where `categories`.`id` in (3, 5, 7, 9, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5131469, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 30.022, "width_percent": 1.329}, {"sql": "select * from `brands` where `brands`.`id` in (2, 3, 5, 11, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5143929, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 31.351, "width_percent": 2.373}, {"sql": "select `brands`.*, (select count(*) from `products` where `brands`.`id` = `products`.`brand_id`) as `products_count` from `brands` where `status` = 1 order by `products_count` desc, `name` asc limit 3", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BrandRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.517744, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BrandRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FBrandRepository.php&line=29", "ajax": false, "filename": "BrandRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 33.724, "width_percent": 2.626}, {"sql": "select * from `posts` where `status` = 'published' and `published_at` is not null order by `published_at` desc limit 3", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.519491, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 36.349, "width_percent": 5.568}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.526261, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 41.917, "width_percent": 5.884}, {"sql": "select `categories`.*, `post_category`.`post_id` as `pivot_post_id`, `post_category`.`category_id` as `pivot_category_id`, `post_category`.`is_primary` as `pivot_is_primary`, `post_category`.`created_at` as `pivot_created_at`, `post_category`.`updated_at` as `pivot_updated_at` from `categories` inner join `post_category` on `categories`.`id` = `post_category`.`category_id` where `post_category`.`post_id` in (15, 16, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.532515, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 19, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 47.801, "width_percent": 4.176}, {"sql": "select * from `page_translations` where `type` = 'static_page' and `code` = 'home' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["static_page", "home", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 20}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5369542, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "TranslationHelper.php:20", "source": {"index": 16, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FTranslationHelper.php&line=20", "ajax": false, "filename": "TranslationHelper.php", "line": "20"}, "connection": "auvista", "explain": null, "start_percent": 51.977, "width_percent": 2.088}, {"sql": "select * from `static_pages` where `static_pages`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 21}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.539392, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "TranslationHelper.php:21", "source": {"index": 21, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FTranslationHelper.php&line=21", "ajax": false, "filename": "TranslationHelper.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 54.065, "width_percent": 3.069}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 86}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.5411649, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:86", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=86", "ajax": false, "filename": "HomeController.php", "line": "86"}, "connection": "auvista", "explain": null, "start_percent": 57.134, "width_percent": 2.309}, {"sql": "select * from `settings` where `name` = 'site_logo' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_logo", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5504081, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 59.443, "width_percent": 2.12}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.553967, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 61.563, "width_percent": 3.322}, {"sql": "select * from `menus` where `location` = 'main-menu' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["main-menu", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.563081, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 64.885, "width_percent": 5.631}, {"sql": "select * from `menu_items` where `menu_id` = 5 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.5662968, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 70.516, "width_percent": 3.606}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.568535, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 74.122, "width_percent": 1.392}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.579115, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 75.514, "width_percent": 2.341}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.580613, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 77.855, "width_percent": 1.171}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.581587, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 79.026, "width_percent": 1.139}, {"sql": "select * from `settings` where `name` = 'contact_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.582587, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 80.165, "width_percent": 1.076}, {"sql": "select * from `settings` where `name` = 'office_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["office_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.583622, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 81.24, "width_percent": 1.171}, {"sql": "select * from `settings` where `name` = 'contact_email' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_email", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.584684, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 82.411, "width_percent": 1.171}, {"sql": "select * from `menus` where `location` = 'footer-support' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-support", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.586082, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 83.581, "width_percent": 3.733}, {"sql": "select * from `menu_items` where `menu_id` = 6 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.58818, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 87.314, "width_percent": 2.183}, {"sql": "select * from `menus` where `location` = 'footer-policy' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-policy", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.590026, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 89.497, "width_percent": 1.677}, {"sql": "select * from `menu_items` where `menu_id` = 7 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.591445, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 91.174, "width_percent": 1.93}, {"sql": "select * from `settings` where `name` = 'facebook_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["facebook_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5930681, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 93.103, "width_percent": 2.721}, {"sql": "select * from `settings` where `name` = 'instagram_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["instagram_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.594729, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 95.824, "width_percent": 1.139}, {"sql": "select * from `settings` where `name` = 'youtube_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["youtube_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.595785, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 96.963, "width_percent": 1.107}, {"sql": "select * from `settings` where `name` = 'site_copyright' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_copyright", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.596791, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 98.07, "width_percent": 0.886}, {"sql": "select * from `settings` where `name` = 'designer_credit' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["designer_credit", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.597714, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 98.956, "width_percent": 1.044}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Setting": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Product": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Brand": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PageTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPageTranslation.php&line=1", "ajax": false, "filename": "PageTranslation.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}}, "count": 67, "is_counter": true}, "livewire": {"data": {"cart-count #cGh7OwkEQL4W8NCxn56z": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"cGh7OwkEQL4W8NCxn56z\"\n]", "cart-count #nRIjk1D2PaVaNfiz4yr5": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"nRIjk1D2PaVaNfiz4yr5\"\n]", "cart-popup #SCC0HjBNcYg90T8wJXbA": "array:4 [\n  \"data\" => array:2 [\n    \"cartItems\" => []\n    \"total\" => 0\n  ]\n  \"name\" => \"cart-popup\"\n  \"component\" => \"App\\Livewire\\CartPopup\"\n  \"id\" => \"SCC0HjBNcYg90T8wJXbA\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:58-101</a>", "middleware": "web", "duration": "675ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1244273981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1244273981\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1803151997 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1803151997\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://auvista.test/san-pham?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik9PaDgycFJIdVNaZi83d2dQdVJDK0E9PSIsInZhbHVlIjoiWFVGMnJXY0VTd3o2WU9KUmhDbFpiVi9uM3d0TWVPNUlTT2dXLzJXWm5qOG5JK25Cc05Wa0ZveEZiK3IyZXpuTzRqNmFnNWp6THNGRURRYlpJaStSQW8ybnBVdUtQOWg1RnRNYVdYWmZ1M1ZrRzZDbDFQakxDVGp5ZHo3V2pmN1giLCJtYWMiOiIxOTU5OTc0OWYyYzAxMTU0MzdlYmRiODQ5MDc4ZDQwNmMyZGIwNDk4OTU2MDQ5ODhlZTAxMDZjMmYwMmYxNmYwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im5MWFBZdjlFS0VVQTBkOFlCck8wb2c9PSIsInZhbHVlIjoiVkd3K3NFdXZ6Qmljd253QmZSc2Y1aGRmaHNYT2t5bElyQTRXem1iUjl0NGl0SW9xakVjWnh1SFhWbUxHQlQreDJJMGEzNHNGZEZqbmJyWE94d3VrVzArTzJ5ZHMwSkRnUWFMcm5uNHhCTU1DSlVFTDdKdkhwSWxOdkQwM1Y0MEciLCJtYWMiOiJiMGEzOGVlYzM5NjcyMTY2ZGJjOWUyZjQzNzg1N2Y0Y2MxMTRkM2NiZGJhNjM1MmUzOTk0M2VlNzE2YzRmZGQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1790940346 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790940346\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2123490043 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:41:10 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2123490043\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1946313085 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946313085\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}