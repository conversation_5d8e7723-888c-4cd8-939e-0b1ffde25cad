<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\File;

class LanguageEditor extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-language';
    protected static ?string $navigationGroup = 'Hệ thống';
    protected static ?string $navigationLabel = 'Sửa file ngôn ngữ';
    protected static ?int $navigationSort = 10;
    protected static string $view = 'filament.pages.language-editor';

    public array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'locale' => 'vi',
            'content' => ''
        ]);
        $this->loadFileContent();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('locale')
                    ->label('Ngôn ngữ')
                    ->options([
                        'vi' => 'Tiếng Việt',
                        'en' => 'English',
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(fn () => $this->loadFileContent()),
                Textarea::make('content')
                    ->label('Nội dung file messages.php')
                    ->rows(30)
                    ->extraAttributes(['class' => 'font-mono'])
                    ->placeholder('<?php\n\nreturn [\n    // Thêm các khóa ngôn ngữ ở đây\n];')
                    ->required(),
            ])
            ->statePath('data');
    }

    public function loadFileContent(): void
    {
        $locale = $this->data['locale'] ?? 'vi';
        $filePath = lang_path("{$locale}/messages.php");
        if (File::exists($filePath)) {
            $content = File::get($filePath);
            $this->form->fill(['content' => $content]);
        } else {
            $this->form->fill(['content' => "<?php\n\nreturn [\n    // Thêm các khóa ngôn ngữ ở đây\n];"]);
        }
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $locale = $data['locale'];
        $content = $data['content'];
        $langPath = lang_path($locale);
        if (!File::exists($langPath)) {
            File::makeDirectory($langPath, 0755, true);
        }
        $filePath = $langPath . '/messages.php';
        try {
            File::put($filePath, $content);
            Notification::make()
                ->success()
                ->title('Thành công')
                ->body('Đã lưu file ngôn ngữ messages.php.')
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body('Không thể lưu file: ' . $e->getMessage())
                ->send();
        }
    }

    public function getTitle(): string
    {
        return 'Sửa file ngôn ngữ';
    }

    public function updatedData($value, $key)
    {
        $this->data[$key] = $value;
    }
} 