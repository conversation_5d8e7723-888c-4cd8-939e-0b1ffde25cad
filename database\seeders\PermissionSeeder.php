<?php

namespace Database\Seeders;

use App\Helpers\ModuleRegistry;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionSeeder extends Seeder
{
    public function run(): void
    {
        // Tạo permissions từ ModuleRegistry
        $modules = ModuleRegistry::getModules();
        
        foreach ($modules as $module => $config) {
            foreach ($config['description'] as $action => $description) {
                Permission::findOrCreate("{$module}.{$action}", 'web')
                    ->update([
                        'group' => $config['group'],
                        'description' => $description
                    ]);
            }
        }
        
        // Tạo một số quyền hệ thống đặc biệt
        $systemPermissions = [
            'admin' => [
                'admin.access' => 'Truy cập vào trang quản trị',
                'admin.settings' => 'Thay đổi cài đặt hệ thống',
            ],
        ];

        foreach ($systemPermissions as $group => $permissions) {
            foreach ($permissions as $permission => $description) {
                Permission::findOrCreate($permission, 'web')
                    ->update([
                        'group' => $group,
                        'description' => $description
                    ]);
            }
        }

        // Tạo và cấp quyền cho role super-admin
        $adminRole = Role::findOrCreate('super-admin', 'web');
        $adminRole->givePermissionTo(Permission::all());
        
        // Tạo role editor với một số quyền cơ bản
        $editorRole = Role::findOrCreate('editor', 'web');
        $editorRole->givePermissionTo([
            'static-page.view', 'static-page.create', 'static-page.edit',
            'post.view', 'post.create', 'post.edit',
            'category.view',
            'media.view', 'media.upload',
        ]);
    }
}