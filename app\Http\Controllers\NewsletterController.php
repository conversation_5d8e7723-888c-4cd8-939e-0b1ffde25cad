<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Contact;

class NewsletterController extends Controller
{
    /**
     * Subscribe to newsletter
     */
    public function subscribe(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|max:255',
        ]);

        // Check if email exists in users or accounts table
        $user = User::where('email', $validated['email'])->first();
        
        if ($user) {
            // Update existing user
            $user->update(['newsletter' => true]);
            return response()->json([
                'success' => true,
                'message' => 'Email đã được đăng ký nhận tin thành công.'
            ]);
        }
        
        // Check if contact exists
        $contact = Contact::where('email', $validated['email'])->first();
        
        if ($contact) {
            // Update existing contact
            $contact->update([
                'is_newsletter' => true
            ]);
        } else {
            // Create new contact
            Contact::create([
                'name' => $request->input('name', 'Subscriber'),
                'email' => $validated['email'],
                'phone' => $request->input('phone', ''),
                'subject' => 'Đăng ký nhận bản tin',
                'message' => 'Đăng ký nhận tin tức và khuyến mãi',
                'is_newsletter' => true,
                'privacy_consent' => true,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Email đã được đăng ký nhận tin thành công.'
        ]);
    }
}
