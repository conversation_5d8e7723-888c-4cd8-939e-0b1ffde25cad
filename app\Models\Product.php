<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Auth;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'sku',
        'description',
        'price',
        'sale_price',
        'stock',
        'status',
        'is_featured',
        'is_new',
        'is_best_seller',
        'image_url',
        'gallery',
        'category_id',
        'brand_id',
        'average_rating',
        'reviews_count',
        'ingredients',
        'benefits',
        'target_users',
        'usage_instructions',
        'advantages',
        'packaging_info',
        'contraindications',
        'product_type',
        'volume',
        'sugar_content',
        'daily_limit',
        'storage_instructions',
        'manufacturer',
        'product_images',
        'specifications',
        'technical_specs',
        'downloads',
        // Thông tin chi tiết
        'origin',
        'warranty',
        'unit',
        'condition',
        'lang' // Thêm trường lang
    ];

    protected $casts = [
        'is_featured' => 'boolean',
        'is_new' => 'boolean',
        'is_best_seller' => 'boolean',
        'status' => 'boolean',
        'price' => 'float',
        'sale_price' => 'float',
        'stock' => 'integer',
        'gallery' => 'array',
        'average_rating' => 'float',
        'reviews_count' => 'integer',
        'product_images' => 'array',
        'specifications' => 'array',
        'technical_specs' => 'array',
        'downloads' => 'array'
        // usage_instructions là text nên không cần cast thành array
    ];

    /**
     * Accessor để đảm bảo technical_specs luôn là array
     */
    public function getTechnicalSpecsAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * Accessor để đảm bảo downloads luôn là array
     */
    public function getDownloadsAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    /**
     * Accessor để đảm bảo gallery luôn là array
     */
    public function getGalleryAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }
        return is_array($value) ? $value : [];
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Quan hệ với khuyến mãi sản phẩm
     */
    public function promotions()
    {
        return $this->belongsToMany(ProductPromotion::class, 'product_promotion_pivot');
    }

    /**
     * Lấy khuyến mãi đang áp dụng cho sản phẩm
     */
    public function getActivePromotionAttribute()
    {
        return $this->promotions()
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->first();
    }

    public function getFullImageUrlAttribute()
    {
        return $this->image_url ? asset('storage/' . $this->image_url) : asset('images/no-image.jpg');
    }

    public function getFormattedRatingAttribute()
    {
        return number_format($this->average_rating, 1);
    }

    /**
     * Tính phần trăm giảm giá
     */
    public function getDiscountAttribute()
    {
        if ($this->sale_price && $this->price && $this->sale_price < $this->price) {
            return round((($this->price - $this->sale_price) / $this->price) * 100);
        }
        return 0;
    }

    /**
     * Quan hệ với đánh giá sản phẩm
     */
    public function reviews()
    {
        return $this->hasMany(ProductReview::class);
    }

    /**
     * Lấy các đánh giá đã được phê duyệt
     */
    public function approvedReviews()
    {
        return $this->reviews()->where('status', true)->orderBy('created_at', 'desc');
    }

    /**
     * Thêm đánh giá mới vào sản phẩm
     */
    public function addReview($reviewData)
    {
        $review = $this->reviews()->create([
            'reviewer_name' => $reviewData['reviewer_name'],
            'reviewer_email' => $reviewData['reviewer_email'],
            'rating' => (int)$reviewData['rating'],
            'comment' => $reviewData['comment'],
            'recommend' => isset($reviewData['recommend']) && $reviewData['recommend'] ? true : false,
            'user_id' => Auth::check() ? Auth::id() : null,
            'verified_purchase' => false,
            'helpful_count' => 0,
            'status' => true, // có thể đổi thành false nếu muốn kiểm duyệt trước
        ]);
        
        // Cập nhật thống kê đánh giá
        $this->updateRatingStats();
        
        return $review->id;
    }

    /**
     * Cập nhật thống kê đánh giá từ bảng product_reviews
     */
    public function updateRatingStats()
    {
        $reviews = $this->reviews()->where('status', true)->get();
        $count = $reviews->count();
        
        if ($count > 0) {
            // Tính rating trung bình
            $averageRating = $reviews->avg('rating');
            
            // Tính phân phối rating
            $distribution = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];
            foreach ($reviews as $review) {
                $rating = min(5, max(1, $review->rating));
                $distribution[$rating]++;
            }
            
            // Chuyển đổi sang phần trăm
            foreach ($distribution as $rating => $value) {
                $distribution[$rating] = round(($value / $count) * 100, 1);
            }
            
            // Cập nhật thông tin đánh giá vào sản phẩm
            $this->update([
                'reviews_count' => $count,
                'average_rating' => round($averageRating, 1),
                'rating_distribution' => json_encode($distribution)
            ]);
        } else {
            // Không có đánh giá nào
            $this->update([
                'reviews_count' => 0,
                'average_rating' => 0,
                'rating_distribution' => json_encode([5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0])
            ]);
        }
        
        return $this;
    }
} 