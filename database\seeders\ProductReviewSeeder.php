<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductReview;
use Carbon\Carbon;

class ProductReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Xóa tất cả đánh giá cũ
        ProductReview::truncate();
        
        // Lấy tất cả sản phẩm có status = true
        $products = Product::where('status', true)->get();
        
        if ($products->isEmpty()) {
            $this->command->info('Không có sản phẩm để tạo đánh giá.');
            return;
        }
        
        // Danh sách reviewer mẫu
        $reviewers = [
            ['name' => 'Nguyễn Văn A', 'email' => '<EMAIL>'],
            ['name' => 'Trần Thị B', 'email' => '<EMAIL>'],
            ['name' => '<PERSON><PERSON>', 'email' => '<EMAIL>'],
            ['name' => '<PERSON>ạm Thị D', 'email' => '<EMAIL>'],
            ['name' => 'Hoàng Văn E', 'email' => '<EMAIL>'],
        ];
        
        // Danh sách comment mẫu
        $comments = [
            'Sản phẩm rất tốt, đúng như mô tả. Tôi đã sử dụng được 2 tuần và thấy hiệu quả rõ rệt.',
            'Sản phẩm chất lượng tốt, giao hàng nhanh. Nhưng giá hơi cao so với thị trường.',
            'Tuyệt vời! Sẽ mua lại lần sau. Thương hiệu auvista luôn là sự lựa chọn hàng đầu của tôi.',
            'Sản phẩm đúng như mong đợi, rất hài lòng với chất lượng.',
            'Mẫu mã đẹp, chất lượng tốt, giá cả hợp lý. Sẽ giới thiệu cho bạn bè.',
            'Hiệu quả sau 1 tuần sử dụng, đúng như quảng cáo. Rất hài lòng!',
            'Đóng gói cẩn thận, giao hàng nhanh chóng. Sản phẩm chính hãng, yên tâm sử dụng.',
            'Chất lượng sản phẩm ổn, nhưng mùi hương không hợp với tôi lắm.',
            'Giá hơi cao nhưng xứng đáng với chất lượng. Đã mua lần thứ 3 rồi.',
            'Thành phần lành tính, phù hợp với da nhạy cảm. Sẽ tiếp tục sử dụng.',
        ];
        
        // Phản hồi admin mẫu
        $adminReplies = [
            'Cảm ơn bạn đã đánh giá sản phẩm. Chúng tôi rất vui khi sản phẩm đáp ứng được nhu cầu của bạn.',
            'Cảm ơn phản hồi của bạn. auvista luôn nỗ lực mang đến những sản phẩm chất lượng cao nhất.',
            'Xin cảm ơn đánh giá tích cực của bạn. Rất mong được phục vụ bạn trong những lần mua sắm tiếp theo.',
            'Chúng tôi rất tiếc về trải nghiệm không như mong đợi. Vui lòng liên hệ với chúng tôi để được hỗ trợ tốt hơn.',
            'Cảm ơn bạn đã chia sẻ trải nghiệm. Chúng tôi sẽ cải thiện sản phẩm dựa trên góp ý của bạn.',
        ];
        
        $totalReviews = 0;
        $this->command->info('Bắt đầu tạo đánh giá mẫu...');
        
        // Tạo đánh giá cho mỗi sản phẩm
        foreach ($products as $product) {
            // Số lượng đánh giá ngẫu nhiên từ 3-8 cho mỗi sản phẩm
            $reviewCount = rand(3, 8);
            
            for ($i = 0; $i < $reviewCount; $i++) {
                // Tạo đánh giá với trọng số khác nhau: 70% là 4-5*, 20% là 3*, 10% là 1-2*
                $weights = [1 => 5, 2 => 5, 3 => 20, 4 => 35, 5 => 35];
                $rating = $this->getWeightedRandom($weights);
                
                $reviewer = $reviewers[array_rand($reviewers)];
                $comment = $comments[array_rand($comments)];
                
                // Ngày tạo trong khoảng 1-30 ngày trước
                $createdAt = Carbon::now()->subDays(rand(1, 30));
                
                // 70% đánh giá có phản hồi từ admin
                $hasAdminReply = rand(1, 100) <= 70;
                $adminReply = $hasAdminReply ? $adminReplies[array_rand($adminReplies)] : null;
                $adminReplyAt = $hasAdminReply ? $createdAt->copy()->addDays(rand(1, 3)) : null;
                
                // Tạo đánh giá
                ProductReview::create([
                    'product_id' => $product->id,
                    'reviewer_name' => $reviewer['name'],
                    'reviewer_email' => $reviewer['email'],
                    'rating' => $rating,
                    'comment' => $comment,
                    'recommend' => rand(0, 100) < 70, // 70% khả năng đề xuất
                    'verified_purchase' => rand(0, 100) < 80, // 80% là khách hàng đã mua
                    'helpful_count' => rand(0, 10),
                    'status' => true,
                    'admin_reply' => $adminReply,
                    'admin_reply_at' => $adminReplyAt,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt,
                ]);
                
                $totalReviews++;
            }
            
            // Cập nhật thống kê đánh giá cho sản phẩm
            $product->updateRatingStats();
        }
        
        $this->command->info("Đã tạo thành công $totalReviews đánh giá cho " . $products->count() . " sản phẩm.");
    }
    
    /**
     * Lấy giá trị ngẫu nhiên dựa trên trọng số
     */
    private function getWeightedRandom(array $weights)
    {
        $totalWeight = array_sum($weights);
        $randomNumber = rand(1, $totalWeight);
        $runningSum = 0;
        
        foreach ($weights as $value => $weight) {
            $runningSum += $weight;
            if ($randomNumber <= $runningSum) {
                return $value;
            }
        }
        
        return array_key_first($weights);
    }
}
