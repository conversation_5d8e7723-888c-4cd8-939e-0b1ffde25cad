<?php

namespace App\Filament\Resources\OrderResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;

class OrderDetailsRelationManager extends RelationManager
{
    protected static string $relationship = 'orderDetails';

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                TextColumn::make('product_name')->label('Sản phẩm'),
                TextColumn::make('quantity')->label('Số lượng'),
                TextColumn::make('price')->label('Đơn giá')->money('VND'),
                TextColumn::make('subtotal')->label('Thành tiền')->money('VND'),
            ]);
    }
} 