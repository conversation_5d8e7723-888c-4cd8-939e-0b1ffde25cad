<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Cookie;

class WishlistManager extends Component
{
    public $productId;
    public $inWishlist = false;
    public $count = 0;
    public $showButton = true;

    public function mount($productId = null)
    {
        $this->productId = $productId;
        $this->updateCount();
        $this->checkIfInWishlist();
        
        // Nếu không có product ID, chỉ hiển thị số lượng
        if (!$this->productId) {
            $this->showButton = false;
        }
    }

    public function updateCount()
    {
        $wishlistItems = $this->getWishlistItems();
        $this->count = count($wishlistItems);
    }

    public function checkIfInWishlist()
    {
        if (!$this->productId) {
            return false;
        }
        
        $wishlistItems = $this->getWishlistItems();
        $this->inWishlist = in_array($this->productId, $wishlistItems);
        
        return $this->inWishlist;
    }

    public function toggleWishlist()
    {
        $wishlistItems = $this->getWishlistItems();
        
        if ($this->inWishlist) {
            // Xóa khỏi wishlist
            $wishlistItems = array_diff($wishlistItems, [$this->productId]);
            $this->inWishlist = false;
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Đã xóa sản phẩm khỏi danh sách yêu thích'
            ]);
        } else {
            // Thêm vào wishlist
            $wishlistItems[] = $this->productId;
            $this->inWishlist = true;
            $this->dispatch('showNotification', [
                'type' => 'success',
                'message' => 'Đã thêm sản phẩm vào danh sách yêu thích'
            ]);
        }
        
        $this->saveWishlistItems($wishlistItems);
        $this->updateCount();
        
        // Gửi sự kiện cập nhật wishlist cho các component khác
        $this->dispatch('wishlistUpdated', ['count' => $this->count]);
    }
    
    protected function getWishlistItems()
    {
        $wishlistCookie = Cookie::get('wishlist');
        if (!$wishlistCookie) {
            return [];
        }
        
        return json_decode($wishlistCookie, true) ?: [];
    }
    
    protected function saveWishlistItems($items)
    {
        Cookie::queue('wishlist', json_encode($items), 60 * 24 * 30); // 30 ngày
    }
    
    public function render()
    {
        if ($this->showButton) {
            return view('livewire.wishlist-button', [
                'inWishlist' => $this->inWishlist,
                'productId' => $this->productId
            ]);
        } else {
            return view('livewire.wishlist-count', [
                'count' => $this->count
            ]);
        }
    }
}
