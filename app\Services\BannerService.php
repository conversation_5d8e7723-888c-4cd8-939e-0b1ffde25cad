<?php

namespace App\Services;

use App\Models\Banner;
use Illuminate\Support\Collection;

class BannerService
{
    /**
     * Lấy tất cả banner theo loại đang active
     *
     * @param string $type
     * @param string|null $position
     * @return Collection
     */
    public function getBannersByType(string $type, ?string $position = null): Collection
    {
        $query = Banner::ofType($type)
            ->active()
            ->ordered();
        
        if ($position) {
            $query->ofPosition($position);
        }
        
        return $query->with('media')->get();
    }
    
    /**
     * Lấy tất cả banner theo vị trí
     *
     * @param string $position
     * @return Collection
     */
    public function getBannersByPosition(string $position): Collection
    {
        return Banner::ofPosition($position)
            ->active()
            ->ordered()
            ->with('media')
            ->get();
    }
    
    /**
     * Lấy tất cả banner
     *
     * @param bool $activeOnly
     * @return Collection
     */
    public function getAllBanners(bool $activeOnly = true): Collection
    {
        $query = Banner::query()->ordered();
        
        if ($activeOnly) {
            $query->active();
        }
        
        return $query->with('media')->get();
    }
    
    /**
     * Lấy tất cả banner theo type và position
     *
     * @param array $types
     * @param string|null $position
     * @return Collection
     */
    public function getBannersByTypes(array $types, ?string $position = null): Collection
    {
        $query = Banner::whereIn('type', $types)
            ->active()
            ->ordered();
        
        if ($position) {
            $query->ofPosition($position);
        }
        
        return $query->with('media')->get();
    }
    
    /**
     * Lấy banner theo slug
     *
     * @param string $slug
     * @return Banner|null
     */
    public function getBannerBySlug(string $slug): ?Banner
    {
        return Banner::where('slug', $slug)
            ->active()
            ->with('media')
            ->first();
    }
} 