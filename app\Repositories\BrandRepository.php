<?php

namespace App\Repositories;

use App\Models\Brand;

class BrandRepository
{
    protected $model;

    public function __construct(Brand $model)
    {
        $this->model = $model;
    }

    /**
     * L<PERSON>y các thương hi<PERSON>u n<PERSON>i bật
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFeaturedBrands($limit = 3)
    {
        return $this->model->where('status', true)
            ->withCount('products')
            ->orderBy('products_count', 'desc')
            ->orderBy('name', 'asc')
            ->take($limit)
            ->get();
    }

    /**
     * Lấy tất cả brands
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllBrands()
    {
        return $this->model->where('status', true)
            ->orderBy('name', 'asc')
            ->get();
    }
} 