@extends('templates.auvista.layouts.default')

@section('title', ($seoData->seo_title ?? __('messages.product_categories')) . ' - Auvista')
@section('meta_description', $seoData->seo_description ?? __('messages.product_categories_description'))

@section('content')
    <!-- Main Content -->
    <main id="main" id="site-main">
        <div class="page-wrapper blog-archive mb-7 lg:mb-[54px] mt-12 lg:mt-[70px]">
            <div class="container mx-auto">
                <div class="wrapper-container pb-8 border-b border-primary-border">
                    <h1 class="mb-4 text-2xl lg:text-[38px]/[1.4] font-bold text-primary-base">
                        {{ __('messages.product_categories') }}
                    </h1>
                    <div class="categories-swiper swiper-slider relative -ml-2 -mr-2" data-items="5" data-mobile="2"
                        data-tablet="3" data-desktop="4" data-large="5" data-xlarge="6" data-spacing="0" data-loop="false"
                        data-navigation="true">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <!-- "Tất cả" option -->
                                <div class="swiper-slide px-2">
                                    <div class="">
                                        <a href="{{ route('products.index') }}"
                                            class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                            <img src="{{ asset('images/img-cat-1.png') }}" alt="Tất cả danh mục"
                                                class="w-full h-full object-cover hover:scale-110 duration-500" />
                                        </a>
                                        <div class="pt-3">
                                            <a href="{{ route('products.index') }}"
                                                class="text-primary-gray2 font-medium hover:text-secondary-main transition-colors {{ empty($selectedCategories) ? 'text-secondary-main font-bold' : '' }}">
                                                Tất cả danh mục
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <!-- Debug: {{ isset($categories) ? 'Categories isset: ' . $categories->count() : 'Categories not set' }} -->
                                @if (isset($categories) && $categories->count() > 0)
                                    @foreach ($categories as $category)
                                        <div class="swiper-slide px-2">
                                            <div class="">
                                                <a href="{{ route('products.index', ['category' => [$category->id]]) }}"
                                                    class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                    <img src="{{ getImageUrl($category->thumbnail, 'images/img-cat-' . (($loop->index % 6) + 1) . '.png') }}"
                                                        alt="{{ $category->name }}"
                                                        class="w-full h-full object-cover hover:scale-110 duration-500" />
                                                </a>
                                                <div class="pt-3">
                                                    <a href="{{ route('products.index', ['category' => [$category->id]]) }}"
                                                        class="text-primary-gray2 font-medium hover:text-secondary-main transition-colors {{ in_array($category->id, $selectedCategories ?? []) ? 'text-secondary-main font-bold' : '' }}
                                               {{ $category->parent_id > 0 ? 'text-sm pl-2' : '' }}">
                                                        {{ $category->parent_id > 0 ? '→ ' : '' }}{{ $category->name }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <!-- Fallback static content -->
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-1.png') }}" alt="Hội nghị truyền hình"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Hội nghị truyền hình
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-2.png') }}" alt="Phòng học thông minh"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Phòng học thông minh
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-3.png') }}" alt="Âm thanh hội thảo"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Âm thanh hội thảo
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-4.png') }}"
                                                    alt="Âm thanh nhạc nền, đa vùng"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Âm thanh nhạc nền, đa vùng
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-5.png') }}"
                                                    alt="Âm thanh, ánh sáng biểu diễn"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Âm thanh, ánh sáng biểu diễn
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                                class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-6.png') }}" alt="Âm thanh thông báo"
                                                    class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                    class="text-primary-gray2 font-medium">
                                                    Âm thanh thông báo
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!-- Navigation buttons -->
                        <div class="swiper-button-next shadow-new-shadow bg-primary-background3 top-[35%] xl:-right-4">
                        </div>
                        <div class="swiper-button-prev shadow-new-shadow bg-primary-background3 top-[35%] xl:-left-4"></div>
                    </div>
                </div>
            </div>

            <div class="container mx-auto mt-8 mb-8">
                <div class="flex items-start justify-between flex-col lg:flex-row gap-[30px]">
                    <!-- Sidebar Filter -->
                    <div
                        class="w-80 lg:w-full lg:max-w-[21.2765957%] order-1 hidden lg:block filter-sidebar fixed lg:relative top-0 left-0 h-full lg:h-auto bg-white z-50 lg:z-auto transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out overflow-y-auto p-6 lg:p-0">
                        <form method="GET" action="{{ route('products.index') }}" id="filterForm">
                            <!-- Mobile Header with Close Button -->
                            <div class="flex justify-between items-center mb-6 lg:hidden">
                                <h3 class="text-xl font-bold text-primary-base">Bộ lọc</h3>
                                <button class="close-filter-sidebar text-primary-gray2 hover:text-primary-base">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </button>
                            </div>

                            <!-- Categories Filter -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h4 class="text-lg font-bold">{{ __('messages.product_type') }}</h4>
                                    <button
                                        class="button-toggle text-secondary-main w-8 h-8 flex justify-center items-center">
                                        <svg width="20" height="21" viewBox="0 0 20 21" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M17.2253 13.075C17.2253 13.2334 17.1669 13.3917 17.0419 13.5167C16.8003 13.7584 16.4003 13.7584 16.1586 13.5167L10.7253 8.08335C10.3253 7.68335 9.67526 7.68335 9.27526 8.08335L3.84193 13.5167C3.60026 13.7584 3.20026 13.7584 2.95859 13.5167C2.71693 13.275 2.71693 12.875 2.95859 12.6334L8.39193 7.20002C8.81693 6.77502 9.39193 6.53335 10.0003 6.53335C10.6086 6.53335 11.1836 6.76669 11.6086 7.20002L17.0419 12.6334C17.1586 12.7584 17.2253 12.9167 17.2253 13.075Z"
                                                fill="#246DDA" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="list-items-checkbox">
                                    <div class="flex flex-col gap-3">
                                        @if (isset($categories) && $categories->count() > 0)
                                            @foreach ($categories as $category)
                                                <label
                                                    class="flex items-center gap-3 cursor-pointer select-none {{ $category->parent_id > 0 ? 'ml-4' : '' }}">
                                                    <input type="checkbox" class="hidden peer" name="category[]"
                                                        value="{{ $category->id }}"
                                                        {{ in_array($category->id, $selectedCategories ?? []) ? 'checked' : '' }}>
                                                    <span
                                                        class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-md flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                        <span
                                                            class="hidden peer-checked:block w-4 h-4 rounded bg-white"></span>
                                                    </span>
                                                    <span
                                                        class="text-primary-gray2 peer-checked:text-secondary-main font-medium {{ $category->parent_id > 0 ? 'text-sm' : '' }}">
                                                        {{ $category->parent_id > 0 ? '→ ' : '' }}{{ $category->name }}
                                                    </span>
                                                </label>
                                            @endforeach
                                        @else
                                            <label class="flex items-center gap-3 cursor-pointer select-none">
                                                <input type="checkbox" class="hidden peer" name="loa" value="loa"
                                                    checked>
                                                <span
                                                    class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-md flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                    <span
                                                        class="hidden peer-checked:block w-4 h-4 rounded bg-white"></span>
                                                </span>
                                                <span
                                                    class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Loa</span>
                                            </label>
                                            <label class="flex items-center gap-3 cursor-pointer select-none">
                                                <input type="checkbox" class="hidden peer" name="manhinh"
                                                    value="manhinh">
                                                <span
                                                    class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-md flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                    <span
                                                        class="hidden peer-checked:block w-4 h-4 rounded bg-white"></span>
                                                </span>
                                                <span
                                                    class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Màn
                                                    hình</span>
                                            </label>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Brands Filter -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h4 class="text-lg font-bold">{{ __('messages.brands') }}</h4>
                                    <button
                                        class="button-toggle text-secondary-main w-8 h-8 flex justify-center items-center">
                                        <svg width="20" height="21" viewBox="0 0 20 21" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M17.2253 13.075C17.2253 13.2334 17.1669 13.3917 17.0419 13.5167C16.8003 13.7584 16.4003 13.7584 16.1586 13.5167L10.7253 8.08335C10.3253 7.68335 9.67526 7.68335 9.27526 8.08335L3.84193 13.5167C3.60026 13.7584 3.20026 13.7584 2.95859 13.5167C2.71693 13.275 2.71693 12.875 2.95859 12.6334L8.39193 7.20002C8.81693 6.77502 9.39193 6.53335 10.0003 6.53335C10.6086 6.53335 11.1836 6.76669 11.6086 7.20002L17.0419 12.6334C17.1586 12.7584 17.2253 12.9167 17.2253 13.075Z"
                                                fill="#246DDA" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="list-items-checkbox">
                                    <div class="flex flex-col gap-3">
                                        @if (isset($brands) && $brands->count() > 0)
                                            @foreach ($brands as $brand)
                                                <label class="flex items-center gap-3 cursor-pointer select-none">
                                                    <input type="checkbox" class="hidden peer" name="brand[]"
                                                        value="{{ $brand->id }}"
                                                        {{ in_array($brand->id, $selectedBrands ?? []) ? 'checked' : '' }}>
                                                    <span
                                                        class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-md flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                        <span
                                                            class="hidden peer-checked:block w-4 h-4 rounded bg-white"></span>
                                                    </span>
                                                    <span
                                                        class="text-primary-gray2 peer-checked:text-secondary-main font-medium">{{ $brand->name }}</span>
                                                </label>
                                            @endforeach
                                        @else
                                            <label class="flex items-center gap-3 cursor-pointer select-none">
                                                <input type="checkbox" class="hidden peer" name="amx" value="AMX"
                                                    checked>
                                                <span
                                                    class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-md flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                    <span
                                                        class="hidden peer-checked:block w-4 h-4 rounded bg-white"></span>
                                                </span>
                                                <span
                                                    class="text-primary-gray2 peer-checked:text-secondary-main font-medium">AMX</span>
                                            </label>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <!-- Price Range Filter -->
                            <div class="">
                                <div class="flex justify-between items-center mb-4">
                                    <h4 class="text-lg font-bold">Khoảng giá</h4>
                                    <button
                                        class="button-toggle text-secondary-main w-8 h-8 flex justify-center items-center">
                                        <svg width="20" height="21" viewBox="0 0 20 21" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M17.2253 13.075C17.2253 13.2334 17.1669 13.3917 17.0419 13.5167C16.8003 13.7584 16.4003 13.7584 16.1586 13.5167L10.7253 8.08335C10.3253 7.68335 9.67526 7.68335 9.27526 8.08335L3.84193 13.5167C3.60026 13.7584 3.20026 13.7584 2.95859 13.5167C2.71693 13.275 2.71693 12.875 2.95859 12.6334L8.39193 7.20002C8.81693 6.77502 9.39193 6.53335 10.0003 6.53335C10.6086 6.53335 11.1836 6.76669 11.6086 7.20002L17.0419 12.6334C17.1586 12.7584 17.2253 12.9167 17.2253 13.075Z"
                                                fill="#246DDA" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="list-items-price">
                                    <div class="flex flex-col gap-3">
                                        <label class="flex items-center gap-3 cursor-pointer select-none">
                                            <input type="radio" name="price" value="20000000+" class="hidden peer"
                                                {{ ($selectedPrice ?? '') == '20000000+' ? 'checked' : '' }}>
                                            <span
                                                class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-full flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                <span
                                                    class="hidden peer-checked:block w-3 h-3 rounded-full bg-white"></span>
                                            </span>
                                            <span
                                                class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Trên
                                                20,000,000đ</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer select-none">
                                            <input type="radio" name="price" value="10000000-20000000"
                                                class="hidden peer"
                                                {{ ($selectedPrice ?? '') == '10000000-20000000' ? 'checked' : '' }}>
                                            <span
                                                class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-full flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                <span
                                                    class="hidden peer-checked:block w-3 h-3 rounded-full bg-white"></span>
                                            </span>
                                            <span
                                                class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Từ
                                                10,000,000đ đến 20,000,000đ</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer select-none">
                                            <input type="radio" name="price" value="5000000+" class="hidden peer"
                                                {{ ($selectedPrice ?? '') == '5000000+' ? 'checked' : '' }}>
                                            <span
                                                class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-full flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                <span
                                                    class="hidden peer-checked:block w-3 h-3 rounded-full bg-white"></span>
                                            </span>
                                            <span
                                                class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Trên
                                                5,000,000đ</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer select-none">
                                            <input type="radio" name="price" value="2000000-5000000"
                                                class="hidden peer"
                                                {{ ($selectedPrice ?? '') == '2000000-5000000' ? 'checked' : '' }}>
                                            <span
                                                class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-full flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                <span
                                                    class="hidden peer-checked:block w-3 h-3 rounded-full bg-white"></span>
                                            </span>
                                            <span
                                                class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Từ
                                                2,000,000đ đến 5,000,000đ</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer select-none">
                                            <input type="radio" name="price" value="0-2000000" class="hidden peer"
                                                {{ ($selectedPrice ?? '') == '0-2000000' ? 'checked' : '' }}>
                                            <span
                                                class="w-5 h-5 border border-primary-border2 bg-primary-background3 rounded-full flex items-center justify-center transition-all shadow-checkbox peer-checked:bg-secondary-main peer-checked:border-secondary-main">
                                                <span
                                                    class="hidden peer-checked:block w-3 h-3 rounded-full bg-white"></span>
                                            </span>
                                            <span
                                                class="text-primary-gray2 peer-checked:text-secondary-main font-medium">Dưới
                                                2,000,000đ</span>
                                        </label>
                                    </div>
                                    <div class="range-price">
                                        <div class="flex gap-2 mt-4">
                                            <div class="flex-1 relative">
                                                <input type="text" placeholder="Tối thiểu"
                                                    class="w-full px-3 h-9 pr-6 border border-primary-border rounded-md font-medium focus:outline-none focus:border-secondary-main bg-white" />
                                                <span
                                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-primary-gray2">đ</span>
                                            </div>
                                            <div class="flex-1 relative">
                                                <input type="text" placeholder="Tối đa"
                                                    class="w-full px-3 h-9 pr-6 border border-primary-border rounded-md font-medium focus:outline-none focus:border-secondary-main bg-white" />
                                                <span
                                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 font-medium text-primary-gray2">đ</span>
                                            </div>
                                        </div>
                                        <button type="submit"
                                            class="bg-gradient2 mt-4 w-full h-9 rounded-full text-white font-medium hover:bg-gradient2-hover transition-colors">
                                            {{ __('messages.search') }}
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Filter Submit Button -->
                            <div class="mt-6 pt-6 border-t border-primary-border">
                                <div class="flex gap-3">
                                    <button type="submit"
                                        class="flex-1 bg-gradient2 h-11 rounded-full text-white font-medium hover:bg-gradient2-hover transition-colors">
                                        {{ __('messages.filter_products') }}
                                    </button>
                                    <a href="{{ route('products.index') }}"
                                        class="flex-1 bg-transparent border border-primary-border h-11 rounded-full text-primary-base font-medium hover:bg-gray-50 transition-colors flex items-center justify-center">
                                        Xóa lọc
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>

                    <div class="w-full order-2">
                        <div class="flex justify-between items-center mb-6 lg:mb-8">
                            <span class="filter-button font-medium flex cursor-pointer lg:hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    stroke-width="1.5" stroke="currentColor" class="size-6">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" />
                                </svg>
                                Lọc sản phẩm
                            </span>
                            <span class="font-medium hidden md:flex">
                                {{ isset($products) ? $products->total() : '181' }} {{ __('messages.products') }}
                            </span>
                            <div class="flex items-center gap-4">
                                <span class="text-primary-gray2">Sắp xếp theo</span>
                                <select
                                    class="bg-white border border-primary-border rounded-full text-primary-gray2 focus:outline-none focus:ring-0 focus:border-secondary-main">
                                    <option value="default">Mặc định</option>
                                    <option value="popularity">Thứ tự mức độ phổ biến</option>
                                    <option value="best-seller">Bán chạy nhất</option>
                                    <option value="rating">Điểm đánh giá</option>
                                    <option value="price-asc">Giá thấp đến cao</option>
                                    <option value="price-desc">Giá cao đến thấp</option>
                                </select>
                            </div>
                        </div>

                        <div
                            class="products list-products grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-5 xl:gap-[30px]">
                            @if (isset($products) && $products->count() > 0)
                                @foreach ($products as $product)
                                    @include('templates.auvista.components.product-card', ['product' => $product])
                                @endforeach
                            @else
                                <!-- Fallback nếu không có sản phẩm -->
                                <div class="col-span-full text-center py-12">
                                    <p class="text-gray-500 text-lg">{{ __('messages.no_products_found') }}</p>
                                </div>
                            @endif
                        </div>

                        <!-- Pagination -->
                        @if (isset($products) && $products->hasPages())
                            <div class="mt-8 flex justify-center">
                                {{ $products->links() }}
                            </div>
                        @else
                            <div class="text-center mt-8">
                                <button
                                    class="rounded-full text-white bg-gradient2 transition-all px-11 py-1 h-11 font-medium hover:bg-gradient5 hover:shadow-xl hover:text-primary-base">
                                    Xem thêm
                                </button>
                            </div>
                        @endif
                    </div>

                    <!-- Mobile Filter Overlay -->
                    <div class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden mobile-filter-overlay lg:hidden"></div>
                </div>
            </div>
        </div>

        <!-- Term Description Section -->
        <div class="bg-primary-grey pt-12 pb-12">
            <div class="container mx-auto">
                <div class="term-description">
                    <h4 class="text-2xl/[1.4] mb-4 lg:mb-8">
                        {{ __('messages.product_categories_description') }}
                    </h4>
                    <p class="text-lg/[1.4] mb-4">
                        <strong>1. Hệ thống âm thanh hội nghị - Nâng tầm trải nghiệm giao tiếp</strong>
                    </p>
                    <p class="text-base/[1.4] mb-4">
                        {{ __('messages.product_quality_description') }}
                    </p>
                    <p class="text-base/[1.4] mb-4">
                        {{ __('messages.explore_products_description') }}
                    </p>
                </div>
                <div class="mt-8">
                    <button id="toggleButton"
                        class="border rounded-full border-secondary-main text-secondary-main font-medium px-11 py-[9px] hover:bg-secondary-main hover:text-white transition-colors">
                        Xem thêm
                    </button>
                </div>
            </div>
        </div>

        <!-- Related News Section -->
        <div class="container mx-auto related-slider mt-8">
            <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-6">
                {{ __('messages.useful_news') }}
            </h2>
            <div class="swiper-slider" data-items="1.3" data-mobile="2" data-tablet="3" data-desktop="3"
                data-large="3" data-xlarge="3" data-spacing="0" data-loop="false" data-navigation="true">
                <div class="swiper -ml-[15px] -mr-[15px]">
                    <div class="swiper-wrapper">
                        @if (isset($relatedPosts) && $relatedPosts->count() > 0)
                            @foreach ($relatedPosts as $post)
                                <div class="swiper-slide p-[15px]">
                                    <div class="rounded-xl overflow-hidden shadow-post">
                                        <a href="{{ route('news.detail', $post->slug) }}"
                                            class="block overflow-hidden aspect-[1.94488189]">
                                            <img src="{{ getImageUrl($post->image, 400, 200) }}"
                                                alt="{{ $post->title }}"
                                                class="w-full h-full object-cover hover:scale-110 duration-500" />
                                        </a>
                                        <div class="py-8 px-6">
                                            <div class="flex items-center space-x-4 text-gray-500 mb-2">
                                                <span
                                                    class="bg-primary-background2 text-primary-price rounded-sm px-2 py-1">
                                                    {{ $post->categories->first() ? $post->categories->first()->name : __('messages.news') }}
                                                </span>
                                                <span class="separator w-[1px] h-6 bg-primary-border"></span>
                                                <div class="flex items-center space-x-1">
                                                    <svg width="24" height="25" viewBox="0 0 24 25"
                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                                            fill="#535563" />
                                                    </svg>
                                                    <span>{{ $post->read_time ?? '6' }} min read</span>
                                                </div>
                                            </div>
                                            <h3 class="text-lg font-medium mb-2">
                                                <a href="{{ route('news.detail', $post->slug) }}"
                                                    class="d-block text-primary-base line-clamp-2 whitespace-normal hover:text-primary-main">
                                                    {{ $post->title }}
                                                </a>
                                            </h3>
                                            <div class="line-clamp-1 text-primary-gray2">
                                                {{ Str::limit(strip_tags($post->excerpt ?? $post->content), 120) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <!-- Fallback static content -->
                            <div class="swiper-slide p-[15px]">
                                <div class="rounded-xl overflow-hidden shadow-post">
                                    <a href="#" class="block overflow-hidden aspect-[1.94488189]">
                                        <img src="{{ asset('images/img-post-2.jpg') }}"
                                            alt="{{ __('messages.audio_news') }}"
                                            class="w-full h-full object-cover hover:scale-110 duration-500" />
                                    </a>
                                    <div class="py-8 px-6">
                                        <div class="flex items-center space-x-4 text-gray-500 mb-2">
                                            <span class="bg-primary-background2 text-primary-price rounded-sm px-2 py-1">
                                                {{ __('messages.news') }}
                                            </span>
                                            <span class="separator w-[1px] h-6 bg-primary-border"></span>
                                            <div class="flex items-center space-x-1">
                                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                                        fill="#535563" />
                                                </svg>
                                                <span>6 min read</span>
                                            </div>
                                        </div>
                                        <h3 class="text-lg font-medium mb-2">
                                            <a href="#"
                                                class="d-block text-primary-base line-clamp-2 whitespace-normal hover:text-primary-main">
                                                {{ __('messages.audio_technology_trends') }}
                                            </a>
                                        </h3>
                                        <div class="line-clamp-1 text-primary-gray2">
                                            {{ __('messages.audio_technology_trends_description') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide p-[15px]">
                                <div class="rounded-xl overflow-hidden shadow-post">
                                    <a href="#" class="block overflow-hidden aspect-[1.94488189]">
                                        <img src="{{ asset('images/img-post-2.jpg') }}"
                                            alt="{{ __('messages.online_conference') }}"
                                            class="w-full h-full object-cover hover:scale-110 duration-500" />
                                    </a>
                                    <div class="py-8 px-6">
                                        <div class="flex items-center space-x-4 text-gray-500 mb-2">
                                            <span
                                                class="bg-primary-background2 text-primary-price rounded-sm px-2 py-1">Hướng
                                                dẫn</span>
                                            <span class="separator w-[1px] h-6 bg-primary-border"></span>
                                            <div class="flex items-center space-x-1">
                                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                                        fill="#535563" />
                                                </svg>
                                                <span>8 min read</span>
                                            </div>
                                        </div>
                                        <h3 class="text-lg font-medium mb-2">
                                            <a href="#"
                                                class="d-block text-primary-base line-clamp-2 whitespace-normal hover:text-primary-main">
                                                Hướng dẫn thiết lập hệ thống âm thanh hội nghị chuyên nghiệp
                                            </a>
                                        </h3>
                                        <div class="line-clamp-1 text-primary-gray2">
                                            {{ __('messages.conference_setup_description') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                    <!-- Navigation buttons -->
                    <div class="swiper-button-next shadow-new-shadow bg-primary-background3"></div>
                    <div class="swiper-button-prev shadow-new-shadow bg-primary-background3"></div>
                </div>
            </div>
        </div>
    </main>
@endsection

@push('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Categories page loaded');

            // Debug category links
            const categoryLinks = document.querySelectorAll('a[href*="category"]');
            console.log('Found category links:', categoryLinks.length);

            categoryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    console.log('Category link clicked:', this.href);
                });
            });

            // Auto submit form when filter changes
            const filterForm = document.getElementById('filterForm');
            if (filterForm) {
                console.log('Filter form found');
                const filterInputs = filterForm.querySelectorAll('input[type="checkbox"], input[type="radio"]');
                console.log('Filter inputs found:', filterInputs.length);

                filterInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        console.log('Filter changed:', this.name, this.value);
                        // Small delay to allow UI update
                        setTimeout(() => {
                            filterForm.submit();
                        }, 100);
                    });
                });
            } else {
                console.log('Filter form NOT found');
            }

            // Term Description Expand/Collapse functionality
            initializeTermDescription();
        });

        function initializeTermDescription() {
            const termDescription = document.querySelector(".term-description");
            const toggleButton = document.getElementById("toggleButton");

            if (!termDescription || !toggleButton) return;

            const allElements = Array.from(termDescription.children);
            let isExpanded = false;
            let truncationIndicator = null;

            // Function to show only first 3 elements
            function showTruncated() {
                allElements.forEach((element, index) => {
                    if (index < 3) {
                        element.style.display = "block";
                    } else {
                        element.style.display = "none";
                    }
                });

                // Add truncation indicator if not exists
                if (!truncationIndicator && allElements.length > 3) {
                    truncationIndicator = document.createElement("span");
                    truncationIndicator.className = "truncation-indicator";
                    truncationIndicator.textContent = "...";
                    truncationIndicator.style.cssText = `
                color: #666;
                font-weight: bold;
                background: #F8F8F8;
                padding: 0 4px;
                border-radius: 4px;
                margin-left: 8px;
            `;

                    // Add to the last visible element
                    if (allElements[2]) {
                        allElements[2].appendChild(truncationIndicator);
                    }
                }

                toggleButton.textContent = "Xem thêm";
                isExpanded = false;
            }

            // Function to show all elements
            function showExpanded() {
                allElements.forEach((element) => {
                    element.style.display = "block";
                });

                // Remove truncation indicator
                if (truncationIndicator) {
                    truncationIndicator.remove();
                    truncationIndicator = null;
                }

                toggleButton.textContent = "Thu gọn";
                isExpanded = true;
            }

            // Toggle function
            function toggleContent() {
                if (isExpanded) {
                    showTruncated();
                    // Smooth scroll to term description section
                    setTimeout(() => {
                        termDescription.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                        });
                    }, 100);
                } else {
                    showExpanded();
                }
            }

            // Add click event listener
            toggleButton.addEventListener("click", toggleContent);

            // Initialize with truncated view
            showTruncated();
        }
    </script>
@endpush
