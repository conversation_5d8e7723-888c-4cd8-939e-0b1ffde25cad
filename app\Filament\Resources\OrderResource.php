<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrderResource\Pages;
use App\Models\Order;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Group;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Str;

class OrderResource extends BaseResource
{
    protected static ?string $model = Order::class;
    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';
    protected static ?string $navigationGroup = 'Quản lý bán hàng';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationLabel = 'Đơn hàng';
    protected static ?string $recordTitleAttribute = 'order_number';
    protected static ?string $slug = 'orders';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make()
                    ->schema([
                        Section::make('Thông tin đơn hàng')
                            ->schema([
                                TextInput::make('order_number')
                                    ->label('Mã đơn hàng')
                                    ->disabled()
                                    ->required(),
                                
                                Select::make('user_id')
                                    ->label('Khách hàng')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                
                                Select::make('status')
                                    ->label('Trạng thái đơn hàng')
                                    ->options([
                                        'pending' => 'Chờ xử lý',
                                        'processing' => 'Đang xử lý',
                                        'shipped' => 'Đã giao vận chuyển',
                                        'delivered' => 'Đã giao hàng',
                                        'cancelled' => 'Đã hủy',
                                        'refunded' => 'Đã hoàn tiền',
                                    ])
                                    ->required(),
                                
                                Select::make('payment_status')
                                    ->label('Trạng thái thanh toán')
                                    ->options([
                                        'pending' => 'Chờ thanh toán',
                                        'paid' => 'Đã thanh toán',
                                        'failed' => 'Thanh toán thất bại',
                                        'refunded' => 'Đã hoàn tiền',
                                    ])
                                    ->required(),
                                    
                                Select::make('payment_method')
                                    ->label('Phương thức thanh toán')
                                    ->options([
                                        'cod' => 'Thanh toán khi nhận hàng (COD)',
                                        'bank_transfer' => 'Chuyển khoản ngân hàng',
                                        'vnpay' => 'VNPay',
                                        'momo' => 'MoMo',
                                        'zalopay' => 'ZaloPay',
                                    ])
                                    ->required(),
                                    
                                TextInput::make('transaction_id')
                                    ->label('Mã giao dịch')
                                    ->helperText('Mã giao dịch từ cổng thanh toán (nếu có)'),
                            ]),
                        
                        Section::make('Thông tin vận chuyển')
                            ->schema([
                                TextInput::make('shipping_method')
                                    ->label('Phương thức vận chuyển')
                                    ->required(),
                                
                                TextInput::make('shipping_fullname')
                                    ->label('Họ tên người nhận')
                                    ->required(),
                                
                                Textarea::make('shipping_address')
                                    ->label('Địa chỉ giao hàng')
                                    ->rows(2)
                                    ->required(),
                                
                                TextInput::make('shipping_city')
                                    ->label('Thành phố')
                                    ->required(),
                                
                                TextInput::make('shipping_state')
                                    ->label('Quận/Huyện')
                                    ->required(),
                                
                                TextInput::make('shipping_phone')
                                    ->label('Số điện thoại')
                                    ->tel()
                                    ->required(),
                                
                                TextInput::make('shipping_email')
                                    ->label('Email')
                                    ->email(),
                            ]),
                            
                        Section::make('Ghi chú')
                            ->schema([
                                Textarea::make('note')
                                    ->label('Ghi chú khách hàng')
                                    ->rows(3),
                                    
                                Textarea::make('admin_note')
                                    ->label('Ghi chú admin')
                                    ->rows(3),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),
                
                Group::make()
                    ->schema([
                        Section::make('Thông tin thanh toán')
                            ->schema([
                                TextInput::make('total_amount')
                                    ->label('Tổng tiền')
                                    ->required()
                                    ->numeric()
                                    ->prefix('VND'),
                                
                                TextInput::make('tax')
                                    ->label('Thuế')
                                    ->numeric()
                                    ->prefix('VND'),
                                
                                TextInput::make('shipping_cost')
                                    ->label('Phí vận chuyển')
                                    ->numeric()
                                    ->prefix('VND'),
                                
                                TextInput::make('discount')
                                    ->label('Giảm giá')
                                    ->numeric()
                                    ->prefix('VND'),
                                
                                TextInput::make('pv_total')
                                    ->label('Tổng PV')
                                    ->numeric(),
                                
                                TextInput::make('bv_total')
                                    ->label('Tổng BV')
                                    ->numeric(),
                            ]),
                            
                        Section::make('Thời gian')
                            ->schema([
                                DatePicker::make('created_at')
                                    ->label('Ngày tạo')
                                    ->disabled(),
                                
                                DatePicker::make('delivered_at')
                                    ->label('Ngày giao hàng'),
                                
                                DatePicker::make('cancelled_at')
                                    ->label('Ngày hủy'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('order_number')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('user.name')
                    ->label('Khách hàng')
                    ->searchable()
                    ->sortable(),
                
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => [
                        'pending' => 'Chờ xử lý',
                        'processing' => 'Đang xử lý',
                        'shipped' => 'Đã giao vận chuyển',
                        'delivered' => 'Đã giao hàng',
                        'cancelled' => 'Đã hủy',
                        'refunded' => 'Đã hoàn tiền',
                    ][$state] ?? $state)
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'processing' => 'primary',
                        'shipped' => 'info',
                        'delivered' => 'success',
                        'cancelled' => 'danger',
                        'refunded' => 'secondary',
                        default => 'secondary',
                    }),
                
                TextColumn::make('payment_status')
                    ->label('Thanh toán')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => [
                        'pending' => 'Chờ thanh toán',
                        'paid' => 'Đã thanh toán',
                        'failed' => 'Thất bại',
                        'refunded' => 'Hoàn tiền',
                    ][$state] ?? $state)
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'paid' => 'success',
                        'failed' => 'danger',
                        'refunded' => 'secondary',
                        default => 'secondary',
                    }),
                
                TextColumn::make('total_amount')
                    ->label('Tổng tiền')
                    ->money('VND')
                    ->sortable(),
                
                TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Trạng thái đơn hàng')
                    ->options([
                        'pending' => 'Chờ xử lý',
                        'processing' => 'Đang xử lý',
                        'shipped' => 'Đã giao vận chuyển',
                        'delivered' => 'Đã giao hàng',
                        'cancelled' => 'Đã hủy',
                        'refunded' => 'Đã hoàn tiền',
                    ]),
                
                SelectFilter::make('payment_status')
                    ->label('Trạng thái thanh toán')
                    ->options([
                        'pending' => 'Chờ thanh toán',
                        'paid' => 'Đã thanh toán',
                        'failed' => 'Thanh toán thất bại',
                        'refunded' => 'Đã hoàn tiền',
                    ]),
                
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Từ ngày'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Đến ngày'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            \App\Filament\Resources\OrderResource\RelationManagers\OrderDetailsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }
} 