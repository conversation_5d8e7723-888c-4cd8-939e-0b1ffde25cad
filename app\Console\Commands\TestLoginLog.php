<?php

namespace App\Console\Commands;

use App\Services\LoginLogService;
use App\Models\User;
use Illuminate\Console\Command;

class TestLoginLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:login-log {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test login logging functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? '<EMAIL>';
        $loginLogService = app(LoginLogService::class);

        $this->info('Testing Login Log Service...');
        $this->newLine();

        // Test successful login
        $this->info('1. Testing successful login...');
        $user = User::where('email', $email)->first();
        
        if ($user) {
            $loginLogService->logSuccessfulLogin($user);
            $this->info("✓ Logged successful login for user: {$user->name} ({$user->email})");
        } else {
            $this->warn("User with email {$email} not found");
        }

        $this->newLine();

        // Test failed login
        $this->info('2. Testing failed login...');
        $loginLogService->logFailedLogin($email, 'invalid_credentials');
        $this->info("✓ Logged failed login attempt for email: {$email}");

        $this->newLine();

        // Test logout
        if ($user) {
            $this->info('3. Testing logout...');
            $loginLogService->logLogout($user);
            $this->info("✓ Logged logout for user: {$user->name} ({$user->email})");
        }

        $this->newLine();
        $this->info('Login log testing completed!');
        $this->info('Check the LogAction table in your database to see the results.');
    }
} 