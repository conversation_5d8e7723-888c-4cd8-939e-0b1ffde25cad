<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PageTranslation extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'code',
        'page_id',
        'lang',
    ];

    /**
     * Get the related page based on type
     */
    public function page()
    {
        switch ($this->type) {
            case 'static_page':
                return $this->belongsTo(StaticPage::class, 'page_id');
            case 'post':
                return $this->belongsTo(Post::class, 'page_id');
            case 'category':
                return $this->belongsTo(Category::class, 'page_id');
            default:
                return null;
        }
    }

    /**
     * Get all translations for the same content
     */
    public function translations()
    {
        return static::where('type', $this->type)
            ->where('code', $this->code)
            ->get();
    }

    /**
     * Get translation for specific language
     */
    public function getTranslation($lang)
    {
        return static::where('type', $this->type)
            ->where('code', $this->code)
            ->where('lang', $lang)
            ->first();
    }

    /**
     * Get the URL for this translation
     */
    public function getUrlAttribute()
    {
        if (!$this->page) {
            return null;
        }

        switch ($this->type) {
            case 'static_page':
                return route('link.detail', $this->page->slug);
            case 'post':
                return route('link.detail', $this->page->slug);
            case 'category':
                return route('link.detail', $this->page->slug);
            default:
                return null;
        }
    }
}
