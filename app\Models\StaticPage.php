<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class StaticPage extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'image',
        'status',
        'lang',
        'published_at',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'author_id',
    ];

    protected $casts = [
        'published_at' => 'datetime',
    ];

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title);
            }

            if (empty($page->author_id)) {
                $page->author_id = 1;
            }

            if (empty($page->lang)) {
                $page->lang = 'vi';
            }
        });
    }

    /**
     * Lưu hoặc cập nhật trang tĩnh dựa trên slug
     *
     * @param array $data Dữ liệu trang tĩnh
     * @return StaticPage
     */
    public static function saveOrUpdate(array $data)
    {
        $page = static::where('slug', $data['slug'])->first();
        
        if (!$page) {
            return static::create($data);
        }
        
        $page->update($data);
        return $page;
    }
}
