<?php

namespace App\Filament\Resources\MediaResource\Pages;

use App\Filament\Resources\MediaResource;
use App\Services\MediaService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditMedia extends EditRecord
{
    protected static string $resource = MediaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function ($record) {
                    // Log trước khi xóa
                    Log::info('Đang xóa media', ['id' => $record->id, 'path' => $record->path]);
                    
                    // Sử dụng service để xóa file
                    app(MediaService::class)->delete($record);
                }),
        ];
    }
    
    protected function afterSave(): void
    {
        // Log thay đổi
        Log::info('Đã cập nhật thông tin media', [
            'id' => $this->record->id,
            'name' => $this->record->name,
            'path' => $this->record->path
        ]);
        
        // Hiển thị thông báo thành công
        $this->notify('success', 'Đã cập nhật thông tin media');
    }
} 