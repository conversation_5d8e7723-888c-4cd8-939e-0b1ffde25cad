<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;
use App\Services\SettingsService;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        try {
            $settings = app(SettingsService::class);
            
            $mailConfig = [
                'driver' => $settings->get('mail_driver', 'smtp'),
                'host' => $settings->get('mail_host', 'smtp.mailtrap.io'),
                'port' => $settings->get('mail_port', 2525),
                'username' => $settings->get('mail_username'),
                'password' => $settings->get('mail_password'),
                'encryption' => $settings->get('mail_encryption', 'tls'),
                'from' => [
                    'address' => $settings->get('mail_from_address'),
                    'name' => $settings->get('mail_from_name'),
                ],
            ];

            Config::set('mail', $mailConfig);
        } catch (\Exception $e) {
            // Log error if needed
        }
    }
} 