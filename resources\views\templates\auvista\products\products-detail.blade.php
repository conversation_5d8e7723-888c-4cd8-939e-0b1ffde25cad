@extends('templates.auvista.layouts.default')

@php
    // <PERSON><PERSON><PERSON> bảo c<PERSON>c thuộ<PERSON> t<PERSON>h JSON được chuyển đổi thành mảng
    $gallery = is_string($product->gallery) ? json_decode($product->gallery, true) : ($product->gallery ?? []);
    $technical_specs = [];
    $downloads = [];

    // Xử lý technical_specs
    if (is_string($product->technical_specs)) {
        $decoded = json_decode($product->technical_specs, true);
        $technical_specs = is_array($decoded) ? $decoded : [];
    } elseif (is_array($product->technical_specs)) {
        $technical_specs = $product->technical_specs;
    } else {
        $technical_specs = [];
    }

    // Xử lý downloads
    if (is_string($product->downloads)) {
        $decoded = json_decode($product->downloads, true);
        $downloads = is_array($decoded) ? $decoded : [];
    } elseif (is_array($product->downloads)) {
        $downloads = $product->downloads;
    } else {
        $downloads = [];
    }

    // usage_instructions là text (string) nên không cần decode JSON
    $usage_instructions = $product->usage_instructions ?? '';

    // Chuẩn bị d<PERSON> liệu cho gallery, sử dụng hàm helper getImageUrl
    $mainImage = getImageUrl($product->image_url, 'images/image-product-1.png');
    $galleryImages = [];
    if (!empty($gallery)) {
        foreach ($gallery as $image) {
            // Xử lý cả trường hợp array of strings và array of objects
            if (is_string($image)) {
                $galleryImages[] = getImageUrl($image);
            } elseif (is_array($image) && isset($image['path'])) {
                $galleryImages[] = getImageUrl($image['path']);
            }
        }
    }

    // Nếu không có gallery, sử dụng main image của sản phẩm
    if (empty($galleryImages)) {
        $galleryImages = [$mainImage];
    }

    // Đảm bảo main image luôn có ở đầu gallery
    if (!in_array($mainImage, $galleryImages)) {
        array_unshift($galleryImages, $mainImage);
    }

    $videoUrl = $product->video_url ?? null;
    $videoThumbnail = $product->video_thumbnail ? getImageUrl($product->video_thumbnail) : $mainImage;

    // Logic tạo breadcrumbs
    $breadcrumbs = ['items' => []];
    $breadcrumbs['items'][] = ['name' => __('Trang chủ'), 'url' => route('home'), 'active' => false];
    if (isset($product->category)) {
        $breadcrumbs['items'][] = ['name' => $product->category->name, 'url' => route('products.category', $product->category->slug), 'active' => false];
    }
    $breadcrumbs['items'][] = ['name' => $product->name, 'url' => '', 'active' => true];

    // Lưu sản phẩm vào danh sách đã xem
    $viewedProducts = session('viewed_products', []);
    // Loại bỏ sản phẩm hiện tại nếu đã tồn tại
    $viewedProducts = array_filter($viewedProducts, function ($id) use ($product) {
        return $id != $product->id;
    });
    // Thêm sản phẩm hiện tại vào đầu danh sách
    array_unshift($viewedProducts, $product->id);
    // Giới hạn danh sách chỉ lưu 10 sản phẩm gần nhất
    $viewedProducts = array_slice($viewedProducts, 0, 10);
    // Lưu lại vào session
    session(['viewed_products' => $viewedProducts]);
@endphp

{{-- Truyền dữ liệu breadcrumbs lên layout --}}
@section('breadcrumbs')
    @include('templates.auvista.blocks.breadcrumb', ['breadcrumbs' => $breadcrumbs])
@endsection

@section('title', $product->name)
@section('meta_description', $product->meta_description ?? Str::limit(strip_tags($product->description), 160))
@section('meta_keywords', $product->meta_keywords)
@section('og_title', $product->name)
@section('og_description', $product->meta_description ?? Str::limit(strip_tags($product->description), 160))
@section('og_image', $mainImage)
@section('og_url', url()->current())
@section('og_type', 'product')

@push('styles')
@endpush

@section('content')
    <!-- CSS and Scripts moved here for debugging -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <style>
        .product-gallery-swiper {
            aspect-ratio: 1 / 1;
        }

        .product-gallery-thumbs {
            margin-top: 1rem;
            padding: 0 40px;
        }

        .product-gallery-thumbs .swiper-slide {
            width: 88px;
            height: 88px;
        }

        .product-gallery-thumbs .swiper-slide-thumb-active .aspect-square {
            border-color: #DE383B !important;
        }

        .product-thumbs-prev,
        .product-thumbs-next {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #151515;
            z-index: 10;
        }

        .product-thumbs-prev {
            left: 0;
        }

        .product-thumbs-next {
            right: 0;
        }

        .product-thumbs-prev::after,
        .product-thumbs-next::after {
            font-size: 14px;
            font-weight: bold;
        }

        .product-gallery-main .swiper-slide a {
            display: block;
            width: 100%;
            height: 100%;
        }
    </style>

    <main id="main" class="site-main">
        <div class="page-wrapper blog-archive-product mb-7 lg:mb-[54px] mt-[30px]">
            <div class="container mx-auto">
                {{-- Breadcrumb will be rendered by the layout --}}
            </div>

            <div class="container mx-auto mb-12 lg:mb-[70px]">
                <div class="p-4 lg:p-6 rounded-xl shadow-new-shadow">
                    <div class="flex flex-col md:flex-row gap-5 md:gap-4 lg:gap-[30px]">
                        {{-- Product Gallery --}}
                        <div class="w-full max-w-[42.585%] relative">
                            <div class="sticky top-4">
                                <div class="gallery-single-product">
                                    <!-- Main Gallery Slider -->
                                    <div
                                        class="product-gallery-main mb-4 overflow-hidden rounded-xl border border-primary-border">
                                        <div class="swiper product-gallery-swiper rounded-lg overflow-hidden">
                                            <div class="swiper-wrapper">
                                                @if($videoUrl)
                                                    <div class="swiper-slide">
                                                        <div class="aspect-square bg-white p-4 flex items-center justify-center group relative cursor-pointer"
                                                            onclick="openVideoModal('{{ $videoUrl }}')">
                                                            <img src="{{ $videoThumbnail }}" alt="Product Video Thumbnail"
                                                                class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105" />
                                                            <div
                                                                class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300">
                                                                <div
                                                                    class="w-14 h-14 bg-secondary-main rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300 shadow-lg">
                                                                    <svg class="w-8 h-8 text-white" fill="currentColor"
                                                                        viewBox="0 0 24 24">
                                                                        <path d="M8 5v14l11-7z" />
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                                @foreach($galleryImages as $image)
                                                    <div class="swiper-slide">
                                                        <a href="{{ getImageUrl($image) }}" data-fancybox="product-gallery">
                                                            <div
                                                                class="aspect-square bg-white p-4 flex items-center justify-center group">
                                                                <img src="{{ getImageUrl($image, 'images/image-product-1.png') }}"
                                                                    alt="{{ $product->name }}"
                                                                    class="w-full h-full object-contain transition-transform duration-300 group-hover:scale-105" />
                                                            </div>
                                                        </a>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Thumbnail Slider -->
                                    <div class="product-gallery-thumbs relative">
                                        <div class="swiper product-thumbs-swiper">
                                            <div class="swiper-wrapper">
                                                @if($videoUrl)
                                                    <div class="swiper-slide cursor-pointer">
                                                        <div
                                                            class="aspect-square bg-white p-2 border border-primary-border rounded-xl hover:border-secondary-main transition-colors relative">
                                                            <img src="{{ $videoThumbnail }}" alt="Video Thumbnail"
                                                                class="w-full h-full object-contain" />
                                                            <div
                                                                class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-secondary-main rounded-full flex items-center justify-center">
                                                                <svg class="w-6 h-6 text-white" fill="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path d="M8 5v14l11-7z" />
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                                @foreach($galleryImages as $thumb)
                                                    <div class="swiper-slide cursor-pointer">
                                                        <div
                                                            class="aspect-square bg-white p-2 border border-primary-border rounded-xl hover:border-secondary-main transition-colors">
                                                            <img src="{{ getImageUrl($thumb, 'images/image-product-1.png') }}"
                                                                alt="Product Thumbnail {{ $loop->iteration }}"
                                                                class="w-full h-full object-contain" />
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-button-next product-thumbs-next bg-primary-background3 shadow-new-shadow">
                                        </div>
                                        <div
                                            class="swiper-button-prev product-thumbs-prev bg-primary-background3 shadow-new-shadow">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Product Info --}}
                        <div class="w-full">
                            <div class="product-summary product-info">
                                <h1 class="text-primary-base font-bold text-2xl md:text-[28px]/[1.4] mb-1">
                                    {{ $product->name }}
                                </h1>
                                <div class="flex items-center gap-4 mb-2">
                                    {{-- Rating and stock status --}}
                                    <div class="flex items-center gap-1">
                                        @for($i = 0; $i < 5; $i++)
                                            @if($i < ($product->average_rating ?? 0))
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                    <path
                                                        d="M3.61261 15.4435C3.22661 15.6415 2.78861 15.2945 2.86661 14.8515L3.69661 10.1215L0.173607 6.7655C-0.155393 6.4515 0.0156066 5.8775 0.456607 5.8155L5.35461 5.1195L7.53861 0.7925C7.73561 0.4025 8.26861 0.4025 8.46561 0.7925L10.6496 5.1195L15.5476 5.8155C15.9886 5.8775 16.1596 6.4515 15.8296 6.7655L12.3076 10.1215L13.1376 14.8515C13.2156 15.2945 12.7776 15.6415 12.3916 15.4435L8.00061 13.1875L3.61161 15.4435H3.61261Z"
                                                        fill="#DE383B" />
                                                </svg>
                                            @else
                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                                    <path
                                                        d="M2.86661 14.8511C2.78861 15.2951 3.22661 15.6421 3.61261 15.4441L8.00261 13.1881L12.3916 15.4441C12.7776 15.6421 13.2156 15.2951 13.1376 14.8521L12.3076 10.1221L15.8296 6.76614C16.1596 6.45214 15.9896 5.87814 15.5476 5.81614L10.6496 5.12014L8.46561 0.793144C8.424 0.705433 8.35836 0.631327 8.27632 0.579436C8.19427 0.527545 8.09918 0.5 8.00211 0.5C7.90503 0.5 7.80994 0.527545 7.7279 0.579436C7.64585 0.631327 7.58021 0.705433 7.53861 0.793144L5.35461 5.12114L0.456607 5.81714C0.0156066 5.87914 -0.155393 6.45314 0.173607 6.76714L3.69661 10.1231L2.86661 14.8531V14.8511Z"
                                                        fill="#D1D5DB" />
                                                </svg>
                                            @endif
                                        @endfor
                                    </div>
                                    <span class="text-primary-gray2">{{ $product->reviews_count ?? 0 }} đánh giá</span>
                                    <span class="text-primary-gray2">{{ $product->sold_count ?? 0 }} đã bán</span>
                                </div>


                                {{-- Price --}}
                                <div class="mb-2">
                                    <div class="flex items-center gap-4">
                                        <span class="text-[38px]/[1.4] text-primary-price font-bold">
                                            {{ number_format($product->sale_price ?? $product->price, 0, ',', '.') }}đ
                                        </span>
                                        @if($product->price && $product->sale_price < $product->price)
                                            <span class="text-2xl text-gray-400 line-through">
                                                {{ number_format($product->price, 0, ',', '.') }}đ
                                            </span>
                                        @endif
                                    </div>
                                </div>

                                {{-- Product Meta --}}
                                <div class="space-y-1 mb-4 bg-primary-grey p-4 rounded-2xl">
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Mã
                                            hàng:</span><span
                                            class="text-primary-base">{{ $product->sku ?? 'Đang cập nhật' }}</span></div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Thương
                                            hiệu:</span><a
                                            href="{{ $product->brand ? route('products.brand', $product->brand->slug) : '#' }}"
                                            class="text-primary-base hover:text-secondary-main">{{ $product->brand->name ?? 'Đang cập nhật' }}</a>
                                    </div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Xuất
                                            xứ:</span><span
                                            class="text-primary-base">{{ $product->origin ?? 'Đang cập nhật' }}</span></div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Bảo
                                            hành:</span><span
                                            class="text-primary-base">{{ $product->warranty ?? 'Đang cập nhật' }}</span>
                                    </div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Đơn
                                            vị:</span><span
                                            class="text-primary-base">{{ $product->unit ?? 'Đang cập nhật' }}</span></div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Tình
                                            trạng:</span><span
                                            class="text-primary-base">{{ $product->condition ?? 'Mới 100%' }}</span></div>
                                    <div class="flex items-center"><span
                                            class="text-primary-gray2 w-32 font-medium pl-4 relative before:content-[''] before:bg-primary-gray2 before:rounded-full before:w-[6px] before:h-[6px] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2">Tồn
                                            kho:</span><span
                                            class="{{ $product->stock > 0 ? 'text-green-600' : 'text-red-500' }}">{{ $product->stock > 0 ? 'Còn hàng' : 'Hết hàng' }}</span>
                                    </div>
                                </div>

                                {{-- Quantity and Add to Cart --}}
                                <div class="space-y-5">
                                    <!-- Quantity Selector -->
                                    <div class="flex items-center gap-4">
                                        <span class="text-primary-base font-medium">Số lượng</span>
                                        <div class="flex items-center border border-gray-300 rounded-lg">
                                            <button
                                                class="w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors border-r"
                                                onclick="decreaseQuantity()">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M20 12H4" />
                                                </svg>
                                            </button>
                                            <input type="number" id="quantity" value="10" min="1"
                                                class="w-16 h-10 text-center border-0 focus:ring-0 focus:outline-none"
                                                readonly />
                                            <button
                                                class="w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors border-l"
                                                onclick="increaseQuantity()">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 4v16m8-8H4" />
                                                </svg>
                                            </button>
                                        </div>
                                        <span class="text-primary-gray2">Còn {{ $product->stock ?? 1802 }} sản phẩm</span>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div
                                        class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 items-center gap-2 sm:gap-3 lg:gap-4">
                                        <!-- Store Location -->
                                        <button
                                            class="col-span-1 h-10 sm:h-12 lg:h-[52px] border border-primary-green text-primary-green rounded-full font-medium hover:bg-green-50 transition-colors flex items-center justify-center gap-2 w-full">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                            Chỉ đường đi
                                        </button>

                                        <!-- Quote Request -->
                                        <button
                                            class="col-span-1 h-10 sm:h-12 lg:h-[52px] border border-primary-price text-primary-price rounded-full font-medium hover:bg-red-50 transition-colors w-full">
                                            Tư vấn & báo giá
                                        </button>

                                        <!-- Add to Cart -->
                                        <button
                                            class="col-span-2 lg:col-span-1 h-10 sm:h-12 lg:h-[52px] border border-secondary-main text-secondary-main rounded-full font-medium hover:bg-blue-200 transition-colors flex items-center justify-center gap-2 w-full whitespace-nowrap">
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z"
                                                    fill="#246DDA" />
                                                <path
                                                    d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z"
                                                    fill="#246DDA" />
                                                <path
                                                    d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z"
                                                    fill="#246DDA" />
                                                <path
                                                    d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z"
                                                    fill="#246DDA" />
                                            </svg>
                                            Thêm vào giỏ hàng
                                        </button>

                                        <!-- Buy Now -->
                                        <button
                                            class="col-span-2 lg:col-span-3 h-10 sm:h-12 lg:h-[52px] bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-300 w-full">
                                            Mua ngay
                                        </button>
                                    </div>
                                </div>

                                <!-- Additional Info -->
                                <div
                                    class="mt-5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 bg-primary-grey p-4 rounded-2xl">
                                    <!-- Bảo hành 1 đổi 1 -->
                                    <div class="col-span-1 md:col-span-1 lg:col-span-1">
                                        <div class="flex items-start gap-4">
                                            <div class="w-6 h-6 flex-shrink-0 mt-3">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <g clip-path="url(#clip0_1_6284)">
                                                        <path
                                                            d="M12.279 1.6715C12.0999 1.59974 11.9001 1.59974 11.721 1.6715L2.769 5.252L12 8.9435L21.231 5.252L12.279 1.6715ZM22.5 6.3605L12.75 10.2605V22.1435L22.5 18.2435V6.3605ZM11.25 22.145V10.259L1.5 6.3605V18.245L11.25 22.145ZM11.1645 0.278002C11.7008 0.0635012 12.2992 0.0635012 12.8355 0.278002L23.529 4.556C23.668 4.61172 23.7872 4.70779 23.8711 4.83184C23.9551 4.95588 24 5.10222 24 5.252V18.245C23.9998 18.5447 23.9098 18.8375 23.7417 19.0856C23.5735 19.3338 23.3348 19.5258 23.0565 19.637L12.279 23.948C12.0999 24.0198 11.9001 24.0198 11.721 23.948L0.945 19.637C0.666377 19.526 0.427414 19.3341 0.258963 19.086C0.0905126 18.8378 0.000309494 18.5449 0 18.245V5.252C3.48599e-05 5.10222 0.0449156 4.95588 0.128861 4.83184C0.212805 4.70779 0.331969 4.61172 0.471 4.556L11.1645 0.278002Z"
                                                            fill="#151515" />
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_1_6284">
                                                            <rect width="24" height="24" fill="white" />
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <p class="text-primary-base font-bold mb-1">Bảo hành 1 đổi 1<br
                                                        class="hidden md:block" />trong vòng 30 ngày</p>
                                                <a href="#" class="text-secondary-main font-medium hover:underline">Xem chi
                                                    tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Bảo hành chính hãng -->
                                    <div class="col-span-1 md:col-span-1 lg:col-span-1">
                                        <div class="flex items-start gap-4">
                                            <div class="w-6 h-6 flex-shrink-0 mt-3">
                                                <svg width="25" height="24" viewBox="0 0 25 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M8.50701 2.385C7.07839 2.77872 5.65948 3.20685 4.25151 3.669C4.12294 3.71065 4.00866 3.78756 3.92167 3.89099C3.83469 3.99442 3.7785 4.1202 3.75951 4.254C2.92851 10.4895 4.84851 15.039 7.13901 18.036C8.10878 19.3176 9.26512 20.4466 10.5695 21.3855C11.0885 21.7515 11.5475 22.0155 11.909 22.185C12.089 22.2705 12.236 22.3275 12.3485 22.362C12.3977 22.3793 12.4484 22.3918 12.5 22.3995C12.551 22.3912 12.6011 22.3787 12.65 22.362C12.764 22.3275 12.911 22.2705 13.091 22.185C13.451 22.0155 13.9115 21.75 14.4305 21.3855C15.7349 20.4466 16.8912 19.3176 17.861 18.036C20.1515 15.0405 22.0715 10.4895 21.2405 4.254C21.2217 4.12014 21.1656 3.99427 21.0786 3.89082C20.9915 3.78736 20.8772 3.71049 20.7485 3.669C19.772 3.3495 18.1235 2.829 16.493 2.3865C14.828 1.935 13.2965 1.6005 12.5 1.6005C11.705 1.6005 10.172 1.935 8.50701 2.3865V2.385ZM8.10801 0.84C9.73551 0.3975 11.465 0 12.5 0C13.535 0 15.2645 0.3975 16.892 0.84C18.557 1.29 20.2355 1.8225 21.2225 2.145C21.6352 2.28128 22.001 2.5312 22.278 2.86605C22.555 3.20091 22.732 3.60711 22.7885 4.038C23.6825 10.7535 21.608 15.7305 19.091 19.023C18.0237 20.4315 16.751 21.6718 15.3155 22.7025C14.8191 23.0592 14.2932 23.3728 13.7435 23.64C13.3235 23.838 12.872 24 12.5 24C12.128 24 11.678 23.838 11.2565 23.64C10.7068 23.3728 10.1809 23.0592 9.68451 22.7025C8.24905 21.6717 6.97638 20.4314 5.90901 19.023C3.39201 15.7305 1.31751 10.7535 2.21151 4.038C2.26804 3.60711 2.44497 3.20091 2.72198 2.86605C2.99899 2.5312 3.36485 2.28128 3.77751 2.145C5.2103 1.67521 6.65422 1.24009 8.10801 0.84Z"
                                                        fill="#151515" />
                                                    <path
                                                        d="M16.7819 7.7203C16.8518 7.78997 16.9072 7.87273 16.945 7.96385C16.9828 8.05497 17.0023 8.15265 17.0023 8.2513C17.0023 8.34995 16.9828 8.44763 16.945 8.53875C16.9072 8.62987 16.8518 8.71263 16.7819 8.7823L12.2819 13.2823C12.2123 13.3521 12.1295 13.4076 12.0384 13.4454C11.9473 13.4832 11.8496 13.5026 11.7509 13.5026C11.6523 13.5026 11.5546 13.4832 11.4635 13.4454C11.3724 13.4076 11.2896 13.3521 11.2199 13.2823L8.96992 11.0323C8.90019 10.9626 8.84487 10.8798 8.80713 10.7887C8.76939 10.6976 8.74997 10.5999 8.74997 10.5013C8.74997 10.4027 8.76939 10.305 8.80713 10.2139C8.84487 10.1228 8.90019 10.04 8.96992 9.9703C9.03965 9.90057 9.12243 9.84525 9.21354 9.80751C9.30465 9.76978 9.4023 9.75035 9.50092 9.75035C9.59953 9.75035 9.69718 9.76978 9.78829 9.80751C9.8794 9.84525 9.96219 9.90057 10.0319 9.9703L11.7509 11.6908L15.7199 7.7203C15.7896 7.65045 15.8724 7.59504 15.9635 7.55723C16.0546 7.51942 16.1523 7.49996 16.2509 7.49996C16.3496 7.49996 16.4473 7.51942 16.5384 7.55723C16.6295 7.59504 16.7122 7.65045 16.7819 7.7203Z"
                                                        fill="#151515" />
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <p class="text-primary-base font-bold mb-1">Bảo hành chính hãng<br
                                                        class="hidden md:block" />qua App điện tử 69 tháng</p>
                                                <a href="#" class="text-secondary-main font-medium hover:underline">Xem chi
                                                    tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Chương trình khuyến mại -->
                                    <div class="col-span-1 md:col-span-2 lg:col-span-1">
                                        <div class="flex items-start gap-4">
                                            <div class="w-6 h-6 flex-shrink-0 mt-3">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M20.163 3.837C20.2501 3.92406 20.3192 4.02742 20.3663 4.14118C20.4134 4.25494 20.4377 4.37686 20.4377 4.5C20.4377 4.62314 20.4134 4.74506 20.3663 4.85882C20.3192 4.97258 20.2501 5.07594 20.163 5.163L5.163 20.163C5.07593 20.2501 4.97257 20.3191 4.85881 20.3663C4.74506 20.4134 4.62313 20.4376 4.5 20.4376C4.37687 20.4376 4.25494 20.4134 4.14119 20.3663C4.02743 20.3191 3.92407 20.2501 3.837 20.163C3.74993 20.0759 3.68087 19.9726 3.63375 19.8588C3.58663 19.7451 3.56238 19.6231 3.56238 19.5C3.56238 19.3769 3.58663 19.2549 3.63375 19.1412C3.68087 19.0274 3.74993 18.9241 3.837 18.837L18.837 3.837C18.9241 3.74992 19.0274 3.68084 19.1412 3.63371C19.2549 3.58658 19.3769 3.56233 19.5 3.56233C19.6231 3.56233 19.7451 3.58658 19.8588 3.63371C19.9726 3.68084 20.0759 3.74992 20.163 3.837ZM6.75 9C6.15326 9 5.58097 8.76295 5.15901 8.34099C4.73705 7.91903 4.5 7.34674 4.5 6.75C4.5 6.15326 4.73705 5.58097 5.15901 5.15901C5.58097 4.73705 6.15326 4.5 6.75 4.5C7.34674 4.5 7.91903 4.73705 8.34099 5.15901C8.76295 5.58097 9 6.15326 9 6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9ZM6.75 10.5C7.74456 10.5 8.69839 10.1049 9.40165 9.40165C10.1049 8.69839 10.5 7.74456 10.5 6.75C10.5 5.75544 10.1049 4.80161 9.40165 4.09835C8.69839 3.39509 7.74456 3 6.75 3C5.75544 3 4.80161 3.39509 4.09835 4.09835C3.39509 4.80161 3 5.75544 3 6.75C3 7.74456 3.39509 8.69839 4.09835 9.40165C4.80161 10.1049 5.75544 10.5 6.75 10.5ZM17.25 19.5C16.6533 19.5 16.081 19.2629 15.659 18.841C15.2371 18.419 15 17.8467 15 17.25C15 16.6533 15.2371 16.081 15.659 15.659C16.081 15.2371 16.6533 15 17.25 15C17.8467 15 18.419 15.2371 18.841 15.659C19.2629 16.081 19.5 16.6533 19.5 17.25C19.5 17.8467 19.2629 18.419 18.841 18.841C18.419 19.2629 17.8467 19.5 17.25 19.5ZM17.25 21C18.2446 21 19.1984 20.6049 19.9016 19.9016C20.6049 19.1984 21 18.2446 21 17.25C21 16.2554 20.6049 15.3016 19.9016 14.5983C19.1984 13.8951 18.2446 13.5 17.25 13.5C16.2554 13.5 15.3016 13.8951 14.5983 14.5983C13.8951 15.3016 13.5 16.2554 13.5 17.25C13.5 18.24456 13.8951 19.1984 14.5983 19.9016C15.3016 20.6049 16.2554 21 17.25 21Z"
                                                        fill="#151515" />
                                                </svg>
                                            </div>
                                            <div class="flex-1">
                                                <p class="text-primary-base font-bold mb-1">Chương trình<br
                                                        class="hidden lg:block" />khuyến mại</p>
                                                <a href="#" class="text-secondary-main font-medium hover:underline">Xem chi
                                                    tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Section for Tabs and Technical Specs Sidebar --}}
            <div class="container mx-auto mb-12 lg:mb-[70px]">
                <div class="flex flex-col lg:flex-row gap-5 md:gap-4 lg:gap-[30px]">
                    {{-- Left Column - Tab content --}}
                    <div class="w-full lg:w-auto lg:flex-1">
                        <div class="nav-tabs p-4 lg:p-6 rounded-xl shadow-new-shadow">
                            {{-- Tab Navigation --}}
                            <div class="tab-navigation mb-6">
                                <div class="flex flex-wrap gap-3">
                                    <button
                                        class="tab-btn active bg-gradient2 text-white px-6 h-11 rounded-full font-bold transition-colors"
                                        data-tab="description">Mô tả sản phẩm</button>
                                    <button
                                        class="tab-btn bg-primary-border text-primary-gray2 px-6 h-11 rounded-full font-bold hover:bg-gray-200 transition-colors"
                                        data-tab="instructions">Hướng dẫn sử dụng</button>
                                    <button
                                        class="tab-btn bg-primary-border text-primary-gray2 px-6 h-11 rounded-full font-bold hover:bg-gray-200 transition-colors"
                                        data-tab="specifications">Thông số</button>
                                    <button
                                        class="tab-btn bg-primary-border text-primary-gray2 px-6 h-11 rounded-full font-bold hover:bg-gray-200 transition-colors"
                                        data-tab="downloads">Download Catalogue</button>
                                </div>
                            </div>

                            {{-- Tab Content --}}
                            <div class="tab-content">
                                {{-- Description Tab --}}
                                <div class="tab-panel active" data-panel="description">
                                    <div class="content-wrapper relative overflow-hidden" style="max-height: 600px">
                                        <div class="content-text text-primary-base text-base/[1.4] prose max-w-none">
                                            {!! $product->description !!}
                                        </div>
                                        <div
                                            class="fade-overlay absolute bottom-0 left-0 w-full h-24 bg-gradient-to-t from-white to-transparent">
                                        </div>
                                    </div>
                                    <button class="show-more-btn mt-4 text-secondary-main font-bold hover:underline">Xem
                                        thêm</button>
                                </div>

                                {{-- Instructions Tab --}}
                                <div class="tab-panel hidden" data-panel="instructions">
                                    <div class="content-text text-primary-base text-base/[1.4] prose max-w-none">
                                        {!! $usage_instructions !!}
                                    </div>
                                </div>

                                {{-- Specifications Tab --}}
                                <div class="tab-panel hidden" data-panel="specifications">
                                    <div class="content-text text-primary-base text-base/[1.4] prose max-w-none">
                                        @if(!empty($technical_specs))
                                            <table class="w-full">
                                                <tbody>
                                                    @foreach($technical_specs as $spec)
                                                        <tr class="border-b">
                                                            <td class="py-2 pr-4 font-semibold">{{ $spec['key'] ?? '' }}</td>
                                                            <td class="py-2">{{ $spec['value'] ?? '' }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        @else
                                            <p>Thông số kỹ thuật đang được cập nhật.</p>
                                        @endif
                                    </div>
                                </div>

                                {{-- Downloads Tab --}}
                                <div class="tab-panel hidden" data-panel="downloads">
                                    <div class="content-text text-primary-base text-base/[1.4]">
                                        @if(!empty($downloads))
                                            <ul>
                                                @foreach($downloads as $download)
                                                    <li><a href="{{ $download['url'] ?? '#' }}"
                                                            class="text-secondary-main hover:underline"
                                                            download>{{ $download['title'] ?? 'Tải xuống' }}</a></li>
                                                @endforeach
                                            </ul>
                                        @else
                                            <p>Chưa có tài liệu để tải xuống.</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Right Column - Technical Specs Sidebar --}}
                    <div class="w-full lg:max-w-[22.6950355%] relative">
                        <div class="sticky top-4 shadow-new-shadow rounded-xl">
                            <div class="flex flex-col">
                                <h2 class="bg-secondary-main text-white px-6 py-4 rounded-tl-lg rounded-tr-lg font-bold">
                                    Thông số kỹ thuật
                                </h2>
                                @if(!empty($technical_specs))
                                    <ul class="px-6 py-4 divide-y divide-primary-border">
                                        @foreach(array_slice($technical_specs, 0, 5) as $spec)
                                            <li class="py-2">
                                                <label
                                                    class="font-medium mb-1 block text-primary-base">{{ $spec['key'] ?? '' }}</label>
                                                <p class="text-primary-gray2">{{ $spec['value'] ?? '' }}</p>
                                            </li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class="p-6">Thông số đang được cập nhật.</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Review Block --}}
        @component('templates.auvista.blocks.review', ['product' => $product, 'reviews' => $reviews, 'reviewsCount' => $reviewsCount, 'averageRating' => $averageRating])
        @endcomponent

        {{-- Related Products --}}
        <div class="container mx-auto related-slider mb-12 lg:mb-[40px]">
            <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]">Sản phẩm liên quan</h2>
            @if(isset($related_products) && $related_products->count() > 0)
                <div class="swiper-slider relative -ml-2 -mr-2 lg:-ml-[15px] lg:-mr-[15px]" data-items="1.3"
                    data-mobile="1.7" data-tablet="2.5" data-desktop="3.5" data-large="4" data-xlarge="4" data-spacing="0"
                    data-loop="true" data-navigation="true" data-autoplay="true" data-autoplay-delay="3000">
                    <div class="swiper related-products-swiper">
                        <div class="swiper-wrapper">
                            @foreach($related_products as $related)
                                <div class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]">
                                    @include('templates.auvista.components.product-card', ['product' => $related])
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- Navigation Buttons -->
                    <div class="swiper-button-prev related-prev absolute left-0 top-1/2 transform -translate-y-1/2 w-11 h-11 bg-white border border-primary-border rounded-full flex items-center justify-center shadow-lg z-10 hover:bg-primary-base hover:text-white transition-colors duration-300">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="transform rotate-180">
                            <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="swiper-button-next related-next absolute right-0 top-1/2 transform -translate-y-1/2 w-11 h-11 bg-white border border-primary-border rounded-full flex items-center justify-center shadow-lg z-10 hover:bg-primary-base hover:text-white transition-colors duration-300">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            @else
                <p class="text-primary-gray2">Không có sản phẩm liên quan.</p>
            @endif
        </div>

        {{-- Recently Viewed Products --}}
        @php
            // Lấy danh sách sản phẩm đã xem từ session
            $viewedProducts = session('viewed_products', []);
            // Loại bỏ sản phẩm hiện tại khỏi danh sách
            $viewedProducts = array_filter($viewedProducts, function ($id) use ($product) {
                return $id != $product->id;
            });
            // Lấy tối đa 8 sản phẩm gần nhất để đủ cho slider
            $viewedProducts = array_slice(array_reverse($viewedProducts), 0, 8);

            // Lấy thông tin chi tiết các sản phẩm đã xem
            $recentlyViewedProducts = collect();
            if (!empty($viewedProducts)) {
                $recentlyViewedProducts = \App\Models\Product::whereIn('id', $viewedProducts)
                    ->where('status', 'active')
                    ->get();
            }
        @endphp

        @if($recentlyViewedProducts->count() > 0)
            <div class="container mx-auto related-slider mb-12 lg:mb-[40px]">
                <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]">Sản phẩm đã xem ({{ $recentlyViewedProducts->count() }})</h2>
                <div class="swiper-slider relative -ml-2 -mr-2 lg:-ml-[15px] lg:-mr-[15px]" data-items="1.3"
                    data-mobile="1.7" data-tablet="2.5" data-desktop="3.5" data-large="4" data-xlarge="4" data-spacing="0"
                    data-loop="true" data-navigation="true" data-autoplay="true" data-autoplay-delay="3000">
                    <div class="swiper recently-viewed-swiper">
                        <div class="swiper-wrapper">
                            @foreach($recentlyViewedProducts as $viewedProduct)
                                <div class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]">
                                    @include('templates.auvista.components.product-card', ['product' => $viewedProduct])
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <!-- Navigation Buttons -->
                    <div class="swiper-button-prev viewed-prev absolute left-0 top-1/2 transform -translate-y-1/2 w-11 h-11 bg-white border border-primary-border rounded-full flex items-center justify-center shadow-lg z-10 hover:bg-primary-base hover:text-white transition-colors duration-300">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="transform rotate-180">
                            <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="swiper-button-next viewed-next absolute right-0 top-1/2 transform -translate-y-1/2 w-11 h-11 bg-white border border-primary-border rounded-full flex items-center justify-center shadow-lg z-10 hover:bg-primary-base hover:text-white transition-colors duration-300">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M6 12L10 8L6 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
            </div>
        @else
            <div class="container mx-auto mb-12 lg:mb-[40px]">
                <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]">Sản phẩm đã xem</h2>
                <p class="text-primary-gray2">Bạn chưa xem sản phẩm nào khác. Hãy khám phá thêm các sản phẩm của chúng tôi!</p>
            </div>
        @endif

            <!-- Video Modal -->
            <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-80 z-50 hidden flex items-center justify-center"
                onclick="closeVideoModal()">
                <div class="relative w-full max-w-4xl" onclick="event.stopPropagation();">
                    <button class="absolute -top-10 -right-2 text-white text-4xl"
                        onclick="closeVideoModal()">&times;</button>
                    <div class="aspect-w-16 aspect-h-9">
                        <iframe id="videoPlayer" src="" frameborder="0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen></iframe>
                    </div>
                </div>
            </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        window.addEventListener('load', function () {
            // Product Gallery Swiper
            const thumbsSwiperEl = document.querySelector(".product-thumbs-swiper");
            if (thumbsSwiperEl) {
                const thumbsSwiper = new Swiper(thumbsSwiperEl, {
                    spaceBetween: 12,
                    slidesPerView: 'auto',
                    freeMode: true,
                    watchSlidesProgress: true,
                    navigation: {
                        nextEl: ".product-thumbs-next",
                        prevEl: ".product-thumbs-prev",
                    },
                });

                const mainSwiperEl = document.querySelector(".product-gallery-swiper");
                if (mainSwiperEl) {
                    const mainSwiper = new Swiper(mainSwiperEl, {
                        spaceBetween: 10,
                        effect: 'fade',
                        fadeEffect: { crossFade: true },
                        thumbs: { swiper: thumbsSwiper },
                    });

                    setTimeout(function () {
                        if (mainSwiper && typeof mainSwiper.update === 'function') {
                            mainSwiper.update();
                        }
                    }, 100);
                }
            }

            // Related Products Swiper
            const relatedSwiperEl = document.querySelector(".related-products-swiper");
            if (relatedSwiperEl) {
                const relatedSwiper = new Swiper(relatedSwiperEl, {
                    slidesPerView: 1.3,
                    spaceBetween: 16,
                    loop: true,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false,
                    },
                    navigation: {
                        nextEl: ".related-next",
                        prevEl: ".related-prev",
                    },
                    breakpoints: {
                        480: {
                            slidesPerView: 1.7,
                            spaceBetween: 16,
                        },
                        640: {
                            slidesPerView: 2.5,
                            spaceBetween: 20,
                        },
                        768: {
                            slidesPerView: 3.5,
                            spaceBetween: 24,
                        },
                        1024: {
                            slidesPerView: 4,
                            spaceBetween: 30,
                        },
                        1280: {
                            slidesPerView: 4,
                            spaceBetween: 30,
                        }
                    }
                });
            }

            // Recently Viewed Products Swiper
            const recentlyViewedSwiperEl = document.querySelector(".recently-viewed-swiper");
            if (recentlyViewedSwiperEl) {
                const recentlyViewedSwiper = new Swiper(recentlyViewedSwiperEl, {
                    slidesPerView: 1.3,
                    spaceBetween: 16,
                    loop: true,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false,
                    },
                    navigation: {
                        nextEl: ".viewed-next",
                        prevEl: ".viewed-prev",
                    },
                    breakpoints: {
                        480: {
                            slidesPerView: 1.7,
                            spaceBetween: 16,
                        },
                        640: {
                            slidesPerView: 2.5,
                            spaceBetween: 20,
                        },
                        768: {
                            slidesPerView: 3.5,
                            spaceBetween: 24,
                        },
                        1024: {
                            slidesPerView: 4,
                            spaceBetween: 30,
                        },
                        1280: {
                            slidesPerView: 4,
                            spaceBetween: 30,
                        }
                    }
                });
            }

            // --- Tab Functionality ---
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanels = document.querySelectorAll('.tab-panel');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tab = button.dataset.tab;
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-gradient2', 'text-white');
                        btn.classList.add('bg-primary-border', 'text-primary-gray2');
                    });
                    button.classList.add('active', 'bg-gradient2', 'text-white');
                    button.classList.remove('bg-primary-border', 'text-primary-gray2');
                    tabPanels.forEach(panel => {
                        panel.style.display = panel.dataset.panel === tab ? 'block' : 'none';
                    });
                });
            });

            // --- Show More for Description ---
            const showMoreBtn = document.querySelector('.show-more-btn');
            const contentWrapper = document.querySelector('.content-wrapper');
            const fadeOverlay = document.querySelector('.fade-overlay');

            if (showMoreBtn && contentWrapper && fadeOverlay) {
                if (contentWrapper.scrollHeight > contentWrapper.clientHeight) {
                    fadeOverlay.style.display = 'block';
                    showMoreBtn.style.display = 'block';
                } else {
                    fadeOverlay.style.display = 'none';
                    showMoreBtn.style.display = 'none';
                }

                showMoreBtn.addEventListener('click', () => {
                    contentWrapper.style.maxHeight = 'none';
                    fadeOverlay.style.display = 'none';
                    showMoreBtn.style.display = 'none';
                });
            }
        });

        const videoModal = document.getElementById('videoModal');
        const videoPlayer = document.getElementById('videoPlayer');
        function openVideoModal(url) {
            if (videoModal && videoPlayer) {
                const videoUrl = new URL(url);
                if (videoUrl.hostname.includes('youtube.com') || videoUrl.hostname.includes('youtu.be')) {
                    videoUrl.searchParams.set('autoplay', '1');
                } else if (videoUrl.hostname.includes('vimeo.com')) {
                    videoUrl.searchParams.set('autoplay', '1');
                }
                videoPlayer.src = videoUrl.toString();
                videoModal.style.display = 'flex';
            }
        }
        function closeVideoModal() {
            if (videoModal && videoPlayer) {
                videoModal.style.display = 'none';
                videoPlayer.src = '';
            }
        }
        document.addEventListener('keydown', function (event) {
            if (event.key === 'Escape' && videoModal.style.display === 'flex') {
                closeVideoModal();
            }
        });

        // --- Quantity Functions ---
        function increaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            if (quantityInput) {
                let currentValue = parseInt(quantityInput.value) || 1;
                quantityInput.value = currentValue + 1;
            }
        }

        function decreaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            if (quantityInput) {
                let currentValue = parseInt(quantityInput.value) || 1;
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                }
            }
        }
    </script>
@endsection