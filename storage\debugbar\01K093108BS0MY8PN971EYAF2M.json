{"__meta": {"id": "01K093108BS0MY8PN971EYAF2M", "datetime": "2025-07-16 14:43:45", "utime": **********.420188, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752651824.685133, "end": **********.420226, "duration": 0.7350931167602539, "duration_str": "735ms", "measures": [{"label": "Booting", "start": 1752651824.685133, "relative_start": 0, "end": **********.049151, "relative_end": **********.049151, "duration": 0.*****************, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.049182, "relative_start": 0.*****************, "end": **********.420229, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.255256, "relative_start": 0.****************, "end": **********.25848, "relative_end": **********.25848, "duration": 0.0032241344451904297, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.section.index", "start": **********.411864, "relative_start": 0.****************, "end": **********.411864, "relative_end": **********.411864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.heading", "start": **********.413853, "relative_start": 0.**************, "end": **********.413853, "relative_end": **********.413853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.415066, "relative_start": 0.********34527588, "end": **********.415066, "relative_end": **********.415066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.416608, "relative_start": 0.7314751148223877, "end": **********.41804, "relative_end": **********.41804, "duration": 0.0014319419860839844, "duration_str": "1.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41302672, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "filament::components.section.index", "param_count": null, "params": [], "start": **********.411812, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.section.heading", "param_count": null, "params": [], "start": **********.413824, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/heading.blade.phpfilament::components.section.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": **********.415046, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.033600000000000005, "accumulated_duration_str": "33.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.292203, "duration": 0.00937, "duration_str": "9.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 27.887}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.341084, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 27.887, "width_percent": 6.518}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.345848, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 34.405, "width_percent": 2.47}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.348286, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 36.875, "width_percent": 6.964}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.351953, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 43.839, "width_percent": 1.726}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3534029, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 45.565, "width_percent": 1.25}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.354677, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 46.815, "width_percent": 1.607}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.356571, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 48.423, "width_percent": 2.768}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3589568, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 51.19, "width_percent": 1.726}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3602421, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 52.917, "width_percent": 0.893}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.361308, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 53.81, "width_percent": 1.458}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3625221, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 55.268, "width_percent": 0.923}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.364134, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 56.19, "width_percent": 3.571}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.366226, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 59.762, "width_percent": 1.042}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.367244, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 60.804, "width_percent": 0.952}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.368195, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 61.756, "width_percent": 0.804}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.369149, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 62.56, "width_percent": 0.685}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3707118, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 63.244, "width_percent": 3.393}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3728588, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 66.637, "width_percent": 2.351}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3744962, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 68.988, "width_percent": 1.31}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.37557, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 70.298, "width_percent": 1.161}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.376788, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 71.458, "width_percent": 2.321}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3791502, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 73.78, "width_percent": 2.47}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3808088, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 76.25, "width_percent": 1.25}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.381869, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 77.5, "width_percent": 1.071}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3829072, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 78.571, "width_percent": 0.893}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.384422, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 79.464, "width_percent": 3.661}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.386689, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 83.125, "width_percent": 1.518}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.38803, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 84.643, "width_percent": 1.042}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.389391, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 85.685, "width_percent": 2.321}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.391446, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 88.006, "width_percent": 2.917}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.393464, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 90.923, "width_percent": 1.875}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.395046, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 92.798, "width_percent": 1.339}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.396409, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 94.137, "width_percent": 1.31}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3981051, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 95.446, "width_percent": 1.875}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3997152, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 97.321, "width_percent": 1.369}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.401122, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 98.69, "width_percent": 1.31}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.treatments-chart #vxxoICav8IEhI9e0BGZh": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"f4e874ac94dc628623a611ebc2c26228\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.treatments-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\TreatmentsChart\"\n  \"id\" => \"vxxoICav8IEhI9e0BGZh\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\TreatmentsChart@updateChartData<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "736ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1290160807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1290160807\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-812220543 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"349 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;f4e874ac94dc628623a611ebc2c26228&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;vxxoICav8IEhI9e0BGZh&quot;,&quot;name&quot;:&quot;app.filament.widgets.treatments-chart&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;e6529866ac81c82b077c00182181b2bab2243dbe15f4952f268161f33244d8a7&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-812220543\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-599639513 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">551</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InRKYU9mM3BTV2lkL1dicFRvQ3pXMUE9PSIsInZhbHVlIjoibS9OSEhNVmhRaGVMTk8rNmp6ZEJzZzJENDFoSDUxR1NTYUZqSUdOYVZ1elJxZjdoZE9wblJiNXVZcWhmQkVzUmxFekIwYWIvL2RqT29HMUhwZ2VXc2xlMnY4OWRjUlJkcFIzWnZabDZkamJOT01Nd0c3TmJQS1NrOWh4c0ZESHMiLCJtYWMiOiI4ZjAwZTRhNGYwNjMzNzQzODYxNGQ3MzVmOWM5MzFmZGIxNTUwZGY3MjliZDcyOTgyN2NiM2JlMzQ5Yjc3NTBiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjF2SXdpSXdSQXpMMnpKTEhtZHl5cEE9PSIsInZhbHVlIjoiS3laRVE2cW4rZlV0N3lPMmNaZnBPRlZUN2lPUWxKaFRHbGtCYWRDMlBxajFzR0N5aVJRRStHdC9vTFh6T0pXMGZoSGpnb3oyNzNnVGdvdHhSeUg1emx0bVQzU0NxanQ1OXd1d3N5Y2N3K3NTaTlmRVE1L1FZaGt1NTlnSXZ3ZWoiLCJtYWMiOiIxM2IyOWIxZWIyMjA1YTE5N2NjYmU4Mjk0YTZjNWViODRhNTU3Yjc4OWI4Mjg2ODlkZTI5ZDE0ZTJhYjY2YWVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599639513\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-674467804 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxYEiPXdkNcyfymjyeN8wk4RbgrpbsugedMWghRH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674467804\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-246586433 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:43:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246586433\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2146263835 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$GgKaDmjdfWlNzTUrcO1K5.P1hXVA9J4EX3bYW.ogCY4SrBpQeXIn2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146263835\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}