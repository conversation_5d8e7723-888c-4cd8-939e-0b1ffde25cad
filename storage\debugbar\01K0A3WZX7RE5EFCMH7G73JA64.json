{"__meta": {"id": "01K0A3WZX7RE5EFCMH7G73JA64", "datetime": "2025-07-17 00:18:17", "utime": **********.000064, "method": "GET", "uri": "/storage/images/img-slide-2.jpg", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[00:18:16] LOG.info: CartPopup loadCart called: {\n    \"cartItems\": [],\n    \"session_cart\": [],\n    \"total\": 0,\n    \"session_id\": \"o7aJskC2E5szMugpX17af0K1mFmDtpLsi4HSKfjg\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.944304, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752686295.976277, "end": **********.000212, "duration": 1.0239348411560059, "duration_str": "1.02s", "measures": [{"label": "Booting", "start": 1752686295.976277, "relative_start": 0, "end": **********.491305, "relative_end": **********.491305, "duration": 0.****************, "duration_str": "515ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.491333, "relative_start": 0.****************, "end": **********.00022, "relative_end": 8.106231689453125e-06, "duration": 0.****************, "duration_str": "509ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.845968, "relative_start": 0.****************, "end": **********.852157, "relative_end": **********.852157, "duration": 0.006189107894897461, "duration_str": "6.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: errors::403", "start": **********.869588, "relative_start": 0.****************, "end": **********.869588, "relative_end": **********.869588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.layouts.default", "start": **********.870323, "relative_start": 0.****************, "end": **********.870323, "relative_end": **********.870323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.components.product_styles", "start": **********.909243, "relative_start": 0.****************, "end": **********.909243, "relative_end": **********.909243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.header", "start": **********.909642, "relative_start": 0.9333648681640625, "end": **********.909642, "relative_end": **********.909642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.922054, "relative_start": 0.9457769393920898, "end": **********.922054, "relative_end": **********.922054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.936289, "relative_start": 0.9600119590759277, "end": **********.936289, "relative_end": **********.936289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.breadcrumb", "start": **********.937613, "relative_start": 0.9613358974456787, "end": **********.937613, "relative_end": **********.937613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "start": **********.945233, "relative_start": 0.9689559936523438, "end": **********.945233, "relative_end": **********.945233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.footer", "start": **********.951924, "relative_start": 0.97564697265625, "end": **********.951924, "relative_end": **********.951924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.994989, "relative_start": 1.018711805343628, "end": **********.995358, "relative_end": **********.995358, "duration": 0.00036907196044921875, "duration_str": "369μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41021128, "peak_usage_str": "39MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\HttpException", "message": "", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "line": 1426, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1376650533 data-indent-pad=\"  \"><span class=sf-dump-note>array:41</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">abort</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>403</span>\n      <span class=sf-dump-index>1</span> => \"\"\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">abort</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-num>403</span>\n      <span class=sf-dump-index>1</span> => \"\"\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"64 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/ServeFile.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">abort_unless</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-const>false</span>\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>403</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>103</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">__invoke</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\ServeFile</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"22 characters\">images/img-slide-2.jpg</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Filesystem\\FilesystemServiceProvider</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"22 characters\">images/img-slide-2.jpg</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>244</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Routing\\CallableDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>215</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">runCallable</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376650533\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            throw new NotFoundHttpException($message, null, 0, $headers);\n", "        }\n", "\n", "        throw new HttpException($code, $message, null, $headers);\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FApplication.php&line=1426", "ajax": false, "filename": "Application.php", "line": "1426"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "errors::403", "param_count": null, "params": [], "start": **********.869548, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/errors/403.blade.phperrors::403", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ferrors%2F403.blade.php&line=1", "ajax": false, "filename": "403.blade.php", "line": "?"}}, {"name": "templates.auvista.layouts.default", "param_count": null, "params": [], "start": **********.870291, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/layouts/default.blade.phptemplates.auvista.layouts.default", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "templates.auvista.components.product_styles", "param_count": null, "params": [], "start": **********.909216, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/components/product_styles.blade.phptemplates.auvista.components.product_styles", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fcomponents%2Fproduct_styles.blade.php&line=1", "ajax": false, "filename": "product_styles.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.header", "param_count": null, "params": [], "start": **********.909619, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.phptemplates.auvista.blocks.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.922034, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.936263, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.breadcrumb", "param_count": null, "params": [], "start": **********.937589, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/breadcrumb.blade.phptemplates.auvista.blocks.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "param_count": null, "params": [], "start": **********.945199, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.phpH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-popup.blade.php&line=1", "ajax": false, "filename": "cart-popup.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.footer", "param_count": null, "params": [], "start": **********.951896, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.phptemplates.auvista.blocks.footer", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 32, "nb_statements": 32, "nb_visible_statements": 32, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03696, "accumulated_duration_str": "36.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.872366, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 6.142}, {"sql": "select * from `settings` where `name` = 'site_keywords' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_keywords", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.88095, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 6.142, "width_percent": 2.841}, {"sql": "select * from `settings` where `name` = 'site_keywords' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_keywords", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.883112, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 8.983, "width_percent": 3.003}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8860312, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 11.986, "width_percent": 3.68}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8886929, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 15.666, "width_percent": 2.516}, {"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.891222, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 18.182, "width_percent": 3.653}, {"sql": "select * from `settings` where `name` = 'og_image' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["og_image", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.893913, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 21.834, "width_percent": 2.868}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.896101, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 24.702, "width_percent": 2.516}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.898101, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 27.219, "width_percent": 3.111}, {"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.900295, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 30.33, "width_percent": 2.219}, {"sql": "select * from `settings` where `name` = 'og_image' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["og_image", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.902164, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 32.549, "width_percent": 2.246}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.904, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 34.794, "width_percent": 2.679}, {"sql": "select * from `settings` where `name` = 'site_logo' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_logo", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.906516, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 37.473, "width_percent": 2.868}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.911031, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 40.341, "width_percent": 1.218}, {"sql": "select * from `menus` where `location` = 'main-menu' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["main-menu", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.925498, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 41.558, "width_percent": 5.519}, {"sql": "select * from `menu_items` where `menu_id` = 5 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.930049, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 47.078, "width_percent": 3.22}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.932879, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 50.298, "width_percent": 2.976}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.953834, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 53.274, "width_percent": 5.763}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.957353, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 59.037, "width_percent": 2.219}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.959218, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 61.255, "width_percent": 2.408}, {"sql": "select * from `settings` where `name` = 'contact_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.961723, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 63.663, "width_percent": 2.841}, {"sql": "select * from `settings` where `name` = 'office_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["office_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.964019, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 66.504, "width_percent": 2.733}, {"sql": "select * from `settings` where `name` = 'contact_email' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_email", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.966321, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 69.237, "width_percent": 2.435}, {"sql": "select * from `menus` where `location` = 'footer-support' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-support", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.969294, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 71.672, "width_percent": 3.139}, {"sql": "select * from `menu_items` where `menu_id` = 6 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9717038, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 74.811, "width_percent": 3.084}, {"sql": "select * from `menus` where `location` = 'footer-policy' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-policy", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9742339, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 77.895, "width_percent": 3.057}, {"sql": "select * from `menu_items` where `menu_id` = 7 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.9769661, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 80.952, "width_percent": 3.166}, {"sql": "select * from `settings` where `name` = 'facebook_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["facebook_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.979646, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 84.118, "width_percent": 2.679}, {"sql": "select * from `settings` where `name` = 'instagram_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["instagram_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.982403, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 86.797, "width_percent": 5.032}, {"sql": "select * from `settings` where `name` = 'youtube_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["youtube_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.985378, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 91.829, "width_percent": 2.652}, {"sql": "select * from `settings` where `name` = 'site_copyright' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_copyright", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.987513, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 94.481, "width_percent": 2.435}, {"sql": "select * from `settings` where `name` = 'designer_credit' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["designer_credit", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9900818, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 96.916, "width_percent": 3.084}]}, "models": {"data": {"App\\Models\\Setting": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\MenuItem": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}}, "count": 40, "is_counter": true}, "livewire": {"data": {"cart-count #SNPspMv0k3zbVcA1IqWJ": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"SNPspMv0k3zbVcA1IqWJ\"\n]", "cart-count #sUK0JkKAxJwH0fZUJjIL": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"sUK0JkKAxJwH0fZUJjIL\"\n]", "cart-popup #SBikLZ31abIZoQpFAJq1": "array:4 [\n  \"data\" => array:2 [\n    \"cartItems\" => []\n    \"total\" => 0\n  ]\n  \"name\" => \"cart-popup\"\n  \"component\" => \"App\\Livewire\\CartPopup\"\n  \"id\" => \"SBikLZ31abIZoQpFAJq1\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "403 Forbidden", "full_url": "https://auvista.test/storage/images/img-slide-2.jpg", "action_name": "storage.local", "controller_action": "Closure", "uri": "GET storage/{path}", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFilesystem%2FFilesystemServiceProvider.php&line=98\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/laravel/framework/src/Illuminate/Filesystem/FilesystemServiceProvider.php:98-104</a>", "duration": "1.03s", "peak_memory": "44MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://auvista.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZ6cTZYanhlZ3BTaEZDaWxOTVN0b0E9PSIsInZhbHVlIjoiRHMyRG5hVEQwdUhiQnBwNHNFT1g5UlRMbElhN3luL3BrNktIZ2NIa09QYXdOalpTaEo3VkQ4bVlpRndGWEZHVkJuclNoQjFUTXNzUGNqL1VZR3BwbzZiUDNySllxamN2bDQwQlRnL3dWM0hIYXowcFVyV25NeVFaM2xsMFArdnQiLCJtYWMiOiJlOTgxMGZiMmI4ZDQ1ZGU5NGM3MjExODJmNWZjMjk3ODE0YmI1ZTFjN2E2MDlkNTFjODRlNzFjZmUzN2YzYTAxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjRmRGNhV0VrZHltZUFxbUQwOWM4RWc9PSIsInZhbHVlIjoicmJnZDRlR3VGT1V4NmJzVU92Qk5YUWlUTTFSdWZqanpuYVROOGREYTJoYncvSUFNZ3lBaDZ3UTJLNGlDTVU2L1lDaTl2d1lzbmR6RU0vd3ZidlUyc3Zsc0lJY0p6ZGFIV0VpclVCVWluMWFmWUdSZmR3b1JRejllTjJRalN1b2EiLCJtYWMiOiIzMmZjN2VlNTg2NzRjYzdhNGIyMjQyZTQ0NWFlMmY1OTMyNTc5Y2E5Y2I4NTg5YjQwMjU3M2YyOWU1MjkxNzYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1469507028 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZ6cTZYanhlZ3BTaEZDaWxOTVN0b0E9PSIsInZhbHVlIjoiRHMyRG5hVEQwdUhiQnBwNHNFT1g5UlRMbElhN3luL3BrNktIZ2NIa09QYXdOalpTaEo3VkQ4bVlpRndGWEZHVkJuclNoQjFUTXNzUGNqL1VZR3BwbzZiUDNySllxamN2bDQwQlRnL3dWM0hIYXowcFVyV25NeVFaM2xsMFArdnQiLCJtYWMiOiJlOTgxMGZiMmI4ZDQ1ZGU5NGM3MjExODJmNWZjMjk3ODE0YmI1ZTFjN2E2MDlkNTFjODRlNzFjZmUzN2YzYTAxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRmRGNhV0VrZHltZUFxbUQwOWM4RWc9PSIsInZhbHVlIjoicmJnZDRlR3VGT1V4NmJzVU92Qk5YUWlUTTFSdWZqanpuYVROOGREYTJoYncvSUFNZ3lBaDZ3UTJLNGlDTVU2L1lDaTl2d1lzbmR6RU0vd3ZidlUyc3Zsc0lJY0p6ZGFIV0VpclVCVWluMWFmWUdSZmR3b1JRejllTjJRalN1b2EiLCJtYWMiOiIzMmZjN2VlNTg2NzRjYzdhNGIyMjQyZTQ0NWFlMmY1OTMyNTc5Y2E5Y2I4NTg5YjQwMjU3M2YyOWU1MjkxNzYyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469507028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1115981151 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 17:18:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115981151\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2116318737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116318737\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "403 Forbidden", "full_url": "https://auvista.test/storage/images/img-slide-2.jpg", "action_name": "storage.local", "controller_action": "Closure"}, "badge": "403 Forbidden"}}