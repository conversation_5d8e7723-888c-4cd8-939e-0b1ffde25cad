<?php

namespace App\Traits;

trait SeoTrait
{
    /**
     * L<PERSON>y dữ liệu SEO
     * 
     * @param array $data Dữ liệu SEO tùy chỉnh
     * @return array
     */
    protected function getDataSeo(array $data = []): array
    {
        // Xử lý và validate dữ liệu
        $seo_title = isset($data['seo_title']) ? $data['seo_title'] : '';
        $seo_description = isset($data['seo_description']) ? $data['seo_description'] : '';
        $seo_keywords = isset($data['seo_keywords']) ? $data['seo_keywords'] : '';
        $seo_author = isset($data['seo_author']) ? $data['seo_author'] : '';
        $seo_robots = isset($data['seo_robots']) ? $data['seo_robots'] : 'index, follow';
        $seo_canonical = isset($data['seo_canonical']) ? $data['seo_canonical'] : '';
        $seo_image = isset($data['seo_image']) ? getImageUrl($data['seo_image']) : '';
        $seo_type = isset($data['seo_type']) ? $data['seo_type'] : 'website';
        
        // Trả về các biến trực tiếp
        return compact(
            'seo_title',
            'seo_description',
            'seo_keywords', 
            'seo_author',
            'seo_robots',
            'seo_canonical',
            'seo_image',
            'seo_type'
        );
    }
} 