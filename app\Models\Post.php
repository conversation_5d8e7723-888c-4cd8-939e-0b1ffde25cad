<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\Category;

class Post extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'status',
        'author_id',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'image',
        'published_at',
        'trend',
        'is_featured',
        'is_hot',
        'lang'
    ];

    protected $casts = [
        'published_at' => 'datetime',
        'status' => 'string',
        'is_featured' => 'boolean',
    ];

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * L<PERSON>y tất cả danh mục của bài viết
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'post_category')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * L<PERSON>y danh mục chính của bài viết
     */
    public function getPrimaryCategoryAttribute()
    {
        $primaryCategory = $this->categories()->wherePivot('is_primary', true)->first();
        if (!$primaryCategory) {
            $primaryCategory = $this->categories()->first();
        }
        return $primaryCategory;
    }

    /**
     * Đặt danh mục chính cho bài viết
     */
    public function setPrimaryCategory(int $categoryId): void
    {
        // Debug
        \Log::info('Setting Primary Category', [
            'post_id' => $this->id,
            'category_id' => $categoryId
        ]);
        
        try {
            // Reset tất cả danh mục thành không phải primary
            \DB::table('post_category')
                ->where('post_id', $this->id)
                ->update(['is_primary' => false]);
            
            // Đặt danh mục được chọn thành primary
            $updated = \DB::table('post_category')
                ->where('post_id', $this->id)
                ->where('category_id', $categoryId)
                ->update(['is_primary' => true]);
            
            // Debug
            \Log::info('Primary Category Updated', [
                'post_id' => $this->id,
                'category_id' => $categoryId,
                'updated' => $updated
            ]);
            
            // Nếu không có bản ghi nào được cập nhật, có thể danh mục chưa được sync
            if ($updated === 0) {
                // Thêm danh mục vào bài viết và đặt là primary
                \DB::table('post_category')->insert([
                    'post_id' => $this->id,
                    'category_id' => $categoryId,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                // Debug
                \Log::info('Primary Category Inserted', [
                    'post_id' => $this->id,
                    'category_id' => $categoryId
                ]);
            }
        } catch (\Exception $e) {
            // Debug
            \Log::error('Error Setting Primary Category', [
                'post_id' => $this->id,
                'category_id' => $categoryId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Lấy ID của danh mục chính
     */
    public function getPrimaryCategoryId(): ?int
    {
        try {
            $primaryCategory = \DB::table('post_category')
                ->where('post_id', $this->id)
                ->where('is_primary', true)
                ->first();
                
            $result = $primaryCategory ? $primaryCategory->category_id : null;
            
            // Debug
            \Log::info('Get Primary Category ID', [
                'post_id' => $this->id,
                'primary_category_id' => $result
            ]);
            
            return $result;
        } catch (\Exception $e) {
            // Debug
            \Log::error('Error Getting Primary Category ID', [
                'post_id' => $this->id,
                'error' => $e->getMessage()
            ]);
            
            return null;
        }
    }

    /**
     * Lấy tên của danh mục chính
     */
    public function getPrimaryCategoryNameAttribute(): ?string
    {
        $primaryCategoryId = $this->getPrimaryCategoryId();
        if (!$primaryCategoryId) {
            return null;
        }
        
        $category = Category::find($primaryCategoryId);
        return $category ? $category->name : null;
    }
}
