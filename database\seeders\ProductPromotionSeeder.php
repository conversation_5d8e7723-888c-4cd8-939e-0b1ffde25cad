<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductPromotion;
use App\Models\Product;
use Illuminate\Support\Facades\DB;

class ProductPromotionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Xóa dữ liệu cũ
        DB::table('product_promotion_pivot')->delete();
        ProductPromotion::query()->delete();

        // Tạo dữ liệu khuyến mãi mẫu
        $promotions = [
            [
                'title' => 'Khuyến mãi mùa hè 2025',
                'start_date' => '2025-04-01',
                'end_date' => '2025-05-31',
                'promotion_items' => [
                    'Tặng Máy vắt cam Rapido (Hết quà hoàn tiền 160.000₫)',
                    'Tặng Phiếu mua hàng nội cơm trị giá 100.000₫',
                    'Tặng Phiếu mua hàng quạt điện trị giá 100.000₫',
                    'Phiếu mua hàng máy lọc nước trị giá 300.000₫',
                    'Miễn phí công lắp đặt'
                ],
                'locations' => ['Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng'],
                'is_active' => true
            ],
            [
                'title' => 'Khuyến mãi đặc biệt Nutrilite',
                'start_date' => '2025-04-01',
                'end_date' => '2025-04-30',
                'promotion_items' => [
                    'Tặng túi du lịch cao cấp trị giá 450.000₫',
                    'Tặng 1 hộp vitamin C trị giá 200.000₫',
                    'Tặng gói kiểm tra sức khỏe tổng quát trị giá 500.000₫',
                    'Miễn phí vận chuyển toàn quốc'
                ],
                'locations' => ['Toàn quốc'],
                'is_active' => true
            ],
            [
                'title' => 'Khuyến mãi Artistry',
                'start_date' => '2025-04-01',
                'end_date' => '2025-05-15',
                'promotion_items' => [
                    'Tặng bộ dưỡng da mini trị giá 350.000₫',
                    'Tặng túi trang điểm cao cấp trị giá 250.000₫',
                    'Tặng voucher spa trị giá 500.000₫'
                ],
                'locations' => ['Hồ Chí Minh', 'Hà Nội', 'Cần Thơ'],
                'is_active' => true
            ]
        ];

        // Tạo promotions và kết nối với sản phẩm
        foreach ($promotions as $index => $promotionData) {
            $promotion = ProductPromotion::create([
                'title' => $promotionData['title'],
                'start_date' => $promotionData['start_date'],
                'end_date' => $promotionData['end_date'],
                'promotion_items' => $promotionData['promotion_items'],
                'locations' => $promotionData['locations'],
                'is_active' => $promotionData['is_active']
            ]);

            // Kết nối promotion với các sản phẩm
            if ($index === 0) { // Khuyến mãi mùa hè - áp dụng cho sản phẩm máy lọc
                $locNuocCategoryId = DB::table('categories')->where('slug', 'loc-nuoc')->first()->id ?? null;
                $locKhongKhiCategoryId = DB::table('categories')->where('slug', 'loc-khong-khi')->first()->id ?? null;
                
                if ($locNuocCategoryId || $locKhongKhiCategoryId) {
                    $categoryIds = [];
                    if ($locNuocCategoryId) $categoryIds[] = $locNuocCategoryId;
                    if ($locKhongKhiCategoryId) $categoryIds[] = $locKhongKhiCategoryId;
                    
                    $products = Product::whereIn('category_id', $categoryIds)->get();
                    $promotion->products()->attach($products->pluck('id'));
                }
            } elseif ($index === 1) { // Khuyến mãi Nutrilite
                $nutritionBrandId = DB::table('brands')->where('slug', 'nutrilite')->first()->id ?? null;
                
                if ($nutritionBrandId) {
                    $products = Product::where('brand_id', $nutritionBrandId)->limit(5)->get();
                    $promotion->products()->attach($products->pluck('id'));
                }
            } elseif ($index === 2) { // Khuyến mãi Artistry
                $artistryBrandId = DB::table('brands')->where('slug', 'artistry')->first()->id ?? null;
                
                if ($artistryBrandId) {
                    $products = Product::where('brand_id', $artistryBrandId)->limit(5)->get();
                    $promotion->products()->attach($products->pluck('id'));
                }
            }
        }

        // Áp dụng khuyến mãi cho 2 sản phẩm đặc biệt
        $nutriliteDoubleX = Product::where('slug', 'nutrilite-double-x')->first();
        if ($nutriliteDoubleX) {
            $promotion = ProductPromotion::where('title', 'Khuyến mãi đặc biệt Nutrilite')->first();
            if ($promotion && !$nutriliteDoubleX->promotions->contains($promotion->id)) {
                $nutriliteDoubleX->promotions()->attach($promotion->id);
            }
        }

        $artistrySupreme = Product::where('slug', 'artistry-supreme-lx')->first();
        if ($artistrySupreme) {
            $promotion = ProductPromotion::where('title', 'Khuyến mãi Artistry')->first();
            if ($promotion && !$artistrySupreme->promotions->contains($promotion->id)) {
                $artistrySupreme->promotions()->attach($promotion->id);
            }
        }
    }
}
