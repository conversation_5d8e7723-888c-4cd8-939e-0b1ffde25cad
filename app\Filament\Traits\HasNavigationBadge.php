<?php

namespace App\Filament\Traits;

trait HasNavigationBadge
{
    /**
     * Hiển thị số lượng bản ghi trong navigation
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
    
    /**
     * Định dạng màu sắc cho badge dựa trên số lượng bản ghi
     */
    public static function getNavigationBadgeColor(): ?string
    {
        $count = static::getModel()::count();
        
        // Tùy chỉnh màu sắc dựa trên số lượng
        return match (true) {
            $count > 100 => 'warning',
            $count > 50 => 'success',
            $count > 10 => 'primary',
            default => 'gray',
        };
    }
    
    /**
     * Phương thức này được gọi để làm mới badge (cần thiết cho Filament v3)
     */
    public static function refreshNavigationBadge(): void
    {
        // Trong Filament v3, thông thường không cần làm gì
        // Chức năng này chỉ để tương thích với code
    }
} 