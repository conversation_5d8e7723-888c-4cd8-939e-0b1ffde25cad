<?php

namespace App\Filament\Widgets;

use App\Models\Order;
use App\Models\Post;
use App\Models\Product;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class TreatmentsChart extends ChartWidget
{
    protected static ?string $heading = 'Biểu đồ hoạt động';
    protected static ?int $sort = 5;

    protected function getData(): array
    {
        // Dữ liệu đơn hàng theo ngày trong 30 ngày qua
        $orders = [];
        $posts = [];
        $products = [];
        $labels = [];
        
        // Lấy 12 ngày gần nhất
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $labels[] = $date->format('d/m');
            
            // Đơn hàng
            $orders[] = Order::whereDate('created_at', $date)->count();
            
            // Bài viết
            $posts[] = Post::whereDate('created_at', $date)->count();
            
            // Sản phẩm
            $products[] = Product::whereDate('created_at', $date)->count();
        }

        return [
            'datasets' => [
                [
                    'label' => 'Đơn hàng',
                    'data' => $orders,
                    'fill' => true,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgb(54, 162, 235)',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Bài viết',
                    'data' => $posts,
                    'fill' => true,
                    'backgroundColor' => 'rgba(153, 102, 255, 0.2)',
                    'borderColor' => 'rgb(153, 102, 255)',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Sản phẩm',
                    'data' => $products,
                    'fill' => true,
                    'backgroundColor' => 'rgba(255, 159, 64, 0.2)',
                    'borderColor' => 'rgb(255, 159, 64)',
                    'tension' => 0.3,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
        ];
    }
}
