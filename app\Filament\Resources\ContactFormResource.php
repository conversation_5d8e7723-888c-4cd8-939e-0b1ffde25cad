<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactFormResource\Pages;
use App\Filament\Resources\ContactFormResource\RelationManagers;
use App\Models\ContactForm;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;

class ContactFormResource extends Resource
{
    protected static ?string $model = ContactForm::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';
    protected static ?string $navigationGroup = 'Quản lý nội dung';
    protected static ?string $navigationLabel = 'Form liên hệ';
    protected static ?int $navigationSort = 10;
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin liên hệ')
                    ->schema([
                        Forms\Components\TextInput::make('member_id')
                            ->label('Mã số NPP/ABO ID')
                            ->disabled(),
                        Forms\Components\TextInput::make('name')
                            ->label('Họ và tên')
                            ->disabled(),
                        Forms\Components\TextInput::make('spouse_name')
                            ->label('Tên vợ/chồng')
                            ->disabled(),
                        Forms\Components\TextInput::make('phone')
                            ->label('Số điện thoại')
                            ->disabled(),
                        Forms\Components\TextInput::make('email')
                            ->disabled(),
                        Forms\Components\TextInput::make('address')
                            ->label('Địa chỉ')
                            ->disabled(),
                        Forms\Components\TextInput::make('subject')
                            ->label('Chủ đề')
                            ->disabled(),
                        Forms\Components\Textarea::make('message')
                            ->label('Nội dung')
                            ->disabled()
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('file_attachment')
                            ->label('Tệp đính kèm')
                            ->disabled()
                            ->visibility('public')
                            ->columnSpanFull(),
                        Forms\Components\Toggle::make('privacy_consent')
                            ->label('Đồng ý chính sách bảo mật')
                            ->disabled(),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'new' => 'Mới',
                                'processing' => 'Đang xử lý',
                                'completed' => 'Đã hoàn thành',
                                'cancelled' => 'Đã hủy',
                            ])
                            ->default('new')
                            ->required(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('member_id')
                    ->label('Mã NPP')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Họ và tên')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('address')
                    ->label('Địa chỉ')
                    ->searchable()
                    ->limit(30),
                TextColumn::make('phone')
                    ->label('Số điện thoại')
                    ->searchable(),
                TextColumn::make('subject')
                    ->label('Chủ đề')
                    ->searchable()
                    ->limit(30),
                TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'new' => 'danger',
                        'processing' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'new' => 'Mới',
                        'processing' => 'Đang xử lý',
                        'completed' => 'Đã hoàn thành',
                        'cancelled' => 'Đã hủy',
                        default => $state,
                    }),
                TextColumn::make('created_at')
                    ->label('Ngày gửi')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'new' => 'Mới',
                        'processing' => 'Đang xử lý',
                        'completed' => 'Đã hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalWidth('xl')
                    ->modalHeading(fn (ContactForm $record) => "Xem chi tiết: {$record->name}"),
                Tables\Actions\Action::make('markAsProcessing')
                    ->label('Đánh dấu đang xử lý')
                    ->icon('heroicon-o-clock')
                    ->color('warning')
                    ->action(function (ContactForm $record) {
                        $record->update(['status' => 'processing']);
                        
                        Notification::make()
                            ->title('Thành công')
                            ->body('Đã đánh dấu liên hệ này là đang xử lý.')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ContactForm $record): bool => $record->status === 'new'),
                Tables\Actions\Action::make('markAsCompleted')
                    ->label('Đánh dấu đã hoàn thành')
                    ->icon('heroicon-o-check')
                    ->color('success')
                    ->action(function (ContactForm $record) {
                        $record->update(['status' => 'completed']);
                        
                        Notification::make()
                            ->title('Thành công')
                            ->body('Đã đánh dấu liên hệ này là đã hoàn thành.')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ContactForm $record): bool => $record->status !== 'completed' && $record->status !== 'cancelled'),
                Tables\Actions\Action::make('markAsCancelled')
                    ->label('Đánh dấu đã hủy')
                    ->icon('heroicon-o-x-mark')
                    ->color('danger')
                    ->action(function (ContactForm $record) {
                        $record->update(['status' => 'cancelled']);
                        
                        Notification::make()
                            ->title('Thành công')
                            ->body('Đã đánh dấu liên hệ này là đã hủy.')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ContactForm $record): bool => $record->status !== 'cancelled'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('bulkMarkAsProcessing')
                        ->label('Đánh dấu đang xử lý')
                        ->icon('heroicon-o-clock')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['status' => 'processing']);
                            });
                            
                            Notification::make()
                                ->title('Thành công')
                                ->body('Đã đánh dấu các liên hệ đã chọn là đang xử lý.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->emptyStateHeading('Chưa có liên hệ nào')
            ->emptyStateDescription('Liên hệ mới sẽ xuất hiện ở đây sau khi khách hàng gửi form từ website.')
            ->emptyStateIcon('heroicon-o-envelope');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContactForms::route('/'),
            'view' => Pages\ViewContactForm::route('/{record}'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'new')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'danger';
    }
}
