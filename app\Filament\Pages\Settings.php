<?php

namespace App\Filament\Pages;

use App\Models\Setting;
use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components;
use Filament\Forms\Components\RichEditor;
use Filament\Notifications\Notification;
use Filament\Forms\Concerns\InteractsWithForms;
use Awcodes\FilamentPluginFormCodeField\Components\CodeField;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\Select;

class Settings extends Page
{
    use InteractsWithForms;

    protected static ?string $navigationGroup = 'Cài đặt hệ thống';
    protected static ?int $navigationGroupSort = 9999; // Add this line for group sorting
    protected static ?string $navigationLabel = 'Cài đặt chung';
    protected static ?int $navigationSort = 9999;
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static string $view = 'filament.pages.settings';

    // Define field arrays as class properties
    protected array $textareaFields = [
        'site_description',
        'meta_description',
        'footer_text',
        'about_content',
        'seo_description',
        'privacy_policy',
        'terms_conditions'
    ];

    protected array $codeFields = [
        'custom_css',
        'header_js',
        'footer_js',
    ];

    protected array $imageFields = [
        'site_logo',
        'favicon',
        'banner_image',
        'footer_logo',
        'default_image',
    ];

    protected array $documentFields = [
        'company_profile',
        'terms_pdf',
        'privacy_pdf',
    ];

    public ?array $data = [];
    public ?string $lang = 'vi';

    public function mount(): void
    {
        $this->lang = request()->query('lang', 'vi');
        $this->loadSettings();
    }
    
    protected function loadSettings(): void
    {
        $settings = Setting::where('lang', $this->lang)->get();
        $this->data = [];

        foreach ($settings as $setting) {
            $value = $setting->value;

            if (in_array($setting->name, $this->imageFields)) {
                $value = $value ? [$value] : [];
            } elseif ($setting->type === 'json') {
                $value = is_array($value) ? $value : json_decode($value, true);
            } elseif ($setting->type === 'boolean') {
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
            }

            $this->data[$setting->name] = $value;
        }

        $this->form->fill($this->data);
    }
    
    public function updatedLang($value): void
    {
        $this->lang = $value;
        $this->loadSettings();
    }

    public function form(Form $form): Form
    {
        $settings = Setting::where('lang', $this->lang)->get();
        $groups = $settings->groupBy('group');

        return $form
            ->schema([
                Select::make('lang')
                    ->label('Ngôn ngữ')
                    ->options([
                        'vi' => 'Tiếng Việt',
                        'en' => 'Tiếng Anh',
                    ])
                    ->default($this->lang)
                    ->reactive()
                    ->afterStateUpdated(fn ($state) => $this->updatedLang($state)),
                Components\Tabs::make('Settings')
                    ->tabs(
                        collect($groups)->map(function ($settings, $group) {
                            return Components\Tabs\Tab::make($group)
                                ->schema(
                                    $settings->map(function ($setting) {
                                        // Check if field is for image or document
                                        if (in_array($setting->name, $this->imageFields)) {
                                            return Components\FileUpload::make($setting->name)
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description)
                                                ->image()
                                                ->imageEditor()
                                                ->directory('settings/images')
                                                ->preserveFilenames()
                                                ->maxSize(5120) // 5MB
                                                ->columnSpanFull()
                                                ->downloadable()
                                                ->openable()
                                                ->disk('public') // Specify public disk
                                                ->visibility('public')
                                                ->maxFiles(1)
                                                ->reorderable(false)
                                                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml']);
                                        }

                                        if (in_array($setting->name, $this->documentFields)) {
                                            return Components\FileUpload::make($setting->name)
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description)
                                                ->acceptedFileTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                                                ->directory('settings/documents')
                                                ->preserveFilenames()
                                                ->maxSize(10240) // 10MB
                                                ->columnSpanFull()
                                                ->downloadable()
                                                ->openable();
                                        }

                                        // Regular field types
                                        return match ($setting->type) {
                                            'string' => in_array($setting->name, $this->textareaFields) 
                                                ? RichEditor::make($setting->name)
                                                    ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                    ->helperText($setting->description)
                                                    ->toolbarButtons([
                                                        'bold',
                                                        'bulletList',
                                                        'h2',
                                                        'h3',
                                                        'italic',
                                                        'link',
                                                        'orderedList',
                                                        'redo',
                                                        'strike',
                                                        'undo',
                                                    ])
                                                    ->columnSpanFull()
                                                : (in_array($setting->name, $this->codeFields)
                                                    ? Components\Textarea::make($setting->name)
                                                        ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                        ->helperText($setting->description)
                                                        ->rows(15)
                                                        ->columnSpanFull()
                                                        ->fontFamily('monospace')
                                                    : Components\TextInput::make($setting->name)
                                                        ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                        ->helperText($setting->description)),
                                            
                                            'number' => Components\TextInput::make($setting->name)
                                                ->numeric()
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description),
                                            
                                            'boolean' => Components\Toggle::make($setting->name)
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description),
                                            
                                            'json' => Components\Textarea::make($setting->name)
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description . ' (Enter valid JSON)')
                                                ->rows(6)
                                                ->columnSpanFull()
                                                ->formatStateUsing(fn ($state) => is_array($state) ? json_encode($state, JSON_PRETTY_PRINT) : $state)
                                                ->dehydrateStateUsing(fn ($state) => is_string($state) ? json_decode($state, true) : $state),
                                            
                                            'code' => Components\Textarea::make($setting->name)
                                                ->label(ucwords(str_replace('_', ' ', $setting->name)))
                                                ->helperText($setting->description)
                                                ->rows(10)
                                                ->columnSpanFull(),
                                            
                                            default => Components\TextInput::make($setting->name),
                                        };
                                    })->toArray()
                                );
                        })->toArray()
                    )
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $settings = Setting::where('lang', $this->lang)->get();
        
        foreach ($settings as $setting) {
            $value = $this->data[$setting->name] ?? null;
            
            // Handle image uploads
            if (in_array($setting->name, $this->imageFields)) {
                if (!empty($value)) {
                    // Get the temporary upload instance
                    $tempFile = collect($value)->first();
                    
                    if ($tempFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                        // Store file in public disk under settings/images
                        $path = $tempFile->store('settings/images', 'public');
                        $value = $path; // Store the relative path
                    } else {
                        // If not a new upload, keep existing path
                        $value = is_array($value) ? array_values($value)[0] : $value;
                    }

                } else {
                    // If value is empty, keep existing value
                    continue;
                }
            }
            
            // Handle JSON data
            if ($setting->type === 'json') {
                $value = is_string($value) ? $value : json_encode($value);
            }
            
            $setting->update(['value' => $value]);
        }

        Notification::make()
            ->success()
            ->title('Settings saved successfully')
            ->send();
            
        $this->redirect(static::getUrl(['lang' => $this->lang]));
    }
}
