<?php

namespace App\Livewire;

use Livewire\Component;
use App\Services\LanguageSyncService;
use Livewire\Attributes\On;

class LanguageSwitcher extends Component
{
    public function mount()
    {
        // Đảm bảo locale được set đúng khi component mount
        $locale = LanguageSyncService::getCurrentLocale();
        app()->setLocale($locale);
    }

    #[On('filament-filter-changed')]
    public function onFilamentFilterChanged($locale)
    {
        // Khi filter trong Filament thay đổi, cập nhật Language Switcher
        LanguageSyncService::syncLocale($locale, 'filter');
        $this->dispatch('$refresh');
    }

    public function getCurrentLocaleProperty()
    {
        // Computed property để luôn lấy locale mới nhất
        return LanguageSyncService::getCurrentLocale();
    }

    public function getLanguagesProperty()
    {
        return [
            'vi' => [
                'name' => 'Tiếng Việt',
                'flag' => '🇻🇳',
                'code' => 'VI'
            ],
            'en' => [
                'name' => 'English',
                'flag' => '🇺🇸',
                'code' => 'EN'
            ]
        ];
    }

    public function render()
    {
        return view('livewire.language-switcher');
    }
}
