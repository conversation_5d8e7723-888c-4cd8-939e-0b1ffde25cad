<?php
namespace App\Filament\Resources;

use App\Filament\Resources\StaticPageResource\Pages;
use App\Models\StaticPage;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables\Table;
use Filament\Tables;
use Illuminate\Support\Str;
use Filament\Forms\Set;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Filters\SelectFilter;
use App\Traits\HasLanguageSync;

class StaticPageResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = StaticPage::class;
    protected static ?string $navigationIcon = 'heroicon-o-document';
    protected static ?string $navigationGroup = 'Quản lý nội dung';
    protected static ?int $navigationGroupSort = 2;
    protected static ?string $navigationLabel = 'Trang tĩnh';
    protected static ?int $navigationSort = 2;
    protected static bool $shouldRegisterNavigation = true;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin trang')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->label('Tiêu đề')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Set $set, ?string $state, ?Model $record) {
                                if ($record && $record->id === 1) {
                                    return;
                                }
                                $set('slug', Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->label('Slug')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->disabled(fn (?Model $record): bool => $record?->id === 1)
                            ->dehydrated(fn (?Model $record): bool => $record?->id !== 1)
                            ->helperText('Slug sẽ được sử dụng trong URL'),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'draft' => 'Bản nháp',
                                'published' => 'Đã xuất bản',
                                'archived' => 'Đã lưu trữ',
                            ])
                            ->default('draft')
                            ->required(),
                        Forms\Components\Select::make('lang')
                            ->label('Ngôn ngữ')
                            ->options([
                                'vi' => 'Tiếng Việt',
                                'en' => 'Tiếng Anh',
                            ])
                            ->default('vi')
                            ->required(),
                        Forms\Components\FileUpload::make('image')
                            ->label('Ảnh đại diện')
                            ->image()
                            ->directory('pages')
                            ->preserveFilenames()
                            ->imageEditor()
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('excerpt')
                            ->label('Tóm tắt')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\RichEditor::make('content')
                            ->label('Nội dung')
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\Hidden::make('published_at')
                            ->default(now())
                            ->dehydrated(true),
                        Forms\Components\Hidden::make('author_id')
                            ->default(1)
                            ->dehydrated(true),
                    ])->columnSpan(['lg' => 2]),

                Forms\Components\Section::make('Thông tin SEO')
                    ->schema([
                        Forms\Components\TextInput::make('seo_title')
                            ->label('Tiêu đề SEO')
                            ->placeholder('Nhập tiêu đề SEO'),
                        Forms\Components\Textarea::make('seo_description')
                            ->label('Mô tả SEO')
                            ->placeholder('Nhập mô tả SEO')
                            ->rows(3),
                        Forms\Components\TextInput::make('seo_keywords')
                            ->label('Từ khóa SEO')
                            ->placeholder('Nhập từ khóa SEO'),
                    ])->columnSpan(['lg' => 1]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('Ảnh')
                    ->disk('public')
                    ->square()
                    ->size(60)
                    ->visibility('public')
                    ->getStateUsing(function ($record) {
                        if ($record->image && Storage::disk('public')->exists($record->image)) {
                            return $record->image;
                        }
                        return null;
                    })
                    ->defaultImageUrl(asset(Config::get('filament-media.defaults.placeholder_image.url'))),
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('author.name')
                    ->label('Tác giả')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'success' => 'published',
                        'warning' => 'draft',
                        'danger' => 'archived',
                    ])
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'published' => 'Đã xuất bản',
                        'draft' => 'Bản nháp',
                        'archived' => 'Đã lưu trữ',
                        default => $state,
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('lang')
                    ->label('Ngôn ngữ')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'vi' => 'Tiếng Việt',
                        'en' => 'Tiếng Anh',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('published_at')
                    ->label('Ngày xuất bản')
                    ->dateTime(Config::get('app.displayFormat', 'd/m/Y H:i'))
                    ->sortable(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'draft' => 'Bản nháp',
                        'published' => 'Đã xuất bản',
                        'archived' => 'Đã lưu trữ',
                    ]),
            ])
            ->actions([
                ViewAction::make()
                    ->modalWidth('7xl')
                    ->label('Xem'),
                EditAction::make()
                    ->modalWidth('7xl')
                    ->label('Sửa'),
                DeleteAction::make()
                    ->label('Xóa')
                    ->hidden(fn (Model $record): bool => $record->id === 1)
                    ->before(function (Model $record) {
                        if ($record->id === 1) {
                            Notification::make()
                                ->danger()
                                ->title('Không thể xóa trang chủ')
                                ->body('Trang chủ không thể bị xóa.')
                                ->send();
                            
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('copy_to_language')
                        ->label('Copy sang ngôn ngữ khác...')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                if ($record->id === 1) { // Assuming home page cannot be copied this way
                                    continue;
                                }
                                $newRecord = $record->replicate();
                                $newRecord->lang = $data['target_lang'];
                                $newRecord->slug = $record->slug . '-' . $data['target_lang'];
                                $newRecord->save();
                            }

                            Notification::make()
                                ->title('Đã copy ' . count($records->where('id', '!=', 1)) . ' bản ghi')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Chọn ngôn ngữ đích')
                                ->options([
                                    'vi' => 'Tiếng Việt',
                                    'en' => 'Tiếng Anh',
                                ])
                                ->required(),
                        ]),
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Xóa đã chọn')
                        ->before(function (Collection $records) {
                            if ($records->contains('id', 1)) {
                                Notification::make()
                                    ->warning()
                                    ->title('Không thể xóa trang chủ')
                                    ->body('Trang chủ không thể bị xóa.')
                                    ->send();
                                
                                return $records->filter(fn ($record) => $record->id !== 1);
                            }
                        }),
                ]),
            ])
            ->headerActions([
                CreateAction::make()
                    ->modalWidth('7xl')
                    ->label('Tạo trang mới'),
            ])
            ->emptyStateActions([
                CreateAction::make()
                    ->modalWidth('7xl')
                    ->label('Tạo trang mới'),
            ])
            ->recordAction(null);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStaticPages::route('/'),
            'create' => Pages\CreateStaticPage::route('/create'),
            'edit' => Pages\EditStaticPage::route('/{record}/edit'),
            'view' => Pages\ViewStaticPage::route('/{record}'),
        ];
    }
} 