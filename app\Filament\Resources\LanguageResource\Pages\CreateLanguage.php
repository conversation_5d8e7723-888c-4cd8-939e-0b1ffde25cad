<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use App\Services\LanguageFileService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateLanguage extends CreateRecord
{
    protected static string $resource = LanguageResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $service = new LanguageFileService();
        $locale = $data['locale'];
        $file = $data['file'];
        $translations = $data['translations'] ?? [];
        
        try {
            $result = $service->createLanguageFile($locale, $file, $translations);
            
            if ($result === false) {
                Notification::make()
                    ->danger()
                    ->title('Lỗi')
                    ->body('File đã tồn tại.')
                    ->send();
                
                $this->halt();
            }
            
            Notification::make()
                ->success()
                ->title('Thành công')
                ->body('Đã tạo file ngôn ngữ mới.')
                ->send();
            
            return [
                'id' => $locale . '_' . $file,
                'locale' => $locale,
                'file' => $file,
                'keys_count' => count($translations),
                'last_modified' => time(),
                'translations' => $translations,
            ];
        } catch (\InvalidArgumentException $e) {
            Notification::make()
                ->danger()
                ->title('Lỗi')
                ->body($e->getMessage())
                ->send();
            
            $this->halt();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Tạo file ngôn ngữ mới';
    }
} 