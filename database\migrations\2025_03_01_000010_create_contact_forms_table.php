<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_forms', function (Blueprint $table) {
            $table->id();
            $table->string('member_id')->nullable()->comment('Mã số NPP/ABO ID');
            $table->string('name');
            $table->string('spouse_name')->nullable()->comment('Tên vợ/chồng');
            $table->string('phone');
            $table->string('email');
            $table->string('address')->nullable()->comment('Địa chỉ');
            $table->string('subject')->default('Không xác định');
            $table->text('message');
            $table->string('file_attachment')->nullable()->comment('Đường dẫn tệp đ<PERSON>h kèm');
            $table->boolean('privacy_consent')->default(false)->comment('Đồng ý chính sách bảo mật');
            $table->boolean('is_newsletter')->default(false)->comment('Đăng ký nhận bản tin');
            $table->string('status')->default('new');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_forms');
    }
};
