<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('log_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->nullOnDelete();
            $table->string('model_type'); // Model bị tác động
            $table->unsignedBigInteger('model_id'); // ID của bản ghi bị tác động
            $table->string('action'); // Hành động: created, updated, deleted
            $table->json('old_data')->nullable(); // Dữ liệu cũ
            $table->json('new_data')->nullable(); // Dữ liệu mới
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('log_actions');
    }
};
