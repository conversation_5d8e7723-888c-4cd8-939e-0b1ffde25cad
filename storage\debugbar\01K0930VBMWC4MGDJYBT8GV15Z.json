{"__meta": {"id": "01K0930VBMWC4MGDJYBT8GV15Z", "datetime": "2025-07-16 14:43:40", "utime": *********0.406262, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752651817.743789, "end": *********0.406408, "duration": 2.662619113922119, "duration_str": "2.66s", "measures": [{"label": "Booting", "start": 1752651817.743789, "relative_start": 0, "end": **********.223492, "relative_end": **********.223492, "duration": 0.****************, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.223502, "relative_start": 0.*****************, "end": *********0.406419, "relative_end": 1.0967254638671875e-05, "duration": 2.****************, "duration_str": "2.18s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.463924, "relative_start": 0.***************, "end": **********.465982, "relative_end": **********.465982, "duration": 0.0020580291748046875, "duration_str": "2.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.icon", "start": *********0.313903, "relative_start": 2.****************, "end": *********0.313903, "relative_end": *********0.313903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.340287, "relative_start": 2.****************, "end": *********0.340287, "relative_end": *********0.340287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.35476, "relative_start": 2.***************, "end": *********0.35476, "relative_end": *********0.35476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.357561, "relative_start": 2.61377215385437, "end": *********0.357561, "relative_end": *********0.357561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.371719, "relative_start": 2.627929925918579, "end": *********0.371719, "relative_end": *********0.371719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.374446, "relative_start": 2.6306569576263428, "end": *********0.374446, "relative_end": *********0.374446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": *********0.375258, "relative_start": 2.6314690113067627, "end": *********0.375258, "relative_end": *********0.375258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": *********0.393731, "relative_start": 2.64994215965271, "end": *********0.393731, "relative_end": *********0.393731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": *********0.395319, "relative_start": 2.6515300273895264, "end": *********0.398592, "relative_end": *********0.398592, "duration": 0.00327301025390625, "duration_str": "3.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41431352, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.313867, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.34026, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.354725, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.35751, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.371657, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.374425, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": *********0.375237, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": *********0.393678, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01976, "accumulated_duration_str": "19.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.491159, "duration": 0.00318, "duration_str": "3.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 16.093}, {"sql": "select count(*) as aggregate from `orders`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.766376, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:20", "source": {"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=20", "ajax": false, "filename": "StatsOverview.php", "line": "20"}, "connection": "auvista", "explain": null, "start_percent": 16.093, "width_percent": 11.741}, {"sql": "select count(*) as aggregate from `orders` where month(`created_at`) = '07' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7698262, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:25", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=25", "ajax": false, "filename": "StatsOverview.php", "line": "25"}, "connection": "auvista", "explain": null, "start_percent": 27.834, "width_percent": 9.464}, {"sql": "select count(*) as aggregate from `orders` where month(`created_at`) = '06' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 29}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.773238, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:29", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=29", "ajax": false, "filename": "StatsOverview.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 37.298, "width_percent": 5.415}, {"sql": "select sum(`total_amount`) as aggregate from `orders` where `status` != 'cancelled' and `payment_status` = 'paid'", "type": "query", "params": [], "bindings": ["cancelled", "paid"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.775708, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:44", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=44", "ajax": false, "filename": "StatsOverview.php", "line": "44"}, "connection": "auvista", "explain": null, "start_percent": 42.713, "width_percent": 5.364}, {"sql": "select sum(`total_amount`) as aggregate from `orders` where `status` != 'cancelled' and `payment_status` = 'paid' and month(`created_at`) = '07' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["cancelled", "paid", "07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 50}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.77797, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:50", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=50", "ajax": false, "filename": "StatsOverview.php", "line": "50"}, "connection": "auvista", "explain": null, "start_percent": 48.077, "width_percent": 4.099}, {"sql": "select sum(`total_amount`) as aggregate from `orders` where `status` != 'cancelled' and `payment_status` = 'paid' and month(`created_at`) = '06' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["cancelled", "paid", "06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7802, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=56", "ajax": false, "filename": "StatsOverview.php", "line": "56"}, "connection": "auvista", "explain": null, "start_percent": 52.176, "width_percent": 6.63}, {"sql": "select count(*) as aggregate from `products`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 69}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.783558, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:69", "source": {"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=69", "ajax": false, "filename": "StatsOverview.php", "line": "69"}, "connection": "auvista", "explain": null, "start_percent": 58.806, "width_percent": 5.415}, {"sql": "select count(*) as aggregate from `products` where month(`created_at`) = '07' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 73}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.785531, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:73", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=73", "ajax": false, "filename": "StatsOverview.php", "line": "73"}, "connection": "auvista", "explain": null, "start_percent": 64.221, "width_percent": 5.516}, {"sql": "select count(*) as aggregate from `products` where month(`created_at`) = '06' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 77}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.7882888, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:77", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 77}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=77", "ajax": false, "filename": "StatsOverview.php", "line": "77"}, "connection": "auvista", "explain": null, "start_percent": 69.737, "width_percent": 7.338}, {"sql": "select count(*) as aggregate from `posts`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.791454, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:90", "source": {"index": 19, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=90", "ajax": false, "filename": "StatsOverview.php", "line": "90"}, "connection": "auvista", "explain": null, "start_percent": 77.075, "width_percent": 15.233}, {"sql": "select count(*) as aggregate from `posts` where month(`created_at`) = '07' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["07", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 94}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.796085, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:94", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 94}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=94", "ajax": false, "filename": "StatsOverview.php", "line": "94"}, "connection": "auvista", "explain": null, "start_percent": 92.308, "width_percent": 4.808}, {"sql": "select count(*) as aggregate from `posts` where month(`created_at`) = '06' and year(`created_at`) = 2025", "type": "query", "params": [], "bindings": ["06", 2025], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.798006, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "StatsOverview.php:98", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/StatsOverview.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\StatsOverview.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FStatsOverview.php&line=98", "ajax": false, "filename": "StatsOverview.php", "line": "98"}, "connection": "auvista", "explain": null, "start_percent": 97.115, "width_percent": 2.885}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.stats-overview #QA3rWTRellCt7LOztxoK": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.widgets.stats-overview\"\n  \"component\" => \"App\\Filament\\Widgets\\StatsOverview\"\n  \"id\" => \"QA3rWTRellCt7LOztxoK\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "2.66s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-944425059 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-944425059\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1748735414 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"305 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;QA3rWTRellCt7LOztxoK&quot;,&quot;name&quot;:&quot;app.filament.widgets.stats-overview&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;ec97c5f623f8fe48e8bd3f5f2e40b7378670184d4b0440473dc9b8c0bb9662a5&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6Imx5UXU1Z05sTGhJMzZ6dmVndW85IiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJlOGU1ZDk2NjllYWI4M2YyNWNhMzBmNmNjOTI2OWQ4YTczM2JiMGYyODQyN2M0ZjBjZDY1NDUyOThiNWRkOGM4In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748735414\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1570936665 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">748</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdUNW1nRmUzVDhTNFVYTGJwVFRqWlE9PSIsInZhbHVlIjoia2lNOHNsNWU1N21uM0hZSjFqTmIraUEzRnpBR01xLzFwdm91TXB5MVgySWdqTSt3dVVxZ2JPK3lGZ280aWRNVmN3RUdVT2o1cG1ET1dpbHEwNGViWlhPVitnUXVaUk41NmswUS9POTd2UnhqNmZ1eC9BSE9peUlSeisrNkFIZ1MiLCJtYWMiOiJiOGQ1NjRiMTQ3MjBhZDY1YzMwZDk1N2U4OWIwYmYwOWQyMjBiMGJiOGIyOGZhYTI5ZGMxOThiZDAwNTc4NjNjIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InVmL1FSSUdOb3Z3ODRmNUJiSkZnVkE9PSIsInZhbHVlIjoiOXBFUVBHM3oxZ3M5Z0FBb3RYTUZ4VnpSZ0gvMEJWVURPWlhhSGJpbS9IT0wrSDRMY1ArK2pZT21uRlpjZDA2WTI0UlJLWER2U3Bvb1Z5U0JpMWFCdDI1Z3k0MmNJSFBobHlOQ0JhVzRmZXA5REN6ajJaRTdobzh6UHdnSXhFU3UiLCJtYWMiOiJmMzg3ZmFhYzg3MGQ4NzU1OGI3NmRlNGYzNTcyY2Y2NDA5YjQwMGM1NGVlY2Y4MGVkZTJlYTFmNzRmNTcxZGIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570936665\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1014297822 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxYEiPXdkNcyfymjyeN8wk4RbgrpbsugedMWghRH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014297822\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-564068938 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:43:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564068938\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1771048481 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$GgKaDmjdfWlNzTUrcO1K5.P1hXVA9J4EX3bYW.ogCY4SrBpQeXIn2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1771048481\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}