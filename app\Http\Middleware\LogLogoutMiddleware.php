<?php

namespace App\Http\Middleware;

use App\Services\LoginLogService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class LogLogoutMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Kiểm tra nếu user đã logout (session bị destroy)
        if (Auth::check() && $request->is('admin/logout')) {
            $user = Auth::user();
            
            // Ghi log logout
            $loginLogService = app(LoginLogService::class);
            $loginLogService->logLogout($user);
        }

        return $response;
    }
} 