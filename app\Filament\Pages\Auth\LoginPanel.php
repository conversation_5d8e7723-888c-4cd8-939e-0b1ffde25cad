<?php

namespace App\Filament\Pages\Auth;

use App\Services\LoginLogService;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Component;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Validation\ValidationException;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;

class LoginPanel extends \Filament\Pages\Auth\Login
{
    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(__('filament-panels::pages/auth/login.form.email.label'))
            ->email()
            ->required()
            ->autocomplete()
            ->autofocus()
            ->default('<EMAIL>') // Giá trị mặc định cho email
            ->extraInputAttributes(['tabindex' => 1]);
    }

    protected function getPasswordFormComponent(): Component
    {
        return parent::getPasswordFormComponent()
            ->default('webest123@'); // Gi<PERSON> trị mặc định cho mật khẩu
    }

    /**
     * Override authenticate method để ghi log
     */
    public function authenticate(): ?LoginResponse
    {
        $loginLogService = app(LoginLogService::class);
        try {
            $response = parent::authenticate();
            $user = auth()->user();
            if ($user) {
                $loginLogService->logSuccessfulLogin($user);
            }
            return $response;
        } catch (ValidationException $e) {
            $email = $this->form->getState()['email'] ?? 'unknown';
            $loginLogService->logFailedLogin($email, 'invalid_credentials');
            throw $e;
        } catch (\Exception $e) {
            $email = $this->form->getState()['email'] ?? 'unknown';
            $loginLogService->logFailedLogin($email, 'system_error');
            throw $e;
        }
    }
}
