<?php

namespace Database\Seeders;

use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\StaticPage;
use Illuminate\Database\Seeder;

class FooterMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = ['vi', 'en'];

        foreach ($languages as $lang) {
            $this->createFooterAboutMenu($lang);
            $this->createFooterProductMenu($lang);
        }
    }

    private function createFooterAboutMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Về Auvista (VI)',
                'slug' => 'footer-about-vi',
                'items' => [
                    ['title' => 'Giới thiệu', 'url' => '/gioi-thieu'],
                    ['title' => 'Lịch sử auvista', 'url' => '/lich-su-auvista.html'],
                    ['title' => '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON> mệnh', 'url' => '/tam-nhin-su-menh.html'],
                    ['title' => 'Trách nhiệm xã hội', 'url' => '/trach-nhiem-xa-hoi.html'],
                ],
            ],
            'en' => [
                'name' => 'About Auvista (EN)',
                'slug' => 'footer-about-en',
                'items' => [
                    ['title' => 'About Us', 'url' => '/about-us'],
                    ['title' => 'Auvista History', 'url' => '/auvista-history.html'],
                    ['title' => 'Vision & Mission', 'url' => '/vision-mission.html'],
                    ['title' => 'Social Responsibility', 'url' => '/social-responsibility.html'],
                ],
            ],
        ];

        $menu = Menu::firstOrCreate(
            ['slug' => $menuData[$lang]['slug'], 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-about',
                'status' => true,
            ]
        );
        
        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'custom', // Assuming these are custom links or simple pages
                    'status' => true,
                ]
            );
        }
    }

    private function createFooterProductMenu(string $lang): void
    {
        $menuData = [
            'vi' => [
                'name' => 'Sản phẩm (VI)',
                'slug' => 'footer-product-vi',
                'items' => [
                    ['title' => 'Dinh dưỡng & Sức khỏe', 'url' => '/products/category/dinh-duong-suc-khoe'],
                    ['title' => 'Làm đẹp & Chăm sóc cá nhân', 'url' => '/products/category/lam-dep-cham-soc-ca-nhan'],
                    ['title' => 'Nhà cửa & Đời sống', 'url' => '/products/category/nha-cua-doi-song'],
                    ['title' => 'Sản phẩm mới', 'url' => '/products/new'],
                ],
            ],
            'en' => [
                'name' => 'Products (EN)',
                'slug' => 'footer-product-en',
                'items' => [
                    ['title' => 'Nutrition & Health', 'url' => '/products/category/nutrition-health'],
                    ['title' => 'Beauty & Personal Care', 'url' => '/products/category/beauty-personal-care'],
                    ['title' => 'Home & Living', 'url' => '/products/category/home-living'],
                    ['title' => 'New Products', 'url' => '/products/new'],
                ],
            ],
        ];
        
        $menu = Menu::firstOrCreate(
            ['slug' => $menuData[$lang]['slug'], 'lang' => $lang],
            [
                'name' => $menuData[$lang]['name'],
                'location' => 'footer-product',
                'status' => true,
            ]
        );

        foreach ($menuData[$lang]['items'] as $index => $item) {
            MenuItem::firstOrCreate(
                ['menu_id' => $menu->id, 'title' => $item['title']],
                [
                    'url' => $item['url'],
                    'order' => $index + 1,
                    'target' => '_self',
                    'type' => 'custom',
                    'status' => true,
                ]
            );
        }
    }

    private function createMenuItems(Menu $menu, array $items, string $lang)
    {
        // Implementation of createMenuItems method
    }
} 