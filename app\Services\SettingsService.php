<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * Lấy giá trị cài đặt theo key
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get($key, $default = null)
    {
        return Cache::remember('setting.' . $key, 3600, function () use ($key, $default) {
            $setting = Setting::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Lưu giá trị cài đặt
     * 
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function set($key, $value)
    {
        $setting = Setting::firstOrNew(['key' => $key]);
        $setting->value = $value;
        $setting->save();

        Cache::forget('setting.' . $key);
        return true;
    }

    /**
     * Xóa cài đặt
     * 
     * @param string $key
     * @return bool
     */
    public function remove($key)
    {
        $setting = Setting::where('key', $key)->first();
        if ($setting) {
            $setting->delete();
            Cache::forget('setting.' . $key);
            return true;
        }
        return false;
    }

    /**
     * <PERSON><PERSON><PERSON> tất cả cài đặt
     * 
     * @return array
     */
    public function all()
    {
        return Cache::remember('settings.all', 3600, function () {
            return Setting::pluck('value', 'key')->toArray();
        });
    }
} 