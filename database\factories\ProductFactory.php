<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Lấy category_ids từ bảng categories
        $categoryIds = Category::where('type', 'product')->pluck('id')->toArray();
        
        // Lấy brand_ids từ bảng brands
        $brandIds = Brand::pluck('id')->toArray();
        
        $name = $this->faker->sentence(3);
        $slug = Str::slug($name);
        
        $price = $this->faker->numberBetween(100000, 5000000);
        $sale_price = $this->faker->boolean(40) ? round($price * (1 - $this->faker->randomFloat(2, 0.05, 0.3))) : null; // 40% chance of having a sale price with 5%-30% discount
        $stock = $this->faker->numberBetween(0, 100);
        $status = $this->faker->boolean(80); // 80% chance of being active
        $is_featured = $this->faker->boolean(20);
        // Sử dụng ảnh placeholder có sẵn
        $placeholderImages = ['image-product-1.png', 'image-product-2.png', 'image-product-3.png', 'image-product-4.png'];
        $image = 'images/' . $this->faker->randomElement($placeholderImages);
        
        // Tạo danh sách ảnh gallery ngẫu nhiên từ placeholder
        $galleryImages = [];
        $galleryCount = $this->faker->numberBetween(4, 6);
        for ($i = 0; $i < $galleryCount; $i++) {
            $galleryImages[] = 'images/' . $this->faker->randomElement($placeholderImages);
        }
        
        // Tạo thông số kỹ thuật mẫu
        $specifications = [];
        $specsCount = $this->faker->numberBetween(5, 8);
        for ($i = 0; $i < $specsCount; $i++) {
            $specifications[] = [
                'key' => $this->faker->randomElement([
                    'Kích thước', 'Cân nặng', 'Driver', 'Phản hồi tần số', 'Trở kháng',
                    'Công suất', 'Bluetooth', 'Pin', 'Connector', 'Xuất xứ'
                ]),
                'value' => $this->faker->randomElement([
                    '220 x 95 x 93 mm', '285g', '45mm', '20Hz-20kHz', '32 ohm',
                    '40W RMS', '5.1', '20 giờ', '3.5mm', 'Trung Quốc'
                ])
            ];
        }

        // Tạo thông số sidebar (ít hơn)
        $sidebarSpecs = array_slice($specifications, 0, $this->faker->numberBetween(4, 5));

        // Tạo downloads mẫu
        $downloads = [];
        $downloadsCount = $this->faker->numberBetween(2, 4);
        $downloadTypes = [
            ['title' => 'Catalogue sản phẩm', 'type' => 'PDF', 'size' => '2.5 MB', 'color' => 'text-red-500'],
            ['title' => 'Hướng dẫn sử dụng', 'type' => 'PDF', 'size' => '1.8 MB', 'color' => 'text-blue-500'],
            ['title' => 'Thông số kỹ thuật', 'type' => 'PDF', 'size' => '900 KB', 'color' => 'text-green-500'],
            ['title' => 'Video hướng dẫn', 'type' => 'MP4', 'size' => '15 MB', 'color' => 'text-purple-500'],
        ];
        
        for ($i = 0; $i < $downloadsCount; $i++) {
            $downloadType = $downloadTypes[$i];
            $downloads[] = [
                'title' => $downloadType['title'],
                'name' => $downloadType['title'],
                'type' => $downloadType['type'],
                'size' => $downloadType['size'],
                'url' => '/uploads/downloads/' . Str::slug($name) . '-' . Str::slug($downloadType['title']) . '.' . strtolower($downloadType['type']),
                'color' => $downloadType['color']
            ];
        }

        return [
            'name' => $name,
            'slug' => $slug,
            'sku' => 'SP' . $this->faker->unique()->numberBetween(100000, 999999),
            'description' => $this->faker->paragraphs(3, true),
            'price' => $price,
            'sale_price' => $sale_price,
            'stock' => $stock,
            'status' => $status,
            'is_featured' => $is_featured,
            'image_url' => $image,
            'gallery' => !empty($galleryImages) ? json_encode($galleryImages) : null,
            'category_id' => $this->faker->randomElement($categoryIds),
            'brand_id' => $this->faker->randomElement($brandIds),
            'is_new' => $this->faker->boolean(30), // 30% chance of being new
            'is_best_seller' => $this->faker->boolean(15), // 15% chance of being best seller
            'technical_specs' => $specifications,
            'sidebar_specs' => $sidebarSpecs,
            'downloads' => $downloads,
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => function (array $attributes) {
                return $this->faker->dateTimeBetween($attributes['created_at'], 'now');
            },
        ];
    }

    /**
     * Indicate that the product is featured.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function featured(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_featured' => true,
            ];
        });
    }

    /**
     * Indicate that the product is new.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function isNew(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_new' => true,
            ];
        });
    }

    /**
     * Indicate that the product is a best seller.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function bestSeller(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'is_best_seller' => true,
            ];
        });
    }

    /**
     * Indicate that the product belongs to a specific category.
     *
     * @param int $categoryId
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function category(int $categoryId): Factory
    {
        return $this->state(function (array $attributes) use ($categoryId) {
            return [
                'category_id' => $categoryId,
            ];
        });
    }

    /**
     * Indicate that the product belongs to a specific brand.
     *
     * @param int $brandId
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function brand(int $brandId): Factory
    {
        return $this->state(function (array $attributes) use ($brandId) {
            return [
                'brand_id' => $brandId,
            ];
        });
    }
} 