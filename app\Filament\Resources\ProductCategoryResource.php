<?php

namespace App\Filament\Resources;

use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Filament\Resources\CategoryResource;
use App\Models\Category;

class ProductCategoryResource extends CategoryResource
{
    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';
    protected static ?string $navigationLabel = 'Danh mục sản phẩm';
    protected static ?int $navigationSort = 2; // Đặt thứ tự hiển thị trong menu
    protected static string $defaultType = 'product';
    
    protected static ?string $slug = 'categories/product'; // Custom URL slug
    
    protected static ?string $model = Category::class;
    protected static bool $shouldRegisterNavigation = true; // Cho phép hiển thị trong menu

    public static function getNavigationGroup(): string
    {
        return 'Quản lý sản phẩm'; // Nhóm menu quản lý sản phẩm
    }

    public static function getNavigationUrl(): string
    {
        return static::getUrl('index');
    }

    public static function isNavigationActive(): bool
    {
        $currentUrl = request()->url();
        $resourceUrl = static::getNavigationUrl();

        // Chỉ active khi URL chứa /categories/product
        return $currentUrl === $resourceUrl ||
               str_contains($currentUrl, '/categories/product');
    }

    public static function form(Form $form): Form
    {
        $form = parent::form($form);
        // You can customize the form if needed for products
        return $form;
    }

    public static function table(Table $table): Table
    {
        $table = parent::table($table);
        // You can customize the table if needed for products
        return $table;
    }

    public static function getPages(): array
    {
        return [
            'index' => \App\Filament\Resources\CategoryResource\Pages\ListCategories::route('/'),
            'create' => \App\Filament\Resources\CategoryResource\Pages\CreateCategory::route('/create'),
            'edit' => \App\Filament\Resources\CategoryResource\Pages\EditCategory::route('/{record}/edit'),
            'view' => \App\Filament\Resources\CategoryResource\Pages\ViewCategory::route('/{record}'),
        ];
    }
}