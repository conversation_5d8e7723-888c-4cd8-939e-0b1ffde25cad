<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\SettingRepository;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Schema;
use App\Services\SettingsService;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('settings', function ($app) {
            return new SettingsService();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Kiểm tra xem bảng settings và cache đã tồn tại chưa
        if (Schema::hasTable('settings') && Schema::hasTable('cache')) {
            // Cache settings trong 24 giờ
            $settings = Cache::remember('site_settings', 60 * 24, function () {
                $settingRepository = app(SettingRepository::class);
                $allSettings = $settingRepository->all();
                
                // <PERSON><PERSON> chức settings theo nhóm
                $groupedSettings = [];
                foreach ($allSettings as $setting) {
                    $group = $setting->group ?? 'general';
                    $groupedSettings[$group][$setting->name] = $setting->value;
                }
                
                return $groupedSettings;
            });

            // Chia sẻ settings với tất cả views
            View::share('settings', $settings);
        } else {
            // Nếu bảng settings hoặc cache chưa tồn tại, chia sẻ một mảng trống
            View::share('settings', []);
        }

        // Tạo helper function để truy cập settings dễ dàng
        // Di chuyển định nghĩa hàm get_setting ra khỏi class để tránh bị redeclare
    }
}

// Helper function đặt ở ngoài namespace để không bị redeclare
if (!function_exists('get_setting')) {
    function get_setting($key, $group = 'general', $default = null) {
        if (!Schema::hasTable('settings') || !Schema::hasTable('cache')) {
            return $default;
        }
        $settings = Cache::get('site_settings');
        return $settings[$group][$key] ?? $default;
    }
} 