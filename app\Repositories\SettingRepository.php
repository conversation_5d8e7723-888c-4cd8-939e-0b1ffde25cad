<?php

namespace App\Repositories;

use App\Models\Setting;

class SettingRepository extends BaseRepository
{
    /**
     * @return string
     */
    public function getModel()
    {
        return Setting::class;
    }
    
    /**
     * <PERSON><PERSON><PERSON> tất cả các cài đặt theo nhóm
     *
     * @param string $group
     * @return mixed
     */
    public function getSettingsByGroup($group)
    {
        return $this->model->where('group', $group)
                      ->get()
                      ->keyBy('name');
    }
    
    /**
     * Lấy giá trị cài đặt theo tên
     *
     * @param string $name
     * @param mixed $default
     * @return mixed
     */
    public function getSettingByName($name, $default = null)
    {
        $setting = $this->model->where('name', $name)->first();
        return $setting ? $setting->value : $default;
    }
    
    /**
     * Cập nhật hoặc tạo cài đặt mới
     *
     * @param string $name
     * @param mixed $value
     * @param string $group
     * @param string $type
     * @return mixed
     */
    public function updateOrCreateSetting($name, $value, $group = 'general', $type = 'string')
    {
        return $this->model->updateOrCreate(
            ['name' => $name],
            [
                'value' => $value,
                'group' => $group,
                'type' => $type
            ]
        );
    }
} 