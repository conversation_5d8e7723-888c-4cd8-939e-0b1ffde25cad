<?php

namespace App\Filament\Resources\PostResource\Pages;

use App\Filament\Resources\PostResource;
use App\Models\Post;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Str;
use Filament\Notifications\Notification;
use Illuminate\Http\Request;

class ListPosts extends ListRecords
{
    protected static string $resource = PostResource::class;

    public function mount(): void
    {
        // Xử lý tham số nhân bản
        $cloneId = request()->query('clone');
        if ($cloneId) {
            $this->handleClonePost($cloneId);
        }
        
        // Xử lý tham số xóa
        $deleteId = request()->query('delete');
        if ($deleteId) {
            $this->handleDeletePost($deleteId);
        }
        
        parent::mount();
    }
    
    protected function handleClonePost($postId): void
    {
        $record = Post::find($postId);
        
        if (!$record) {
            Notification::make()
                ->title('Không tìm thấy bài viết')
                ->danger()
                ->send();
            return;
        }
        
        // Nhân bản bài viết
        $newPost = $record->replicate();
        $newPost->title = $record->title . ' (Bản sao)';
        $newPost->slug = $record->slug . '-copy-' . Str::random(5);
        $newPost->created_at = now();
        $newPost->updated_at = now();
        $newPost->status = 'draft'; // Đặt trạng thái của bản sao là nháp
        $newPost->published_at = null;
        $newPost->save();
        
        // Sao chép danh mục
        $categories = $record->categories()->pluck('category_id')->toArray();
        if (!empty($categories)) {
            $newPost->categories()->sync($categories);
            
            // Sao chép danh mục chính
            $primaryCategoryId = $record->getPrimaryCategoryId();
            if ($primaryCategoryId) {
                $newPost->setPrimaryCategory($primaryCategoryId);
            }
        }
        
        // Thông báo và chuyển hướng
        Notification::make()
            ->title('Bài viết đã được nhân bản')
            ->body('Bài viết "' . $record->title . '" đã được nhân bản thành công.')
            ->success()
            ->send();
            
        redirect()->route('filament.' . config('filament.default_panel') . '.resources.posts.edit', ['record' => $newPost->id]);
    }
    
    protected function handleDeletePost($postId): void
    {
        // Chỉ hiển thị modal xóa, không xóa trực tiếp
        // Sẽ chuyển hướng để modal xóa của Filament hiển thị
        redirect()->route('filament.' . config('filament.default_panel') . '.resources.posts.index');
        
        // Lưu ý: Không có cách đơn giản để kích hoạt modal xóa của Filament qua URL
        // Người dùng vẫn cần sử dụng nút xóa trong trang danh sách
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->modalWidth('7xl')  // Changed to 7xl which is roughly 80%
                ->before(function (array $data) {
                    $data['author_id'] = auth()->id();
                    return $data;
                }),
        ];
    }
}
