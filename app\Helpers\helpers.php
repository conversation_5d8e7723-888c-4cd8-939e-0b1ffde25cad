<?php

use App\Services\CartService;

/**
 * Lấy giá trị cài đặt từ cơ sở dữ liệu
 *
 * @param string $key Khóa cài đặt cần lấy
 * @param mixed $default Gi<PERSON> trị mặc định nếu không tìm thấy
 * @return mixed Giá trị cài đặt
 */
function getSetting($key, $default = null)
{
    $locale = app()->getLocale();
    // Sử dụng cache để tối ưu hiệu suất, thêm locale vào key cache
    return \Illuminate\Support\Facades\Cache::remember('setting_' . $locale . '_' . $key, 3600, function() use ($key, $default, $locale) {
        $setting = \App\Models\Setting::where('name', $key)->where('lang', $locale)->first();
        
        // Nếu không tìm thấy setting cho ngôn ngữ hiện tại, thử tìm cho ngôn ngữ mặc định (vi)
        if (!$setting) {
            $setting = \App\Models\Setting::where('name', $key)->where('lang', 'vi')->first();
        }

        return $setting ? $setting->value : $default;
    });
}

/**
 * Lấy URL của hình ảnh với kích thước tùy chỉnh
 * 
 * @param string|null $path Đường dẫn hình ảnh
 * @param string $fallbackOrContext Fallback image path hoặc context (product, post, etc.)
 * @param string|int $width Chiều rộng hình ảnh
 * @param string|int $height Chiều cao hình ảnh
 * @return string URL của hình ảnh
 */
function getImageUrl($path = null, $fallbackOrContext = 'product', $width = null, $height = null) {
    // Xác định context trước
    $context = (strpos($fallbackOrContext, 'images/') !== false || preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $fallbackOrContext)) 
        ? 'product' 
        : $fallbackOrContext;
    
    // Nếu đường dẫn trống, trả về placeholder
    if (empty($path)) {
        $placeholderText = 'No+Image';
        
        if ($context == 'product' || strpos($context, 'product') !== false) {
            $placeholderText = 'Sản+Phẩm';
        } elseif ($context == 'post' || strpos($context, 'post') !== false) {
            $placeholderText = 'Bài+Viết';
        } elseif ($context == 'category' || strpos($context, 'category') !== false) {
            $placeholderText = 'Danh+Mục';
        }
        
        return 'https://placehold.co/' . ($width ?: 600) . 'x' . ($height ?: 400) . '/e5e7eb/a3a3a3.png?text=' . $placeholderText;
    }
    
    // Kiểm tra xem đường dẫn có phải URL đầy đủ hay không
    if (filter_var($path, FILTER_VALIDATE_URL)) {
        return $path;
    }
    
    // Đường dẫn tuyệt đối đến thư mục storage
    $storagePath = storage_path('app/public');
    
    // Chuẩn hóa đường dẫn (loại bỏ các ký tự / thừa ở đầu)
    $path = ltrim($path, '/');
    
    // Đường dẫn đầy đủ đến file
    $fullPath = $storagePath . '/' . $path;
    
    // Kiểm tra xem file có tồn tại không
    if (!file_exists($fullPath)) {
        // Nếu có fallback image, dùng fallback
        if (strpos($fallbackOrContext, 'images/') !== false || preg_match('/\.(jpg|jpeg|png|gif|webp)$/i', $fallbackOrContext)) {
            // Kiểm tra xem fallback file có tồn tại không
            $fallbackPath = public_path($fallbackOrContext);
            if (file_exists($fallbackPath)) {
                return asset($fallbackOrContext);
            }
        }
        
        // Nếu không tìm thấy hình ảnh, trả về placeholder theo context
        $placeholderText = 'No+Image';
        
        if ($context == 'product' || strpos($path, 'product') !== false) {
            $placeholderText = 'Sản+Phẩm';
        } elseif ($context == 'post' || strpos($path, 'post') !== false) {
            $placeholderText = 'Bài+Viết';
        } elseif ($context == 'category' || strpos($path, 'category') !== false) {
            $placeholderText = 'Danh+Mục';
        }
        
        return 'https://placehold.co/' . ($width ?: 600) . 'x' . ($height ?: 400) . '/e5e7eb/a3a3a3.png?text=' . $placeholderText;
    }
    
    // Nếu không cần resize, trả về URL gốc
    if (is_null($width) && is_null($height)) {
        return asset('storage/' . $path);
    }
    
    // Xử lý resize hình ảnh
    try {
        // Tạo tên file mới cho hình ảnh đã resize
        $extension = pathinfo($path, PATHINFO_EXTENSION);
        $filename = pathinfo($path, PATHINFO_FILENAME);
        $directory = pathinfo($path, PATHINFO_DIRNAME);
        
        // Tạo tên file mới với kích thước
        $newFilename = $filename . '-' . $width . 'x' . $height . '.' . $extension;
        
        // Thư mục cache cho hình ảnh đã resize
        $cacheDir = 'cache/' . $directory;
        $cachePath = $storagePath . '/' . $cacheDir;
        
        // Tạo thư mục cache nếu chưa tồn tại
        if (!file_exists($cachePath)) {
            mkdir($cachePath, 0755, true);
        }
        
        // Đường dẫn đầy đủ đến file đã resize
        $newPath = $cachePath . '/' . $newFilename;
        
        // Nếu file đã resize tồn tại, trả về URL của nó
        if (file_exists($newPath)) {
            return asset('storage/' . $cacheDir . '/' . $newFilename);
        }
        
        // Resize hình ảnh
        $image = \Intervention\Image\ImageManager::gd()->read($fullPath);
        
        if ($width && $height) {
            // Resize với chiều rộng và chiều cao cố định
            $image->cover($width, $height);
        } elseif ($width) {
            // Resize chỉ với chiều rộng, chiều cao tự động
            $image->scale(width: $width);
        } elseif ($height) {
            // Resize chỉ với chiều cao, chiều rộng tự động
            $image->scale(height: $height);
        }
        
        // Lưu hình ảnh đã resize
        $image->save($newPath);
        
        return asset('storage/' . $cacheDir . '/' . $newFilename);
    } catch (\Exception $e) {
        // Nếu có lỗi khi resize, trả về placeholder
        $placeholderText = 'No+Image';
        
        if ($context == 'product' || strpos($path, 'product') !== false) {
            $placeholderText = 'Sản+Phẩm';
        } elseif ($context == 'post' || strpos($path, 'post') !== false) {
            $placeholderText = 'Bài+Viết';
        } elseif ($context == 'category' || strpos($path, 'category') !== false) {
            $placeholderText = 'Danh+Mục';
        }
        
        return 'https://placehold.co/' . ($width ?: 600) . 'x' . ($height ?: 400) . '/e5e7eb/a3a3a3.png?text=' . $placeholderText;
    }
}

/**
 * Trả về URL của file media (video, audio)
 *
 * @param string $mediaPath Đường dẫn đến file media
 * @return string URL đến file media
 */
function getMediaUrl($mediaPath)
{
    if (empty($mediaPath)) {
        return '';
    }

    // Nếu đường dẫn đã là URL đầy đủ
    if (filter_var($mediaPath, FILTER_VALIDATE_URL)) {
        return $mediaPath;
    }

    // Nếu đường dẫn bắt đầu bằng "storage/"
    if (strpos($mediaPath, 'storage/') === 0) {
        $mediaPath = 'public/' . substr($mediaPath, 8);
    }

    // Nếu đường dẫn bắt đầu bằng "/"
    if (substr($mediaPath, 0, 1) === '/') {
        $mediaPath = ltrim($mediaPath, '/');
    }
    
    // Kiểm tra xem file có tồn tại trong storage không
    if (!\Illuminate\Support\Facades\Storage::exists($mediaPath)) {
        return '';
    }

    return \Illuminate\Support\Facades\Storage::url($mediaPath);
}

/**
 * Trả về URL chi tiết bài viết
 *
 * @param string|object $post Slug hoặc đối tượng bài viết
 * @return string URL chi tiết bài viết
 */
function getPostUrl($post)
{
    if (is_object($post)) {
        $slug = $post->slug;
    } else {
        $slug = $post;
    }
    
    return url($slug . '.html');
}

/**
 * Trả về URL danh mục bài viết
 *
 * @param string|object|array $category Slug, đối tượng hoặc mảng thông tin danh mục
 * @return string URL danh mục bài viết
 */
function getCategoryUrl($category)
{
    if (is_object($category)) {
        $slug = $category->slug;
    } elseif (is_array($category) && isset($category['slug'])) {
        $slug = $category['slug'];
    } else {
        $slug = $category;
    }
    
    return url($slug . '/');
}

/**
 * Trả về URL danh mục bài viết có phân trang
 *
 * @param string|object|array $category Slug, đối tượng hoặc mảng thông tin danh mục
 * @param int|null $page Số trang, null nếu là trang đầu tiên
 * @return string URL danh mục bài viết có phân trang
 */
function getCategoryPageUrl($category, $page = null)
{
    $slug = '';
    
    if (is_object($category)) {
        $slug = $category->slug;
    } elseif (is_array($category) && isset($category['slug'])) {
        $slug = $category['slug'];
    } else {
        $slug = $category;
    }
    
    // Nếu là trang tin tức tổng hợp
    if (empty($slug)) {
        if ($page && $page > 1) {
            return url('tin-tuc?page=' . $page);
        }
        return url('tin-tuc');
    }
    
    // Nếu là trang đầu tiên hoặc không có số trang
    if (!$page || $page <= 1) {
        return url('tin-tuc/' . $slug . '.html');
    }
    
    // Nếu là các trang tiếp theo
    return url('tin-tuc/' . $slug . '.html?page=' . $page);
}

/**
 * Trả về URL chi tiết trang tĩnh
 *
 * @param string|object $page Slug hoặc đối tượng trang tĩnh
 * @return string URL chi tiết trang tĩnh
 */
function getPageUrl($page)
{
    if (is_object($page)) {
        $slug = $page->slug;
    } else {
        $slug = $page;
    }
    
    // Xử lý đặc biệt cho trang giới thiệu
    if ($slug === 'gioi-thieu') {
        return url('/gioi-thieu');
    }
    
    return url($slug . '.html');
}

/**
 * Lấy menu theo vị trí
 *
 * @param string $location Vị trí của menu (header, footer, sidebar, ...)
 * @param bool $skipCache Bỏ qua cache nếu true
 * @return \App\Models\Menu|null Menu tìm thấy hoặc null nếu không tìm thấy
 */
function getMenu($location, $skipCache = false)
{
    $cacheKey = 'menu_'.$location;
    
    // Nếu yêu cầu bỏ qua cache hoặc đang ở trang admin
    if ($skipCache || request()->segment(1) === 'admin') {
        // Xóa cache hiện tại
        \Illuminate\Support\Facades\Cache::forget($cacheKey);
        
        // Lấy menu từ database
        $menu = \App\Models\Menu::where('location', $location)
                ->where('status', true)
                ->with(['items' => function($query) {
                    $query->where('status', true)
                          ->whereNull('parent_id')
                          ->orderBy('order');
                }, 'items.children' => function($query) {
                    $query->where('status', true)
                          ->orderBy('order');
                }])
                ->first();
                
        return $menu;
    }
    
    // Sử dụng cache với thời gian 1 giờ
    return \Illuminate\Support\Facades\Cache::remember($cacheKey, 3600, function() use ($location) {
        $menu = \App\Models\Menu::where('location', $location)
                ->where('status', true)
                ->with(['items' => function($query) {
                    $query->where('status', true)
                          ->whereNull('parent_id')
                          ->orderBy('order');
                }, 'items.children' => function($query) {
                    $query->where('status', true)
                          ->orderBy('order');
                }])
                ->first();
                
        if ($menu) {
            \Illuminate\Support\Facades\Log::info("Menu [{$location}] cached:", [
                'menu_id' => $menu->id,
                'menu_name' => $menu->name,
                'items_count' => $menu->items->count(),
                'items' => $menu->items->pluck('title', 'id')->toArray()
            ]);
        }
        
        return $menu;
    });
}

/**
 * Hiển thị menu chính
 *
 * @param string $location Vị trí của menu (header, footer, sidebar, ...)
 * @param string $template Template để hiển thị menu (default, mobile, ...)
 * @return string HTML của menu
 */
function renderMenu($location = 'header', $template = 'default')
{
    $menu = getMenu($location);
    
    if (!$menu) {
        return '';
    }
    
    if ($template === 'default') {
        return renderDefaultMenu($menu->items);
    } elseif ($template === 'mobile') {
        return renderMobileMenu($menu->items);
    }
    
    return '';
}

/**
 * Hiển thị menu mặc định
 *
 * @param \Illuminate\Database\Eloquent\Collection $items Các mục menu
 * @param int $level Cấp độ của menu
 * @return string HTML của menu
 */
function renderDefaultMenu($items, $level = 0)
{
    if ($items->isEmpty()) {
        return '';
    }
    
    // Áp dụng class cho menu cấp 0
    $html = $level === 0 ? '<ul class="menu-ul wrap">' : '<ul class="menu-sub">';
    
    foreach ($items as $item) {
        $hasChildren = $item->children->isNotEmpty();
        $isActive = isMenuItemActive($item);
        $liClass = $level === 0 ? 'menu-ul__li menu-item' : 'menu-sub__li clear';
        
        $html .= '<li class="' . $liClass . ($isActive ? ' active' : '') . '">';
        
        // Xử lý URL
        $url = '#';
        if ($item->type === 'custom') {
            $url = $item->url;
        } elseif ($item->model_type === 'App\\Models\\Post' && $item->model) {
            $url = getPostUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\Category' && $item->model) {
            $url = getCategoryUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\StaticPage' && $item->model) {
            $url = getPageUrl($item->model);
        }
        
        $target = $item->target ?: '_self';
        $itemClass = $hasChildren ? ' has-submenu' : '';
        
        $html .= '<a href="' . $url . '" class="menu-link' . $itemClass . '" target="' . $target . '">';
        
        if ($item->icon) {
            $html .= '<i class="' . $item->icon . '"></i> ';
        }
        
        $html .= $item->title;
        
        if ($hasChildren && $level === 0) {
            $html .= ' <i class="fa fa-angle-down"></i>';
        }
        
        $html .= '</a>';
        
        if ($hasChildren) {
            $html .= renderDefaultMenu($item->children, $level + 1);
        }
        
        $html .= '</li>';
    }
    
    $html .= '</ul>';
    
    return $html;
}

/**
 * Hiển thị menu mobile
 *
 * @param \Illuminate\Database\Eloquent\Collection $items Các mục menu
 * @param int $level Cấp độ của menu
 * @return string HTML của menu
 */
function renderMobileMenu($items, $level = 0)
{
    if ($items->isEmpty()) {
        return '';
    }
    
    if ($level === 0) {
        $html = '<div class="second-nav">';
        // Thêm header menu mobile
        $html .= '<div class="menu-mobile-header">
            <div class="site-logo">
                <img src="' . getImageUrl(getSetting('site_logo_mobile') ?: getSetting('site_logo'), 160, 42) . '" alt="' . getSetting('site_title') . '">
            </div>
            <button class="menu-close" title="Đóng menu">
                <i class="fa fa-times" aria-hidden="true"></i>
            </button>
        </div>';
        $html .= '<ul>';
    } else {
        $html = '<ul>';
    }
    
    foreach ($items as $item) {
        $hasChildren = $item->children->isNotEmpty();
        $isActive = isMenuItemActive($item);
        
        $html .= '<li class="' . ($isActive ? 'active' : '') . '">';
        
        // Xử lý URL
        $url = '#';
        if ($item->type === 'custom') {
            $url = $item->url;
        } elseif ($item->model_type === 'App\\Models\\Post') {
            $url = getPostUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\Category') {
            $url = getCategoryUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\StaticPage') {
            $url = getPageUrl($item->model);
        }
        
        // Thêm icon nếu có
        $icon = '';
        if ($item->icon) {
            $icon = '<i class="' . $item->icon . '" aria-hidden="true"></i> ';
        }
        
        // Thêm link
        $html .= '<a href="' . $url . '"' . ($item->target ? ' target="' . $item->target . '"' : '') . '>' . $icon . $item->title . '</a>';
        
        // Thêm submenu nếu có
        if ($hasChildren) {
            $html .= renderMobileMenu($item->children, $level + 1);
        }
        
        $html .= '</li>';
    }
    
    // Thêm thông tin liên hệ vào menu mobile cấp 0
    if ($level === 0) {
        $html .= '</ul><div class="foot-menu">
            <div class="foot-menu__title">
                ' . getSetting('site_title') . '
            </div>
            <div class="foot-menu__info">
                <div class="phone">
                    <div class="icon"><a href="tel:' . getSetting('site_phone') . '"><i class="fa fa-phone"></i></a></div>
                </div>
            </div>
            <p></p>
        </div></div>';
    } else {
        $html .= '</ul>';
    }
    
    return $html;
}

/**
 * Kiểm tra xem menu item có đang active không
 *
 * @param \App\Models\MenuItem $item Menu item cần kiểm tra
 * @return bool True nếu menu item đang active, ngược lại là false
 */
function isMenuItemActive($item)
{
    $currentUrl = request()->url();
    
    // Kiểm tra URL hiện tại có trùng với URL của menu item không
    if ($item->url && $currentUrl === $item->url) {
        return true;
    }
    
    // Kiểm tra URL hiện tại có bắt đầu bằng URL của menu item không (cho submenu)
    if ($item->url && $item->url !== '#' && strpos($currentUrl, $item->url) === 0) {
        return true;
    }
    
    // Kiểm tra các menu item con
    foreach ($item->children as $child) {
        if (isMenuItemActive($child)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Hiển thị menu footer
 *
 * @param string $location Vị trí của menu (footer-1, footer-2, ...)
 * @param string $title Tiêu đề của menu
 * @return string HTML của menu footer
 */
function renderFooterMenu($location, $title = '')
{
    $menu = getMenu($location);
    
    if (!$menu) {
        return '';
    }
    
    $html = '<div class="item-foot foot-link__content">';
    
    // Thêm tiêu đề nếu có
    if (!empty($title)) {
        $html .= '<div class="item-foot__title wrap">
                    <p class="name">' . $title . '</p>
                  </div>';
    }
    
    $html .= '<div class="item-foot__content wrap">
                <ul class="list-link">';
    
    foreach ($menu->items as $item) {
        // Xử lý URL
        $url = '#';
        if ($item->type === 'custom') {
            $url = $item->url;
        } elseif ($item->model_type === 'App\\Models\\Post') {
            $url = getPostUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\Category') {
            $url = getCategoryUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\StaticPage') {
            $url = getPageUrl($item->model);
        }
        
        $html .= '<li class="list-link__item">
                    <a href="' . $url . '"' . ($item->target ? ' target="' . $item->target . '"' : '') . '>' . $item->title . '</a>
                  </li>';
    }
    
    $html .= '</ul>
              </div>
            </div>';
    
    return $html;
}

/**
 * Định dạng ngày tháng theo ngôn ngữ hiện tại
 *
 * @param string|\Carbon\Carbon $date Ngày cần định dạng
 * @param string $format Format hiển thị, mặc định là 'd/m/Y'
 * @return string Chuỗi ngày đã được định dạng
 */
function formatDateLocalized($date, $format = 'd/m/Y')
{
    if (!$date) {
        return '';
    }
    
    if (!($date instanceof \Carbon\Carbon)) {
        $date = \Carbon\Carbon::parse($date);
    }
    
    // Nếu sử dụng tiếng Việt, format theo kiểu Việt Nam
    if (\Illuminate\Support\Facades\App::getLocale() === 'vi') {
        return $date->format($format);
    }
    
    return $date->format($format);
}

if (!function_exists('formatDate')) {
    /**
     * Định dạng ngày tháng theo format d/m/Y
     *
     * @param string|null $date
     * @return string
     */
    function formatDate($date)
    {
        if (!$date) return '';
        return date('d/m/Y', strtotime($date));
    }
}

if (!function_exists('formatDateTime')) {
    /**
     * Định dạng ngày tháng giờ theo format d/m/Y H:i
     *
     * @param string|null $date
     * @return string
     */
    function formatDateTime($date)
    {
        if (!$date) return '';
        return date('d/m/Y H:i', strtotime($date));
    }
}

if (!function_exists('getPostUrl')) {
    /**
     * Lấy URL của bài viết
     * 
     * @param string|array $post Slug hoặc mảng thông tin bài viết
     * @return string
     */
    function getPostUrl($post)
    {
        if (is_array($post)) {
            $slug = $post['slug'];
        } else {
            $slug = $post;
        }
        return url($slug . '.html');
    }
}

if (!function_exists('getCategoryUrl')) {
    /**
     * Lấy URL của danh mục
     * 
     * @param array|string|object $category Category array hoặc slug hoặc object
     * @param bool $absolute Trả về URL tuyệt đối hay không
     * @return string
     */
    function getCategoryUrl($category, $absolute = true)
    {
        $slug = '';
        $type = 'product'; // Mặc định là sản phẩm
        
        if (is_object($category)) {
            $slug = $category->slug;
            $type = $category->type ?? 'product';
        } elseif (is_array($category)) {
            $slug = $category['slug'];
            $type = $category['type'] ?? 'product';
        } else {
            $slug = $category;
            
            // Kiểm tra nếu slug là string, thử tìm category trong database để xác định type
            try {
                $categoryModel = \App\Models\Category::where('slug', $slug)->first();
                if ($categoryModel) {
                    $type = $categoryModel->type;
                }
            } catch (\Exception $e) {
                // Giữ nguyên giá trị mặc định nếu có lỗi
            }
        }
        
        // Nếu là danh mục sản phẩm
        if ($type == 'product') {
            return route('products.category', ['slug' => $slug]);
        }
        
        // Nếu là danh mục bài viết
        if ($type == 'post') {
            return route('static.page', ['slug' => $slug]);
        }
        
        // Mặc định cho trường hợp không xác định được type
        return url($slug . '/');
    }
}

if (!function_exists('getCategoryPlaceholder')) {
    /**
     * Lấy ảnh placeholder phù hợp cho danh mục
     * 
     * @param object $category Category object
     * @param int $index Loop index for fallback
     * @return string
     */
    function getCategoryPlaceholder($category, $index = 0)
    {
        // If category has thumbnail, return it
        if ($category->thumbnail) {
            return asset('storage/' . $category->thumbnail);
        }
        
        $categoryName = strtolower($category->name);
        
        // Smart placeholder based on category name
        if (str_contains($categoryName, 'tai nghe')) {
            return asset('images/img-cat-1.png');
        } elseif (str_contains($categoryName, 'loa')) {
            return asset('images/img-cat-2.png');
        } elseif (str_contains($categoryName, 'âm thanh') || str_contains($categoryName, 'ampli') || str_contains($categoryName, 'dac')) {
            return asset('images/img-cat-3.png');
        } elseif (str_contains($categoryName, 'micro') || str_contains($categoryName, 'soundcard')) {
            return asset('images/img-cat-4.png');
        } elseif (str_contains($categoryName, 'gaming')) {
            return asset('images/img-cat-5.png');
        } elseif (str_contains($categoryName, 'bluetooth') || str_contains($categoryName, 'wireless')) {
            return asset('images/img-cat-6.png');
        } else {
            // Cycle through images for other categories
            return asset('images/img-cat-' . (($index % 6) + 1) . '.png');
        }
    }
}

if (!function_exists('formatCurrency')) {
    /**
     * Định dạng số tiền
     * 
     * @param float|int|string $amount
     * @param string $currency
     * @return string
     */
    function formatCurrency($amount, $currency = 'VND')
    {
        if (!is_numeric($amount)) {
            return $amount;
        }

        $amount = number_format($amount, 0, ',', '.');
        
        if ($currency == 'VND') {
            return $amount . ' ₫';
        }
        
        return $amount . ' ' . $currency;
    }
}

if (!function_exists('formatPrice')) {
    /**
     * Format price for display
     * 
     * @param float|int|string $price
     * @return string
     */
    function formatPrice($price)
    {
        return formatCurrency($price, 'VND');
    }
}

if (!function_exists('stripTags')) {
    /**
     * Loại bỏ HTML tags và rút gọn chuỗi
     * 
     * @param string $text
     * @param int $length
     * @return string
     */
    function stripTags($text, $length = 0)
    {
        $text = strip_tags($text);
        $text = trim(preg_replace('/\s+/', ' ', $text));
        
        if ($length > 0 && strlen($text) > $length) {
            $text = substr($text, 0, $length) . '...';
        }
        
        return $text;
    }
}

if (!function_exists('getMetaTitle')) {
    /**
     * Lấy tiêu đề meta cho SEO
     * 
     * @param string $title
     * @param string|null $suffix
     * @return string
     */
    function getMetaTitle($title, $suffix = null)
    {
        if (!$suffix) {
            $suffix = getSetting('site_title');
        }
        
        return $title . ($suffix ? ' - ' . $suffix : '');
    }
}

if (!function_exists('getMetaDescription')) {
    /**
     * Lấy mô tả meta cho SEO
     * 
     * @param string $description
     * @param int $length
     * @return string
     */
    function getMetaDescription($description, $length = 160)
    {
        return stripTags($description, $length);
    }
}

if (!function_exists('formatFileSize')) {
    /**
     * Định dạng kích thước file
     * 
     * @param int $bytes
     * @return string
     */
    function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } elseif ($bytes > 1) {
            return $bytes . ' bytes';
        } elseif ($bytes == 1) {
            return $bytes . ' byte';
        } else {
            return '0 bytes';
        }
    }
}

if (!function_exists('getProductUrl')) {
    /**
     * Tạo URL cho sản phẩm
     * 
     * @param string $slug Slug của sản phẩm
     * @return string URL đầy đủ của sản phẩm
     */
    function getProductUrl($slug)
    {
        return route('products.show', ['slug' => $slug]);
    }
}

if (!function_exists('getBrandUrl')) {
    /**
     * Tạo URL cho thương hiệu sản phẩm
     * 
     * @param string $slug Slug của thương hiệu
     * @return string URL đầy đủ của thương hiệu
     */
    function getBrandUrl($slug)
    {
        return route('products.brand', ['slug' => $slug]);
    }
}

/**
 * Lấy menu top
 * 
 * @return \App\Models\Menu|null
 */
function getTopMenu()
{
    return \App\Models\Menu::where('location', 'top')
        ->where('status', true)
        ->with(['items' => function($query) {
            $query->where('status', true)
                  ->whereNull('parent_id')
                  ->orderBy('order');
        }])
        ->first();
}

/**
 * Lấy menu chính
 * 
 * @return \App\Models\Menu|null
 */
function getMainMenu()
{
    return \App\Models\Menu::where('location', 'main')
        ->where('status', true)
        ->with(['items' => function($query) {
            $query->where('status', true)
                  ->whereNull('parent_id')
                  ->orderBy('order');
        }, 'items.children' => function($query) {
            $query->where('status', true)
                  ->orderBy('order');
        }])
        ->first();
}

/**
 * Render menu top
 * 
 * @return string
 */
function renderTopMenu()
{
    $menu = getTopMenu();
    if (!$menu) return '';

    $html = '<ul class="top-menu">';
    foreach ($menu->items as $item) {
        $url = '#';
        if ($item->type === 'custom') {
            $url = $item->url;
        } elseif ($item->model_type === 'App\\Models\\Post') {
            $url = getPostUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\Category') {
            $url = getCategoryUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\StaticPage') {
            $url = getPageUrl($item->model);
        }

        $html .= '<li class="top-menu__item">';
        $html .= '<a href="' . $url . '"' . ($item->target ? ' target="' . $item->target . '"' : '') . '>';
        if ($item->icon) {
            $html .= '<i class="' . $item->icon . '"></i> ';
        }
        $html .= $item->title;
        $html .= '</a>';
        $html .= '</li>';
    }
    $html .= '</ul>';
    return $html;
}

/**
 * Render menu chính
 * 
 * @return string
 */
function renderMainMenu()
{
    $menu = getMainMenu();
    if (!$menu) return '';

    $html = '<ul class="main-menu">';
    foreach ($menu->items as $item) {
        $hasChildren = $item->children->isNotEmpty();
        $isActive = isMenuItemActive($item);
        
        $url = '#';
        if ($item->type === 'custom') {
            $url = $item->url;
        } elseif ($item->model_type === 'App\\Models\\Post') {
            $url = getPostUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\Category') {
            $url = getCategoryUrl($item->model);
        } elseif ($item->model_type === 'App\\Models\\StaticPage') {
            $url = getPageUrl($item->model);
        }

        $html .= '<li class="main-menu__item' . ($isActive ? ' active' : '') . ($hasChildren ? ' has-submenu' : '') . '">';
        $html .= '<a href="' . $url . '" class="main-menu__link">';
        if ($item->icon) {
            $html .= '<i class="' . $item->icon . '"></i> ';
        }
        $html .= $item->title;
        if ($hasChildren) {
            $html .= ' <i class="fas fa-chevron-down"></i>';
        }
        $html .= '</a>';

        if ($hasChildren) {
            $html .= '<ul class="submenu">';
            foreach ($item->children as $child) {
                $childUrl = '#';
                if ($child->type === 'custom') {
                    $childUrl = $child->url;
                } elseif ($child->model_type === 'App\\Models\\Post') {
                    $childUrl = getPostUrl($child->model);
                } elseif ($child->model_type === 'App\\Models\\Category') {
                    $childUrl = getCategoryUrl($child->model);
                } elseif ($child->model_type === 'App\\Models\\StaticPage') {
                    $childUrl = getPageUrl($child->model);
                }

                $html .= '<li class="submenu__item">';
                $html .= '<a href="' . $childUrl . '"' . ($child->target ? ' target="' . $child->target . '"' : '') . '>';
                if ($child->icon) {
                    $html .= '<i class="' . $child->icon . '"></i> ';
                }
                $html .= $child->title;
                $html .= '</a>';
                $html .= '</li>';
            }
            $html .= '</ul>';
        }

        $html .= '</li>';
    }
    $html .= '</ul>';
    return $html;
}

if (!function_exists('getCartCount')) {
    /**
     * Lấy số lượng sản phẩm trong giỏ hàng
     * 
     * @return int
     */
    function getCartCount()
    {
        // Sửa lỗi bằng cách sử dụng global namespace
        return (new \App\Services\CartService())->getCartCount();
    }
}

/**
 * Xử lý tất cả hình ảnh trong nội dung HTML và thay thế bằng getImageUrl
 *
 * @param string $content Nội dung HTML cần xử lý
 * @return string Nội dung HTML sau khi xử lý
 */
function processContentImages($content)
{
    if (empty($content)) {
        return $content;
    }
    
    // Tìm và thay thế tất cả đường dẫn ảnh trong thẻ img
    $pattern = '/<img[^>]*src=[\"\']([^\"\']+)[\"\'][^>]*>/i';
    
    return preg_replace_callback($pattern, function($matches) {
        $fullTag = $matches[0]; // Thẻ img đầy đủ
        $src = $matches[1];     // Giá trị của thuộc tính src
        
        // Bỏ qua các URL bên ngoài và data URL
        if (strpos($src, 'http') === 0 || strpos($src, 'data:') === 0) {
            return $fullTag;
        }
        
        // Thay thế src bằng kết quả từ getImageUrl
        return str_replace($src, getImageUrl($src), $fullTag);
    }, $content);
}

if (!function_exists('getBrandImageUrl')) {
    /**
     * Lấy URL ảnh thương hiệu từ thư mục products
     * 
     * @param string $brandSlug Slug của thương hiệu
     * @param int $width Chiều rộng ảnh
     * @param int $height Chiều cao ảnh
     * @return string URL ảnh thương hiệu
     */
    function getBrandImageUrl($brandSlug, $width = 600, $height = 400)
    {
        // Map slug brand với thư mục products
        $brandFolderMap = [
            'thai-tue' => 'ruou-thai-tue',
            'hu-shen-yuan' => 'an-truong-khang',
            'hu-shen-yuan-ngoc-bao-dan' => 'ruou-thai-tue',
            'tran-bi' => 'hoan-nguyen-bao',
            'tran-bi-tan-hoi' => 'hoan-nguyen-bao',
            'ben-huan-yuan' => 'ruou-thai-tue',
            'hu-wei-fang' => 'hoan-xuan',
            'hu-wei-shi' => 'hoan-xuan',
            'zhi-san-tone' => 'tra-pho-nhi',
            'chang-huan-qing' => 'sinh-luc-vuong',
            'tra-pho-nhi' => 'tra-pho-nhi',
            'xin-xue-ning' => 'da-day-an-vi',
            'ngoc-bat-bao' => 'ngoc-bao-dan',
            'da-day-an-vi' => 'da-day-an-vi',
            'sinh-luc-vuong' => 'sinh-luc-vuong',
            'ngoc-bao-dan' => 'ngoc-bao-dan',
            'hoat-huyet-khang' => 'hoat-huyet-khang',
            'hoan-xuan' => 'hoan-xuan',
            'an-truong-khang' => 'an-truong-khang',
            // Thêm mapping cho các brands auvista từ seeder
            'nutrilite' => 'ruou-thai-tue',
            'artistry' => 'hoan-xuan',
            'bodykey' => 'sinh-luc-vuong',
            'atmosphere' => 'tra-pho-nhi',
            'espring' => 'da-day-an-vi',
            'g-and-h' => 'ngoc-bao-dan',
            'glister' => 'hoat-huyet-khang',
            'auvista-home' => 'hoan-xuan',
            'xs' => 'tra-pho-nhi',
            'satinique' => 'da-day-an-vi'
        ];

        // Lấy tên thư mục từ map
        $folderName = $brandFolderMap[$brandSlug] ?? $brandSlug;
        
        // Đường dẫn thư mục sản phẩm
        $productFolder = storage_path('app/public/products/' . $folderName);
        
        // Kiểm tra thư mục có tồn tại không
        if (!is_dir($productFolder)) {
            return 'https://placehold.co/' . $width . 'x' . $height . '/e5e7eb/a3a3a3.png?text=' . urlencode($brandSlug);
        }
        
        // Lấy file ảnh đầu tiên trong thư mục
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif'];
        $files = scandir($productFolder);
        
        foreach ($files as $file) {
            if ($file === '.' || $file === '..') continue;
            
            $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
            if (in_array($extension, $allowedExtensions)) {
                // Trả về URL ảnh đầu tiên tìm được
                return getImageUrl('products/' . $folderName . '/' . $file, 'product', $width, $height);
            }
        }
        
        // Nếu không tìm thấy ảnh nào, trả về placeholder
        return 'https://placehold.co/' . $width . 'x' . $height . '/e5e7eb/a3a3a3.png?text=' . urlencode($brandSlug);
    }
} 

// Return star
function getStarRating($rating)
{
    $fullStars = floor($rating);
    $halfStar = ($rating - $fullStars) >= 0.5 ? 1 : 0;
    $emptyStars = 5 - $fullStars - $halfStar;
    
    $stars = '';
    for ($i = 0; $i < $fullStars; $i++) {
        $stars .= '<i class="fas fa-star"></i>';
    }
    if ($halfStar) {
        $stars .= '<i class="fas fa-star-half"></i>';
    }
    for ($i = 0; $i < $emptyStars; $i++) {
        $stars .= '<i class="far fa-star"></i>';
    }
    
    return $stars;
}