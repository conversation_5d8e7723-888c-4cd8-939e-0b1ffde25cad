<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Account extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'phone',
        'status',
        'account_type',
        'id_number',
        'address',
        'bank_name',
        'bank_account',
        'bank_branch',
        'distributor_code',
        'newsletter',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'newsletter' => 'boolean',
        ];
    }

    /**
     * Kiểm tra nếu tài khoản là đại lý
     *
     * @return bool
     */
    public function isDistributor(): bool
    {
        return $this->account_type === 'distributor';
    }

    /**
     * Kiểm tra nếu tài khoản là tài khoản thường
     *
     * @return bool
     */
    public function isRegular(): bool
    {
        return $this->account_type === 'regular';
    }

    /**
     * Kiểm tra nếu email đã được xác thực
     *
     * @return bool
     */
    public function hasVerifiedEmail(): bool
    {
        return $this->email_verified_at !== null;
    }
}
