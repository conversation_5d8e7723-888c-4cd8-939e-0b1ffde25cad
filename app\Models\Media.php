<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Services\MediaService;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'file_name',
        'mime_type',
        'size',
        'disk',
        'directory',
        'path',
        'extension',
        'alt',
        'title',
        'description',
        'width',
        'height',
    ];

    protected $appends = [
        'url',
        'thumbnail_url',
    ];

    /**
     * Lấy URL của file
     */
    public function getUrlAttribute()
    {
        return Storage::disk($this->disk)->url($this->path);
    }

    /**
     * Lấy URL của thumbnail
     */
    public function getThumbnailUrlAttribute()
    {
        $mediaService = app(MediaService::class);
        $thumbnailPath = $mediaService->getThumbnailPath($this);
        
        if (Storage::disk($this->disk)->exists($thumbnailPath)) {
            return Storage::disk($this->disk)->url($thumbnailPath);
        }
        
        return $this->url;
    }

    /**
     * Xóa file khi xóa model (chỉ xóa file, không gọi MediaService->delete)
     */
    protected static function boot()
    {
        parent::boot();
        
        static::deleting(function ($media) {
            // Xóa file ảnh gốc
            if (Storage::disk($media->disk)->exists($media->path)) {
                Storage::disk($media->disk)->delete($media->path);
            }
            
            // Xóa thumbnail
            $mediaService = app(MediaService::class);
            $thumbnailPath = $mediaService->getThumbnailPath($media);
            
            if (Storage::disk($media->disk)->exists($thumbnailPath)) {
                Storage::disk($media->disk)->delete($thumbnailPath);
            }
        });
    }
} 