<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Đang tạo dữ liệu mẫu cho đơn hàng...');
        
        // Kiểm tra xem có sản phẩm nào chưa
        $productsCount = Product::count();
        if ($productsCount === 0) {
            $this->command->warn('Không có sản phẩm nào trong cơ sở dữ liệu. Đang tạo sản phẩm mẫu...');
            Product::factory(10)->create();
            $this->command->info('Đã tạo 10 sản phẩm mẫu.');
        }
        
        // Kiểm tra xem có người dùng nào chưa
        $usersCount = User::count();
        if ($usersCount === 0) {
            $this->command->warn('Không có người dùng nào trong cơ sở dữ liệu ngoài admin. Đang tạo người dùng mẫu...');
            User::factory(5)->create();
            $this->command->info('Đã tạo 5 người dùng mẫu.');
        }
        
        // Tạo 50 đơn hàng, mỗi đơn hàng có 1-5 sản phẩm
        Order::factory(50)
            ->create()
            ->each(function (Order $order) {
                // Lấy số lượng sản phẩm ngẫu nhiên cho mỗi đơn hàng
                $detailCount = rand(1, 5);
                $this->command->info("Đang tạo {$detailCount} sản phẩm cho đơn hàng {$order->order_number}");
                
                // Lấy các sản phẩm ngẫu nhiên
                $products = Product::inRandomOrder()->take($detailCount)->get();
                
                // Tạo chi tiết đơn hàng
                $total = 0;
                $bvTotal = 0;
                
                foreach ($products as $product) {
                    $quantity = rand(1, 3);
                    $price = $product->price;
                    $subtotal = $price * $quantity;
                    
                    // Tính BV cho sản phẩm này
                    $bvRate = mt_rand(3, 15) / 100; // 3-15%
                    $bv = round($price * $bvRate * $quantity);
                    
                    $bvTotal += $bv;
                    $total += $subtotal;
                    
                    // Tạo chi tiết đơn hàng
                    OrderDetail::create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'product_code' => 'SKU-' . strtoupper(substr(md5($product->id), 0, 8)),
                        'product_image' => $product->image,
                        'quantity' => $quantity,
                        'price' => $price,
                        'subtotal' => $subtotal,
                        'options' => json_encode([
                            'color' => array_rand(array_flip(['Đỏ', 'Xanh', 'Vàng', 'Đen', 'Trắng'])),
                            'size' => array_rand(array_flip(['S', 'M', 'L', 'XL'])),
                        ]),
                        'bv' => $bv,
                    ]);
                }
                
                // Cập nhật tổng tiền, pv, bv cho đơn hàng
                $tax = round($total * 0.1); // 10% VAT
                $shipping = mt_rand(20000, 100000);
                $discount = round($total * mt_rand(0, 10) / 100); // 0-10% giảm giá
                
                $order->update([
                    'total_amount' => $total + $tax + $shipping - $discount,
                    'tax' => $tax,
                    'shipping_cost' => $shipping,
                    'discount' => $discount,
                    'bv_total' => $bvTotal,
                ]);
            });
        
        $this->command->info('Đã tạo 50 đơn hàng mẫu thành công!');
    }
} 