<?php

namespace App\Services;

use App\Models\LogAction;
use App\Models\User;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Log;

class LoginLogService
{
    /**
     * Ghi log khi login thành công
     */
    public function logSuccessfulLogin(User $user): void
    {
        try {
            LogAction::create([
                'user_id' => $user->id,
                'model_type' => User::class,
                'model_id' => $user->id,
                'action' => 'login_success',
                'old_data' => null,
                'new_data' => [
                    'email' => $user->email,
                    'name' => $user->name,
                    'login_at' => now()->toDateTimeString(),
                ],
                'ip_address' => Request::ip(),
                'user_agent' => Request::userAgent(),
            ]);

            Log::info('User logged in successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => Request::ip(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log successful login', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);
        }
    }

    /**
     * Ghi log khi login thất bại
     */
    public function logFailedLogin(string $email, string $reason = 'invalid_credentials'): void
    {
        try {
            LogAction::create([
                'user_id' => null,
                'model_type' => User::class,
                'model_id' => null,
                'action' => 'login_failed',
                'old_data' => null,
                'new_data' => [
                    'email' => $email,
                    'reason' => $reason,
                    'failed_at' => now()->toDateTimeString(),
                ],
                'ip_address' => Request::ip(),
                'user_agent' => Request::userAgent(),
            ]);

            Log::warning('User login failed', [
                'email' => $email,
                'reason' => $reason,
                'ip' => Request::ip(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log failed login', [
                'error' => $e->getMessage(),
                'email' => $email,
            ]);
        }
    }

    /**
     * Ghi log khi logout
     */
    public function logLogout(User $user): void
    {
        try {
            LogAction::create([
                'user_id' => $user->id,
                'model_type' => User::class,
                'model_id' => $user->id,
                'action' => 'logout',
                'old_data' => null,
                'new_data' => [
                    'email' => $user->email,
                    'name' => $user->name,
                    'logout_at' => now()->toDateTimeString(),
                ],
                'ip_address' => Request::ip(),
                'user_agent' => Request::userAgent(),
            ]);

            Log::info('User logged out', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => Request::ip(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log logout', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
            ]);
        }
    }
} 