<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Setting;

class UpdateCompanyInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'company:update-info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cập nhật thông tin công ty Midsun Vietnam';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Bắt đầu cập nhật thông tin công ty...');

        // Danh sách thông tin cần cập nhật
        $settings = [
            // Thông tin chung
            'site_title' => 'Midsun Vietnam - Sống khỏe - Sống đẹp - Sống hạnh phúc',
            'site_description' => 'Sống khỏe - Sống đẹp - Sống hạnh phúc',
            'site_logo' => 'https://drive.google.com/file/d/1NXlBhfvt-PYtQoFR229b6IcPsyobQk56/view?usp=sharing',
            'site_copyright' => 'Copyright @ 2025 Midsun Vietnam.',
            'company_slogan' => 'Sống khỏe - Sống đẹp - Sống hạnh phúc.',
            'site_company_name' => 'Midsun Vietnam',
            'site_phone' => '0867719186',
            'site_email' => '<EMAIL>',
            
            // SEO
            'site_name' => 'Midsun Vietnam',
            
            // Mạng xã hội
            'facebook_url' => 'https://www.facebook.com/midsunvietnam',
            'youtube_url' => 'https://www.youtube.com/midsunvietnam', 
            'instagram_url' => 'https://www.instagram.com/midsunvietnam',
            
            // Liên hệ
            'contact_phone' => '0867719186',
            'contact_email' => '<EMAIL>',
            
            // Footer
            'footer_about' => 'Midsun Vietnam là công ty chuyên cung cấp các sản phẩm và dịch vụ chăm sóc sức khỏe, làm đẹp toàn diện. Với slogan "Sống khỏe - Sống đẹp - Sống hạnh phúc", chúng tôi cam kết mang đến cuộc sống tốt đẹp hơn cho mọi người.',
            
            // Email config  
            'mail_host' => 'smtp.gmail.com',
            'mail_port' => '587',
            'mail_username' => '<EMAIL>',
            'mail_from_address' => '<EMAIL>',
            'mail_from_name' => 'Midsun Vietnam',
        ];

        $updated = 0;
        $created = 0;

        foreach ($settings as $key => $value) {
            $setting = Setting::where('name', $key)->first();
            
            if ($setting) {
                $setting->update(['value' => $value]);
                $updated++;
                $this->line("✅ Cập nhật: {$key}");
            } else {
                Setting::create([
                    'name' => $key,
                    'value' => $value,
                    'type' => 'string',
                    'group' => 'Thông tin chung',
                    'description' => "Tự động tạo: {$key}"
                ]);
                $created++;
                $this->line("➕ Tạo mới: {$key}");
            }
        }

        $this->newLine();
        $this->info("🎉 Hoàn thành!");
        $this->info("📊 Thống kê:");
        $this->info("   - Đã cập nhật: {$updated} setting");
        $this->info("   - Đã tạo mới: {$created} setting");
        $this->newLine();
        $this->warn("💡 Lưu ý: Hãy chạy 'php artisan cache:clear' để xóa cache settings");

        return 0;
    }
}
