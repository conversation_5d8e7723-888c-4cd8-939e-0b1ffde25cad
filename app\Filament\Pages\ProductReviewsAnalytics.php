<?php

namespace App\Filament\Pages;

use App\Models\ProductReview;
use App\Models\Product;
use Filament\Pages\Page;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\StatsOverviewWidget;
use Illuminate\Support\Facades\DB;

class ProductReviewsAnalytics extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static string $view = 'filament.pages.product-reviews-analytics';

    protected static ?string $navigationLabel = 'Thống kê bình luận';

    protected static ?string $title = 'Thống kê & Phân tích bình luận sản phẩm';

    protected static ?string $navigationGroup = 'Sản phẩm';

    protected static ?int $navigationSort = 4;

    public function getWidgets(): array
    {
        return [
            ProductReviewsAnalyticsStatsWidget::class,
            ProductReviewsRatingChartWidget::class,
            ProductReviewsTimelineChartWidget::class,
        ];
    }
}

class ProductReviewsAnalyticsStatsWidget extends StatsOverviewWidget
{
    protected function getStats(): array
    {
        $totalReviews = ProductReview::count();
        $averageRating = ProductReview::avg('rating');
        $responseRate = $totalReviews > 0 ? (ProductReview::whereNotNull('admin_reply')->count() / $totalReviews) * 100 : 0;
        $verifiedPurchases = ProductReview::where('verified_purchase', true)->count();

        return [
            StatsOverviewWidget\Stat::make('Tổng bình luận', $totalReviews)
                ->description('Tất cả bình luận từ khách hàng')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('primary'),

            StatsOverviewWidget\Stat::make('Đánh giá TB', number_format($averageRating, 2))
                ->description('Điểm đánh giá trung bình')
                ->descriptionIcon('heroicon-m-star')
                ->color($averageRating >= 4 ? 'success' : ($averageRating >= 3 ? 'warning' : 'danger')),

            StatsOverviewWidget\Stat::make('Tỷ lệ phản hồi', number_format($responseRate, 1) . '%')
                ->description('Phần trăm bình luận đã được phản hồi')
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color($responseRate >= 80 ? 'success' : ($responseRate >= 60 ? 'warning' : 'danger')),

            StatsOverviewWidget\Stat::make('Đã mua hàng', $verifiedPurchases)
                ->description('Bình luận từ khách hàng đã mua')
                ->descriptionIcon('heroicon-m-check-badge')
                ->color('info'),
        ];
    }
}

class ProductReviewsRatingChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Phân bố đánh giá theo sao';

    protected function getData(): array
    {
        $ratingCounts = ProductReview::select('rating', DB::raw('count(*) as count'))
            ->groupBy('rating')
            ->orderBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        $labels = [];
        $data = [];
        
        for ($i = 1; $i <= 5; $i++) {
            $labels[] = $i . ' sao';
            $data[] = $ratingCounts[$i] ?? 0;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Số lượng đánh giá',
                    'data' => $data,
                    'backgroundColor' => [
                        'rgb(239, 68, 68)',   // 1 sao - đỏ
                        'rgb(245, 158, 11)',  // 2 sao - cam
                        'rgb(156, 163, 175)', // 3 sao - xám
                        'rgb(34, 197, 94)',   // 4 sao - xanh lá
                        'rgb(16, 185, 129)',  // 5 sao - xanh lá đậm
                    ],
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }
}

class ProductReviewsTimelineChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Xu hướng bình luận theo thời gian';

    protected function getData(): array
    {
        $reviews = ProductReview::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('count(*) as count')
            )
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'datasets' => [
                [
                    'label' => 'Số bình luận',
                    'data' => $reviews->pluck('count')->toArray(),
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'fill' => true,
                ],
            ],
            'labels' => $reviews->pluck('date')->map(fn($date) => 
                \Carbon\Carbon::parse($date)->format('d/m')
            )->toArray(),
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }
}
