<div class="product-card bg-white rounded-xl shadow-product hover:shadow-post transition-shadow duration-300 overflow-hidden group h-full flex flex-col">
    <div class="relative">
        <!-- Product Image -->
        <div class="">
            <a href="{{ route('products.show', $product->slug) }}" class="aspect-square overflow-hidden">
                <img src="{{ $product->image_url ? asset('storage/' . $product->image_url) : asset('images/image-product-1.png') }}"
                     alt="{{ $product->name }}"
                     class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300" />
            </a>
        </div>

        <!-- Badges -->
        <div class="absolute top-4 left-4 flex flex-col gap-1">
            @if($product->discount > 0)
                <span class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1">
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z" fill="white" />
                    </svg>
                    -{{ $product->discount }}%
                </span>
            @endif
            @if($product->is_featured)
                <span class="bg-primary-badge2 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 1L7.5 4.5H11L8.25 6.75L9.75 10.25L6 8L2.25 10.25L3.75 6.75L1 4.5H4.5L6 1Z" fill="white" />
                    </svg>
                    Nổi bật
                </span>
            @endif
            @if($product->is_best_seller)
                <span class="bg-primary-badge3 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 1.5C3.24 1.5 1 3.74 1 6.5C1 9.26 3.24 11.5 6 11.5C8.76 11.5 11 9.26 11 6.5C11 3.74 8.76 1.5 6 1.5ZM6.165 9C6.075 9.03 5.92 9.03 5.83 9C5.05 8.735 3.3 7.62 3.3 5.73C3.3 4.895 3.97 4.22 4.8 4.22C5.29 4.22 5.725 4.455 6 4.825C6.27 4.46 6.71 4.22 7.2 4.22C8.03 4.22 8.7 4.895 8.7 5.73C8.7 7.62 6.95 8.735 6.165 9Z" fill="white" />
                    </svg>
                    Bán chạy
                </span>
            @endif
        </div>
    </div>

    <!-- Product Info -->
    <div class="px-4 pb-4 flex flex-col flex-grow">
        <div>
            <span class="text-sm text-primary-gray font-medium">{{ $product->category ? $product->category->name : 'Thiết bị âm thanh' }}</span>
        </div>
        <h3 class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4 flex-grow">
            <a href="{{ route('products.show', $product->slug) }}" class="text-primary-base hover:text-secondary-main transition-colors">
                {{ $product->name }}
            </a>
        </h3>
        <div class="flex items-center justify-between gap-1 mt-auto">
            <div class="flex flex-col">
                <div class="flex items-center gap-2">
                    <span class="text-primary-price font-bold text-lg">{{ number_format($product->sale_price ?: $product->price) }}đ</span>
                </div>
                @if($product->price && $product->sale_price < $product->price)
                    <span class="text-primary-gray font-medium line-through">{{ number_format($product->price) }}đ</span>
                @endif
            </div>
            <!-- Add to Cart Button -->
            @livewire('add-to-cart-button', [
                'productId' => $product->id,
                'showQuantity' => false,
            ])
        </div>
    </div>
</div>
