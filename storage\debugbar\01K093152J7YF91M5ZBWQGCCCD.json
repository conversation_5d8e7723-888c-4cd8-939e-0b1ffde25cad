{"__meta": {"id": "01K093152J7YF91M5ZBWQGCCCD", "datetime": "2025-07-16 14:43:50", "utime": **********.355162, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752651829.682027, "end": **********.355194, "duration": 0.6731669902801514, "duration_str": "673ms", "measures": [{"label": "Booting", "start": 1752651829.682027, "relative_start": 0, "end": **********.079807, "relative_end": **********.079807, "duration": 0.****************, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.079828, "relative_start": 0.*****************, "end": **********.355196, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.250935, "relative_start": 0.****************, "end": **********.254073, "relative_end": **********.254073, "duration": 0.003137826919555664, "duration_str": "3.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.section.index", "start": **********.348924, "relative_start": 0.****************, "end": **********.348924, "relative_end": **********.348924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.heading", "start": **********.350734, "relative_start": 0.****************, "end": **********.350734, "relative_end": **********.350734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.351558, "relative_start": 0.6695308685302734, "end": **********.351558, "relative_end": **********.351558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.352707, "relative_start": 0.6706798076629639, "end": **********.353406, "relative_end": **********.353406, "duration": 0.0006990432739257812, "duration_str": "699μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41280984, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "filament::components.section.index", "param_count": null, "params": [], "start": **********.348892, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.section.heading", "param_count": null, "params": [], "start": **********.350704, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/heading.blade.phpfilament::components.section.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": **********.351541, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02914, "accumulated_duration_str": "29.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.271543, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 11.977}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.283794, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 11.977, "width_percent": 4.29}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.28644, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 16.266, "width_percent": 6.074}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-05'", "type": "query", "params": [], "bindings": ["2025-07-05"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.289652, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 22.34, "width_percent": 3.466}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.291615, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 25.806, "width_percent": 2.025}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.292875, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 27.831, "width_percent": 1.613}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.294248, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 29.444, "width_percent": 3.5}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2961712, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 32.944, "width_percent": 1.579}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2973082, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 34.523, "width_percent": 1.51}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2984211, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 36.033, "width_percent": 1.647}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3001611, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 37.68, "width_percent": 4.633}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3026721, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 42.313, "width_percent": 2.745}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3044379, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 45.058, "width_percent": 2.059}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.305837, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 47.117, "width_percent": 1.441}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.307011, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 48.559, "width_percent": 1.99}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3087158, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 50.549, "width_percent": 3.226}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.310588, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 53.775, "width_percent": 1.887}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.311963, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 55.662, "width_percent": 1.853}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3132951, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 57.515, "width_percent": 1.75}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.315192, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 59.266, "width_percent": 4.598}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3173938, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 63.864, "width_percent": 2.093}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.318812, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 65.957, "width_percent": 1.784}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3204622, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 67.742, "width_percent": 2.128}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.322154, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 69.87, "width_percent": 3.089}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.323889, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 72.958, "width_percent": 1.819}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.325301, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 74.777, "width_percent": 1.407}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.326495, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 76.184, "width_percent": 1.441}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3277152, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 77.625, "width_percent": 1.579}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.329314, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 79.204, "width_percent": 3.294}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.33146, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 82.498, "width_percent": 3.157}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.333251, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 85.655, "width_percent": 1.956}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3345878, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 87.612, "width_percent": 1.476}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.335973, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 89.087, "width_percent": 4.049}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3380191, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 93.137, "width_percent": 1.75}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3392131, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 94.887, "width_percent": 1.544}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.340308, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 96.431, "width_percent": 1.784}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.341564, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 98.216, "width_percent": 1.784}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.treatments-chart #vxxoICav8IEhI9e0BGZh": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"f4e874ac94dc628623a611ebc2c26228\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.treatments-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\TreatmentsChart\"\n  \"id\" => \"vxxoICav8IEhI9e0BGZh\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\TreatmentsChart@updateChartData<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "674ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1098218005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1098218005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1912255599 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"331 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;f4e874ac94dc628623a611ebc2c26228&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;vxxoICav8IEhI9e0BGZh&quot;,&quot;name&quot;:&quot;app.filament.widgets.treatments-chart&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;5ba64eaf3fa50ee7e23e97d73d357a61ca4f97162fa6760d36eff791c34c87ab&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1912255599\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1517603234 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">531</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlFodU55emNwQ0liTmhaTDg1MzdwWFE9PSIsInZhbHVlIjoibTVRNU5UaEZuNU8ySkZpRG9iaCtRUXdSRXp0dzNBVlVGc1IvZ081VXFBcE5sYk4ybWUrcnRXY2lRY3EzTk5OZ0RwQ3hLYkNUN0xxamN1SVZNMHhacWZralF0UksxdnhGQTU0cVBGTXhTcEZ2alV3UmpmMStHL2FPWXAxLzBtQWUiLCJtYWMiOiJiNGEzMDI0YjBjYThjYmUyMGIxZjI2NDFlNmRlNjQ5YzQ0YzQ2YTAwOGEzZDkwMjg4NWQ2M2UwOWQ4YWZhM2Y0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjY3OVJFQ3M0QkNMOGhoT2NNbmh6SGc9PSIsInZhbHVlIjoiUndjR0RMeHFQOGdFckhrSHA4eTAwTjg4NDEwSG1rRWQ4NXgrOHFDUHQwcWpYSHptaXpET3kyak9WVjRxWTJDUUtYdC84UDJvaWJTRElPeHpMYlBCL3p6dllaQ3AwdDVmOVRMUFBmNVJIdm5tNXlOT2RWVW1zR3JZOTI4Y1pWSGQiLCJtYWMiOiIwOTIyZWMyYTViMTQ2ZGFkNjRkNmE4NWJkNDhiMzE3YjMyNzUwNzVjMWE5ZWE3ZjE1YmJiNWI1ODJhOGU2YTQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517603234\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SxYEiPXdkNcyfymjyeN8wk4RbgrpbsugedMWghRH</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1405161233 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:43:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1405161233\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1547253164 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FctHlRISTc11rErvZ26sBKKW7PHrKWoJTvXGsWsC</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$GgKaDmjdfWlNzTUrcO1K5.P1hXVA9J4EX3bYW.ogCY4SrBpQeXIn2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547253164\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}