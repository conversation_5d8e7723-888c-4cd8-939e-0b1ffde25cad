<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Đảm bảo xóa bỏ cột product_reviews nếu còn tồn tại
            if (Schema::hasColumn('products', 'product_reviews')) {
                $table->dropColumn('product_reviews');
            }

            // Đ<PERSON>m bảo tất cả các trường tổng hợp đánh giá tồn tại
            if (!Schema::hasColumn('products', 'reviews_count')) {
                $table->integer('reviews_count')->default(0);
            }
            
            if (!Schema::hasColumn('products', 'average_rating')) {
                $table->float('average_rating', 2, 1)->default(0);
            }
            
            if (!Schema::hasColumn('products', 'rating_distribution')) {
                $table->json('rating_distribution')->nullable();
            }
        });
        
        // Đảm bảo tất cả sản phẩm đều có phân phối rating
        DB::statement("
            UPDATE products 
            SET rating_distribution = JSON_OBJECT('5', 0, '4', 0, '3', 0, '2', 0, '1', 0) 
            WHERE rating_distribution IS NULL OR rating_distribution = 'null' OR rating_distribution = ''
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Không cần rollback, vì đây là tối ưu hóa cấu trúc
    }
};
