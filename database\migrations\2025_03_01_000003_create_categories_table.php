<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên danh mục
            $table->string('slug')->unique(); // Slug cho danh mục
            $table->text('description')->nullable();
            $table->enum('type', ['post', 'product']); // Loại danh mục (post, product)
            $table->unsignedBigInteger('parent_id')->default(0); // Changed this line
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->string('seo_title')->nullable();
            $table->text('seo_description')->nullable();
            $table->string('seo_keywords')->nullable();
            $table->string('thumbnail')->nullable();
            $table->string('lang', 5)->default('vi');
            $table->timestamps();

            // Add index for better performance
            $table->index(['type', 'status']);
            $table->index('parent_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
