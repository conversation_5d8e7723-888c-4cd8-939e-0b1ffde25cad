<?php

namespace App\Livewire;

use Livewire\Component;

class Notification extends Component
{
    public $message = '';
    public $type = '';
    public $show = false;
    
    protected $listeners = ['showNotification'];
    
    public function showNotification($data)
    {
        $this->message = $data['message'] ?? '';
        $this->type = $data['type'] ?? 'info';
        $this->show = true;
    }
    
    public function hideNotification()
    {
        $this->show = false;
        $this->message = '';
        $this->type = '';
    }
    
    public function render()
    {
        return view('livewire.notification');
    }
} 