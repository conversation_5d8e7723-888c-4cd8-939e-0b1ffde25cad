<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    public function index()
    {
        // Breadcrumbs cho trang danh sách đơn hàng
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Tài khoản',
                    'url' => route('profile.edit'),
                    'active' => false
                ],
                [
                    'name' => 'Lịch sử đơn hàng',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Lịch sử đơn hàng'
        ];

        // Lấy thông tin người dùng hiện tại từ guard frontend
        $user = Auth::guard('frontend')->user();
        
        // L<PERSON><PERSON> danh sách đơn hàng của người dùng, sắp xếp theo thời gian tạo mới nhất
        $orders = Order::where('user_id', $user->id)
                      ->orderBy('created_at', 'desc')
                      ->paginate(10);
        
        return view('templates.auvista.account.orders', compact('orders', 'breadcrumbs'));
    }

    public function show($id)
    {
        // Lấy thông tin người dùng hiện tại từ guard frontend
        $user = Auth::guard('frontend')->user();
        
        // Lấy chi tiết đơn hàng, đảm bảo đơn hàng thuộc về người dùng hiện tại
        $order = Order::where('id', $id)
                     ->where('user_id', $user->id)
                     ->firstOrFail();

        // Breadcrumbs cho trang chi tiết đơn hàng
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Tài khoản',
                    'url' => route('profile.edit'),
                    'active' => false
                ],
                [
                    'name' => 'Lịch sử đơn hàng',
                    'url' => route('orders.index'),
                    'active' => false
                ],
                [
                    'name' => 'Đơn hàng #' . $order->order_number,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Đơn hàng #' . $order->order_number
        ];
        
        return view('templates.auvista.account.order-detail', compact('order', 'breadcrumbs'));
    }

    public function cancel($id)
    {
        // Lấy thông tin người dùng hiện tại từ guard frontend
        $user = Auth::guard('frontend')->user();
        
        // Lấy đơn hàng, đảm bảo đơn hàng thuộc về người dùng hiện tại
        $order = Order::where('id', $id)
                     ->where('user_id', $user->id)
                     ->firstOrFail();
        
        // Kiểm tra xem đơn hàng có thể được hủy không (chỉ hủy được khi đơn hàng đang ở trạng thái chờ xác nhận)
        if ($order->status === 'pending') {
            $order->status = 'cancelled';
            $order->save();
            
            return redirect()->route('orders.show', $id)->with('success', 'Đơn hàng đã được hủy thành công.');
        }
        
        return redirect()->route('orders.show', $id)->with('error', 'Không thể hủy đơn hàng ở trạng thái hiện tại.');
    }
    
    public function store(Request $request)
    {
        // Validate dữ liệu đầu vào
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'note' => 'nullable|string',
            'payment_method' => 'required|string|in:cod,bank_transfer',
        ]);
        
        // Kiểm tra xem giỏ hàng có sản phẩm không
        $cartItems = session('cart', []);
        if (empty($cartItems)) {
            return redirect()->back()->with('error', 'Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm trước khi thanh toán.');
        }
        
        // Lấy thông tin người dùng hiện tại từ guard frontend
        $user = Auth::guard('frontend')->user();
        
        // Tính tổng tiền
        $totalAmount = 0;
        foreach ($cartItems as $productId => $quantity) {
            $product = Product::find($productId);
            if ($product) {
                $totalAmount += $product->price * $quantity;
            }
        }
        
        // Phí vận chuyển và thuế (có thể điều chỉnh tùy theo logic kinh doanh)
        $shippingCost = 0; // Miễn phí vận chuyển
        $tax = 0; // Thuế đã bao gồm trong giá
        
        // Tạo đơn hàng mới
        $order = new Order();
        $order->user_id = $user ? $user->id : null;
        $order->order_number = 'ORD-' . strtoupper(Str::random(8));
        $order->total_amount = $totalAmount + $shippingCost + $tax;
        $order->shipping_cost = $shippingCost;
        $order->tax = $tax;
        $order->status = 'pending';
        $order->payment_status = 'pending';
        $order->payment_method = $validated['payment_method'];
        
        // Thông tin giao hàng
        $order->shipping_fullname = $validated['name'];
        $order->shipping_address = $validated['address'];
        $order->shipping_city = ''; // Có thể bổ sung thêm
        $order->shipping_phone = $validated['phone'];
        $order->shipping_email = $validated['email'];
        $order->note = $validated['note'] ?? null;
        
        $order->save();
        
        // Lưu chi tiết đơn hàng
        foreach ($cartItems as $productId => $quantity) {
            $product = Product::find($productId);
            if ($product) {
                $orderDetail = new OrderDetail();
                $orderDetail->order_id = $order->id;
                $orderDetail->product_id = $product->id;
                $orderDetail->product_name = $product->name;
                $orderDetail->quantity = $quantity;
                $orderDetail->price = $product->price;
                $orderDetail->subtotal = $product->price * $quantity;
                $orderDetail->save();
            }
        }
        
        // Xóa giỏ hàng sau khi đặt hàng thành công
        Session::forget('cart');
        
        return redirect()->route('orders.success', $order->id)->with('success', 'Đơn hàng của bạn đã được đặt thành công!');
    }
    
    public function success($id)
    {
        // Lấy đơn hàng với chi tiết sản phẩm
        $order = Order::with('details.product')->findOrFail($id);
        
        return view('templates.auvista.pages.order-success', compact('order'));
    }
}
