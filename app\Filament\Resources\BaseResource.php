<?php

namespace App\Filament\Resources;

use Filament\Resources\Resource;

abstract class BaseResource extends Resource
{
    public static function canAccess(): bool
    {
        $user = auth()->user();
        
        // Cho phép super-admin truy cập không cần check quyền
        if ($user->roles->contains('id', 1)) {
            return true;
        }

        // Lấy tên resource hiện tại (ví dụ: menu, post, page...)
        $resource = strtolower(str_replace('Resource', '', class_basename(static::class)));

        // Kiểm tra quyền dựa trên tên resource
        return $user->canAny([
            "{$resource}.view",
            "{$resource}.create", 
            "{$resource}.edit",
            "{$resource}.delete"
        ]);
    }
}