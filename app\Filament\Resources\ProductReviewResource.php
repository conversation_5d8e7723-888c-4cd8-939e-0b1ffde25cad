<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductReviewResource\Pages;
use App\Models\ProductReview;
use App\Models\Product;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class ProductReviewResource extends Resource
{
    protected static ?string $model = ProductReview::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Quản lý bình luận';

    protected static ?string $modelLabel = 'Bình luận sản phẩm';

    protected static ?string $pluralModelLabel = 'Bình luận sản phẩm';

    protected static ?string $navigationGroup = 'Sản phẩm';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(2)
                    ->schema([
                        Section::make('Thông tin bình luận')
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Sản phẩm')
                                    ->relationship('product', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),

                                Forms\Components\Select::make('user_id')
                                    ->label('Người dùng')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->nullable(),

                                Forms\Components\TextInput::make('reviewer_name')
                                    ->label('Tên người đánh giá')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('reviewer_email')
                                    ->label('Email người đánh giá')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('reviewer_phone')
                                    ->label('Số điện thoại')
                                    ->tel()
                                    ->maxLength(20),

                                Forms\Components\Select::make('rating')
                                    ->label('Đánh giá sao')
                                    ->options([
                                        1 => '1 sao - Rất tệ',
                                        2 => '2 sao - Tệ',
                                        3 => '3 sao - Trung bình',
                                        4 => '4 sao - Tốt',
                                        5 => '5 sao - Xuất sắc',
                                    ])
                                    ->required(),

                                Forms\Components\Textarea::make('comment')
                                    ->label('Nội dung bình luận')
                                    ->required()
                                    ->rows(4)
                                    ->columnSpanFull(),
                            ])
                            ->columnSpan(1),

                        Section::make('Phản hồi & Trạng thái')
                            ->schema([
                                Forms\Components\Textarea::make('admin_reply')
                                    ->label('Phản hồi của admin')
                                    ->rows(4)
                                    ->columnSpanFull(),

                                Forms\Components\Select::make('admin_id')
                                    ->label('Admin phản hồi')
                                    ->relationship('admin', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->nullable(),

                                Forms\Components\DateTimePicker::make('admin_reply_at')
                                    ->label('Thời gian phản hồi')
                                    ->nullable(),

                                Forms\Components\Toggle::make('recommend')
                                    ->label('Đề xuất sản phẩm')
                                    ->default(false),

                                Forms\Components\Toggle::make('verified_purchase')
                                    ->label('Đã mua hàng')
                                    ->default(false),

                                Forms\Components\TextInput::make('helpful_count')
                                    ->label('Số lượt hữu ích')
                                    ->numeric()
                                    ->default(0),

                                Forms\Components\Toggle::make('status')
                                    ->label('Hiển thị')
                                    ->default(true),
                            ])
                            ->columnSpan(1),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Sản phẩm')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('reviewer_name')
                    ->label('Người đánh giá')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('rating')
                    ->label('Đánh giá')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '1' => 'danger',
                        '2' => 'warning',
                        '3' => 'gray',
                        '4' => 'success',
                        '5' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => $state . ' ⭐')
                    ->sortable(),

                Tables\Columns\TextColumn::make('comment')
                    ->label('Bình luận')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\IconColumn::make('status')
                    ->label('Hiển thị')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('admin_reply')
                    ->label('Đã phản hồi')
                    ->boolean()
                    ->getStateUsing(fn ($record) => !empty($record->admin_reply)),

                Tables\Columns\TextColumn::make('helpful_count')
                    ->label('Hữu ích')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('rating')
                    ->label('Đánh giá sao')
                    ->options([
                        1 => '1 sao',
                        2 => '2 sao',
                        3 => '3 sao',
                        4 => '4 sao',
                        5 => '5 sao',
                    ]),

                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        1 => 'Hiển thị',
                        0 => 'Ẩn',
                    ]),

                Filter::make('has_admin_reply')
                    ->label('Đã có phản hồi')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('admin_reply')),

                Filter::make('no_admin_reply')
                    ->label('Chưa có phản hồi')
                    ->query(fn (Builder $query): Builder => $query->whereNull('admin_reply')),

                SelectFilter::make('product')
                    ->label('Sản phẩm')
                    ->relationship('product', 'name')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('reply')
                    ->label('Phản hồi')
                    ->icon('heroicon-o-chat-bubble-left')
                    ->color('info')
                    ->visible(fn ($record) => empty($record->admin_reply))
                    ->form([
                        Forms\Components\Textarea::make('admin_reply')
                            ->label('Phản hồi của bạn')
                            ->required()
                            ->rows(4)
                            ->placeholder('Nhập phản hồi cho bình luận này...'),
                    ])
                    ->action(function (array $data, $record): void {
                        $record->update([
                            'admin_reply' => $data['admin_reply'],
                            'admin_reply_at' => now(),
                            'admin_id' => auth()->id(),
                        ]);

                        // Cập nhật thống kê sản phẩm
                        $record->product->updateRatingStats();

                        Notification::make()
                            ->title('Đã thêm phản hồi thành công')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('edit_reply')
                    ->label('Sửa phản hồi')
                    ->icon('heroicon-o-pencil')
                    ->color('warning')
                    ->visible(fn ($record) => !empty($record->admin_reply))
                    ->form([
                        Forms\Components\Textarea::make('admin_reply')
                            ->label('Sửa phản hồi')
                            ->required()
                            ->rows(4)
                            ->default(fn ($record) => $record->admin_reply),
                    ])
                    ->action(function (array $data, $record): void {
                        $record->update([
                            'admin_reply' => $data['admin_reply'],
                            'admin_reply_at' => now(),
                            'admin_id' => auth()->id(),
                        ]);

                        Notification::make()
                            ->title('Đã cập nhật phản hồi thành công')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\Action::make('toggle_status')
                    ->label(fn ($record) => $record->status ? 'Ẩn' : 'Hiển thị')
                    ->icon(fn ($record) => $record->status ? 'heroicon-o-eye-slash' : 'heroicon-o-eye')
                    ->color(fn ($record) => $record->status ? 'warning' : 'success')
                    ->action(function ($record): void {
                        $record->updateStatus(!$record->status);
                        
                        Notification::make()
                            ->title($record->status ? 'Đã hiển thị bình luận' : 'Đã ẩn bình luận')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    BulkAction::make('approve')
                        ->label('Duyệt hiển thị')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each(fn ($record) => $record->updateStatus(true));

                            Notification::make()
                                ->title('Đã duyệt ' . $records->count() . ' bình luận')
                                ->success()
                                ->send();
                        }),

                    BulkAction::make('hide')
                        ->label('Ẩn bình luận')
                        ->icon('heroicon-o-eye-slash')
                        ->color('warning')
                        ->action(function (Collection $records): void {
                            $records->each(fn ($record) => $record->updateStatus(false));

                            Notification::make()
                                ->title('Đã ẩn ' . $records->count() . ' bình luận')
                                ->success()
                                ->send();
                        }),

                    BulkAction::make('mark_verified')
                        ->label('Đánh dấu đã mua hàng')
                        ->icon('heroicon-o-check-badge')
                        ->color('info')
                        ->action(function (Collection $records): void {
                            $records->each(fn ($record) => $record->update(['verified_purchase' => true]));

                            Notification::make()
                                ->title('Đã đánh dấu ' . $records->count() . ' bình luận là đã mua hàng')
                                ->success()
                                ->send();
                        }),

                    BulkAction::make('export')
                        ->label('Xuất Excel')
                        ->icon('heroicon-o-document-arrow-down')
                        ->color('gray')
                        ->action(function (Collection $records): void {
                            // Có thể implement export functionality sau
                            Notification::make()
                                ->title('Tính năng xuất Excel sẽ được phát triển')
                                ->info()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin sản phẩm')
                    ->schema([
                        Infolists\Components\TextEntry::make('product.name')
                            ->label('Tên sản phẩm')
                            ->weight('bold'),

                        Infolists\Components\TextEntry::make('product.sku')
                            ->label('Mã sản phẩm'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Thông tin người đánh giá')
                    ->schema([
                        Infolists\Components\TextEntry::make('reviewer_name')
                            ->label('Tên người đánh giá')
                            ->weight('bold'),

                        Infolists\Components\TextEntry::make('reviewer_email')
                            ->label('Email')
                            ->copyable(),

                        Infolists\Components\TextEntry::make('reviewer_phone')
                            ->label('Số điện thoại')
                            ->copyable()
                            ->placeholder('Không có'),

                        Infolists\Components\TextEntry::make('user.name')
                            ->label('Tài khoản liên kết')
                            ->placeholder('Không có'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Nội dung đánh giá')
                    ->schema([
                        Infolists\Components\TextEntry::make('rating')
                            ->label('Đánh giá sao')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                '1' => 'danger',
                                '2' => 'warning',
                                '3' => 'gray',
                                '4' => 'success',
                                '5' => 'success',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string => $state . ' ⭐'),

                        Infolists\Components\TextEntry::make('comment')
                            ->label('Bình luận')
                            ->columnSpanFull()
                            ->prose(),

                        Infolists\Components\IconEntry::make('recommend')
                            ->label('Đề xuất sản phẩm')
                            ->boolean(),

                        Infolists\Components\IconEntry::make('verified_purchase')
                            ->label('Đã mua hàng')
                            ->boolean(),

                        Infolists\Components\TextEntry::make('helpful_count')
                            ->label('Số lượt hữu ích')
                            ->numeric(),

                        Infolists\Components\IconEntry::make('status')
                            ->label('Hiển thị')
                            ->boolean(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Phản hồi từ Admin')
                    ->schema([
                        Infolists\Components\TextEntry::make('admin_reply')
                            ->label('Nội dung phản hồi')
                            ->columnSpanFull()
                            ->prose()
                            ->placeholder('Chưa có phản hồi'),

                        Infolists\Components\TextEntry::make('admin.name')
                            ->label('Admin phản hồi')
                            ->placeholder('Chưa có'),

                        Infolists\Components\TextEntry::make('admin_reply_at')
                            ->label('Thời gian phản hồi')
                            ->dateTime('d/m/Y H:i')
                            ->placeholder('Chưa có'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Thông tin hệ thống')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),

                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Cập nhật lần cuối')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductReviews::route('/'),
            'create' => Pages\CreateProductReview::route('/create'),
            'view' => Pages\ViewProductReview::route('/{record}'),
            'edit' => Pages\EditProductReview::route('/{record}/edit'),
        ];
    }
}
