<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contact extends Model
{
    use HasFactory;

    protected $table = 'contact_forms';

    protected $fillable = [
        'member_id',
        'name',
        'spouse_name',
        'email',
        'address',
        'phone',
        'subject',
        'message',
        'file_attachment',
        'privacy_consent',
        'is_newsletter',
        'status',
    ];

    /**
     * Các trạng thái của form liên hệ
     */
    const STATUS_NEW = 'new';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Lấy tên trạng thái
     */
    public function getStatusNameAttribute()
    {
        return match($this->status) {
            self::STATUS_NEW => 'Mới',
            self::STATUS_PROCESSING => 'Đang xử lý',
            self::STATUS_COMPLETED => 'Đã hoàn thành',
            self::STATUS_CANCELLED => 'Đã hủy',
            default => 'Không xác định',
        };
    }
}
