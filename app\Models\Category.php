<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'type',
        'parent_id',
        'status',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'thumbnail'
    ];

    protected $casts = [
        'status' => 'string',
        'type' => 'string',
        'parent_id' => 'integer',
    ];

    /**
     * Mutator để xử lý parent_id null
     */
    public function setParentIdAttribute($value)
    {
        $this->attributes['parent_id'] = $value === '' || $value === 0 ? 0 : (int) $value;
    }

    /**
     * Accessor để xử lý parent_id null
     */
    public function getParentIdAttribute($value)
    {
        return $value === 0 ? 0 : $value;
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Kiểm tra xem danh mục có phải là danh mục gốc không
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Lấy tất cả danh mục gốc (không có parent)
     */
    public static function getRootCategories(string $type = 'post'): \Illuminate\Database\Eloquent\Collection
    {
        return static::where('type', $type)
            ->whereNull('parent_id')
            ->orderBy('name')
            ->get();
    }

    /**
     * Lấy tất cả danh mục con
     */
    public function getAllChildren(): \Illuminate\Database\Eloquent\Collection
    {
        return $this->children()->with('children')->get();
    }

    /**
     * Kiểm tra xem có thể set parent_id thành giá trị này không
     */
    public function canSetParent($parentId): bool
    {
        // Chuyển đổi giá trị để xử lý null
        $parentId = $parentId === '' || $parentId === null ? null : (int) $parentId;
        
        // Không thể set chính nó làm parent
        if ($parentId == $this->id) {
            return false;
        }

        // Nếu parent_id là null, luôn hợp lệ
        if (is_null($parentId)) {
            return true;
        }

        // Kiểm tra xem parent_id có tồn tại không
        $parent = static::find($parentId);
        if (!$parent) {
            return false;
        }

        // Kiểm tra circular reference - không thể chọn con của mình làm parent
        $childrenIds = $this->getAllChildren()->pluck('id')->toArray();
        if (in_array($parentId, $childrenIds)) {
            return false;
        }

        return true;
    }

    /**
     * Boot method để thêm validation
     */
    protected static function boot()
    {
        parent::boot();

        // Không cần validation ở đây vì đã xử lý trong Filament Resource
    }
}