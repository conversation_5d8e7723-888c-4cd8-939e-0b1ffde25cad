<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Image Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default image "driver" used by Intervention Image.
    |
    | Supported: "gd", "imagick"
    |
    */

    'driver' => env('IMAGE_DRIVER', 'gd'),

    /*
    |--------------------------------------------------------------------------
    | Memory Limit
    |--------------------------------------------------------------------------
    |
    | This option sets the maximum memory allocation for image processing.
    | Default is 128M.
    |
    */
    
    'memory_limit' => env('IMAGE_MEMORY_LIMIT', '128M'),
    
    /*
    |--------------------------------------------------------------------------
    | Quality
    |--------------------------------------------------------------------------
    |
    | This option sets the default quality for image encodings.
    |
    */
    
    'quality' => env('IMAGE_QUALITY', 90),

];
