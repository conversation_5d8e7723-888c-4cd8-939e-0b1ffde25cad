<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('group')->nullable();
            $table->text('value')->nullable();;
            $table->text('description')->nullable();
            $table->string('type', 10, ['string', 'number', 'boolean', 'json', 'code'])->default('string');
            $table->string('lang', 5)->default('vi');
            $table->timestamps();
        });
    }

    public function down(): void {
        Schema::dropIfExists('settings');
    }
};
