<?php

namespace App\Filament\Resources\ProductReviewResource\Pages;

use App\Filament\Resources\ProductReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListProductReviews extends ListRecords
{
    protected static string $resource = ProductReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('Tất cả')
                ->badge(fn () => $this->getModel()::count()),

            'pending' => Tab::make('Chờ phản hồi')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNull('admin_reply'))
                ->badge(fn () => $this->getModel()::whereNull('admin_reply')->count())
                ->badgeColor('warning'),

            'replied' => Tab::make('Đã phản hồi')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('admin_reply'))
                ->badge(fn () => $this->getModel()::whereNotNull('admin_reply')->count())
                ->badgeColor('success'),

            'hidden' => Tab::make('Đã ẩn')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', false))
                ->badge(fn () => $this->getModel()::where('status', false)->count())
                ->badgeColor('danger'),

            'high_rating' => Tab::make('Đánh giá cao (4-5 sao)')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('rating', [4, 5]))
                ->badge(fn () => $this->getModel()::whereIn('rating', [4, 5])->count())
                ->badgeColor('success'),

            'low_rating' => Tab::make('Đánh giá thấp (1-2 sao)')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('rating', [1, 2]))
                ->badge(fn () => $this->getModel()::whereIn('rating', [1, 2])->count())
                ->badgeColor('danger'),
        ];
    }
}
