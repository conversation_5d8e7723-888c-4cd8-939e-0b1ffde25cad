<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Repositories\BannerRepository;
use App\Repositories\PostRepository;
use App\Repositories\SettingRepository;
use App\Repositories\CategoryRepository;
use App\Repositories\ProductRepository;
use App\Repositories\BrandRepository;
use App\Models\StaticPage;
use App\Traits\SeoTrait;
use App\Helpers\TranslationHelper;

class HomeController extends BaseController
{
    use SeoTrait;

    protected $bannerRepository;
    protected $postRepository;
    protected $settingRepository;
    protected $categoryRepository;
    protected $productRepository;
    protected $brandRepository;

    /**
     * HomeController constructor.
     *
     * @param BannerRepository $bannerRepository
     * @param PostRepository $postRepository
     * @param SettingRepository $settingRepository
     * @param CategoryRepository $categoryRepository
     * @param ProductRepository $productRepository
     * @param BrandRepository $brandRepository
     */
    public function __construct(
        BannerRepository $bannerRepository,
        PostRepository $postRepository,
        SettingRepository $settingRepository,
        CategoryRepository $categoryRepository,
        ProductRepository $productRepository,
        BrandRepository $brandRepository
    ) {
        $this->bannerRepository = $bannerRepository;
        $this->postRepository = $postRepository;
        $this->settingRepository = $settingRepository;
        $this->categoryRepository = $categoryRepository;
        $this->productRepository = $productRepository;
        $this->brandRepository = $brandRepository;
    }

    /**
     * Hiển thị trang chủ
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Lấy dữ liệu cho trang chủ
        $banners = $this->bannerRepository->getBanners('slider');
        $featuredCategories = $this->categoryRepository->getFeaturedCategories(3);
        $newProducts = $this->productRepository->getNewProducts(8);
        $bestSellers = $this->productRepository->getBestSellers(8);
        $featuredProducts = $this->productRepository->getFeaturedProducts(8);
        
        // Nếu không có sản phẩm nổi bật, lấy sản phẩm thường
        if ($featuredProducts->isEmpty()) {
            $featuredProducts = \App\Models\Product::where('status', true)
                ->with(['category', 'brand'])
                ->orderBy('created_at', 'desc')
                ->limit(8)
                ->get();
        }
        
        $featuredBrands = $this->brandRepository->getFeaturedBrands(3);
        $latestPosts = $this->postRepository->getPosts(null, 3);

        // Lấy dữ liệu SEO từ StaticPage
        $homePage = TranslationHelper::getTranslatedPage('home', app()->getLocale());
        // Dữ liệu SEO đặt ở cuối hàm, ngay trước return
        $seoData = $this->getDataSeo([
            'seo_title' => $homePage->seo_title ?? '',
            'seo_description' => $homePage->seo_description ?? '',
            'seo_keywords' => $homePage->seo_keywords ?? '',
            'seo_author' => $homePage->author->name ?? '',
            'seo_canonical' => route('home'),
            'seo_type' => 'website',
            'seo_image' => $homePage->image ?? ''
        ]);

        return view('templates.auvista.pages.home', compact(
            'banners',
            'featuredCategories',
            'newProducts',
            'bestSellers',
            'featuredProducts',
            'featuredBrands',
            'latestPosts'
        ) + $seoData);
    }
}
