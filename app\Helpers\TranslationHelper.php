<?php

namespace App\Helpers;

use App\Models\PageTranslation;
use App\Models\StaticPage;

class TranslationHelper
{
    /**
     * Get translated page by code and language
     */
    public static function getTranslatedPage($code, $lang = null, $type = 'static_page')
    {
        $lang = app()->getLocale() ?? 'vi';

        $translation = PageTranslation::where('type', $type)
            ->where('code', $code)
            ->where('lang', $lang)
            ->first();
        return $translation ? $translation->page : null;
    }

    public static function getTranslatedPageCode($pageId, $type = 'static_page')
    {
        $translation = PageTranslation::where('page_id', $pageId)->where('type', $type)->first();
        $newCode = $translation->code;

        return $newCode ? $newCode : null;
    }

    /**
     * Get all translations for a specific code
     */
    public static function getAllTranslations($code, $type = 'static_page')
    {
        return PageTranslation::where('type', $type)
            ->where('code', $code)
            ->with('page')
            ->get();
    }

    /**
     * Get translation URL for language switching
     */
    public static function getTranslationUrl($code, $lang, $type = 'static_page')
    {
        $translation = PageTranslation::where('type', $type)
            ->where('code', $code)
            ->where('lang', $lang)
            ->first();

        if (!$translation || !$translation->page) {
            return null;
        }

        return $translation->url;
    }

    /**
     * Get current page translation code
     */
    public static function getCurrentPageCode($pageId, $type = 'static_page')
    {
        $translation = PageTranslation::where('type', $type)
            ->where('page_id', $pageId)
            ->first();

        return $translation ? $translation->code : null;
    }

    /**
     * Get language switcher links for current page
     */
    public static function getLanguageSwitcherLinks($pageId, $type = 'static_page')
    {
        $translation = PageTranslation::where('type', $type)
            ->where('page_id', $pageId)
            ->first();

        if (!$translation) {
            return [];
        }

        $translations = PageTranslation::where('type', $type)
            ->where('code', $translation->code)
            ->with('page')
            ->get();

        $links = [];
        foreach ($translations as $trans) {
            $links[$trans->lang] = [
                'url' => $trans->url,
                'title' => $trans->page->title ?? '',
                'lang' => $trans->lang,
            ];
        }

        return $links;
    }

    /**
     * Check if page has translation in specific language
     */
    public static function hasTranslation($code, $lang, $type = 'static_page')
    {
        return PageTranslation::where('type', $type)
            ->where('code', $code)
            ->where('lang', $lang)
            ->exists();
    }

    /**
     * Get alternative language URL for current page
     */
    public static function getAlternativeLanguageUrl($currentLang, $targetLang, $type = 'static_page')
    {
        // Get current page code from session or request
        $currentCode = session('current_page_code');

        if (!$currentCode) {
            return null;
        }

        return self::getTranslationUrl($currentCode, $targetLang, $type);
    }
}
