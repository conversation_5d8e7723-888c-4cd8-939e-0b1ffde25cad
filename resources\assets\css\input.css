/* Font Imports */
@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-Regular.otf")
        format("opentype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-Light.otf")
        format("opentype");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-Medium.otf")
        format("opentype");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-SemiBold.otf")
        format("opentype");
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-Bold.otf")
        format("opentype");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../../assets/fonts/NeueEinstellung-ExtraBold.otf")
        format("opentype");
    font-weight: 800;
    font-style: normal;
}

/* Neue Einstellung Font Family */
@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Thin.otf") format("opentype");
    font-weight: 100;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-ExtraLight.otf") format("opentype");
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Light.otf") format("opentype");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Regular.otf") format("opentype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Medium.otf") format("opentype");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-SemiBold.otf") format("opentype");
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Bold.otf") format("opentype");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-ExtraBold.otf") format("opentype");
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";
    src: url("../fonts/NeueEinstellung-Black.otf") format("opentype");
    font-weight: 900;
    font-style: normal;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Base Styles */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-neue text-primary-base antialiased;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-semibold leading-tight;
    }

    a {
        @apply transition-colors duration-200;
    }
}

/* Custom Components */
@layer components {
    /* Buttons */
    .btn {
        @apply inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }

    .btn-primary {
        @apply btn bg-blue-600 hover:bg-blue-700 text-white py-2.5 px-6 focus:ring-blue-500;
    }

    .btn-secondary {
        @apply btn bg-gray-600 hover:bg-gray-700 text-white py-2.5 px-6 focus:ring-gray-500;
    }

    .btn-outline {
        @apply btn border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white py-2 px-6 focus:ring-blue-500;
    }

    .btn-sm {
        @apply py-1.5 px-4 text-sm;
    }

    .btn-lg {
        @apply py-3 px-8 text-lg;
    }

    /* Cards */
    .card {
        @apply bg-white rounded-lg shadow-md overflow-hidden;
    }

    .card-hover {
        @apply card hover:shadow-xl transition-shadow duration-300;
    }

    .card-body {
        @apply p-6;
    }

    .card-header {
        @apply px-6 py-4 border-b border-gray-200;
    }

    .card-footer {
        @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
    }

    /* Navigation */
    .nav-link {
        @apply text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 relative;
    }

    .nav-link:hover::after {
        content: "";
        @apply absolute bottom-0 left-0 w-full h-0.5 bg-blue-600;
    }

    .nav-link.active {
        @apply text-blue-600;
    }

    /* Forms */
    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .form-error {
        @apply text-red-600 text-sm mt-1;
    }

    /* Hero Section */
    .hero-section {
        @apply relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20 overflow-hidden;
    }

    .hero-content {
        @apply relative z-10 container mx-auto px-4 text-center;
    }

    .hero-title {
        @apply text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fade-in;
    }

    .hero-subtitle {
        @apply text-xl md:text-2xl mb-8 opacity-90 animate-slide-up;
    }

    /* Sections */
    .section {
        @apply py-16;
    }

    .section-title {
        @apply text-3xl md:text-4xl font-bold text-center mb-12;
    }

    .section-subtitle {
        @apply text-lg text-gray-600 text-center mb-8 max-w-3xl mx-auto;
    }

    /* Grid Layouts */
    .grid-auto-fit {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .grid-auto-fill {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }

    /* Product Grid - Ensure equal height cards */
    .products.list-products {
        @apply grid items-stretch;
    }

    .products.list-products > * {
        @apply h-full;
    }

    /* Feature Cards */
    .feature-card {
        @apply card-hover text-center p-8;
    }

    .feature-icon {
        @apply w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center;
    }

    .feature-title {
        @apply text-xl font-semibold mb-4;
    }

    .feature-description {
        @apply text-gray-600;
    }

    /* Stats */
    .stat-card {
        @apply text-center p-6;
    }

    .stat-number {
        @apply text-4xl font-bold text-blue-600 mb-2;
    }

    .stat-label {
        @apply text-gray-600 font-medium;
    }

    /* Testimonials */
    .testimonial-card {
        @apply card p-6 relative;
    }

    .testimonial-quote {
        @apply text-gray-600 italic mb-4;
    }

    .testimonial-author {
        @apply flex items-center;
    }

    .testimonial-avatar {
        @apply w-12 h-12 rounded-full mr-4;
    }

    /* Badges */
    .badge {
        @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
    }

    .badge-primary {
        @apply badge bg-blue-100 text-blue-800;
    }

    .badge-success {
        @apply badge bg-green-100 text-green-800;
    }

    .badge-warning {
        @apply badge bg-yellow-100 text-yellow-800;
    }

    .badge-danger {
        @apply badge bg-red-100 text-red-800;
    }

    /* Loading States */
    .loading {
        @apply animate-pulse;
    }

    .spinner {
        @apply animate-spin h-5 w-5 border-2 border-gray-300 border-t-blue-600 rounded-full;
    }

    /* Review System Styles */
    .review-item {
        @apply transition-all duration-[0.3s] ease-[ease];
    }
    .star-btn svg {
        @apply transition-[color] duration-[0.2s] ease-[ease] cursor-pointer;
    }
    .star-btn:hover svg {
        @apply scale-110;
    }
    .filter-btn {
        @apply transition-all duration-[0.3s] ease-[ease] hover:-translate-y-px hover:shadow-[0_2px_4px_rgba(0,0,0,0.1)];
    }
    .helpful-btn:hover {
        @apply -translate-y-px;
    }
    .review-modal {
        @apply backdrop-blur-sm animate-[fadeIn_0.3s_ease];
    }
    @keyframes fadeIn {
        from {
            @apply opacity-0;
        }
        to {
            @apply opacity-100;
        }
    }
    .modal-content {
        @apply animate-[slideUp_0.3s_ease];
    }
    @keyframes slideUp {
        from {
            @apply translate-y-[30px] opacity-0;
        }
        to {
            @apply translate-y-0 opacity-100;
        }
    }
    .rating-bar {
        @apply transition-all duration-[0.3s] ease-[ease] hover:scale-[1.02];
    }
    .review-images img:hover {
        @apply transition-transform duration-[0.3s] ease-[ease] scale-105;
    }

    /* Review form enhancements */
    .review-form textarea:focus,
    .review-form input:focus {
        @apply shadow-[0_0_0_3px_rgba(59,130,246,0.1)];
    }
    .submit-btn:hover {
        @apply -translate-y-px shadow-[0_4px_8px_rgba(0,0,0,0.2)];
    }
    .product-thumbs-swiper .swiper-slide {
        @apply opacity-70 transition-opacity duration-[0.3s] ease-[ease] hover:opacity-100;
    }
    .product-thumbs-swiper .swiper-slide-thumb-active {
        @apply opacity-100;
    }
    /* Custom Spacing */
    .section-padding {
        @apply py-16 md:py-20 lg:py-24;
    }

    .container-padding {
        @apply px-4 sm:px-6 lg:px-8;
    }

    .swiper-pagination {
        @apply bottom-7 !important;
    }

    /* Hero Slider Hover Effects */
    .categories-swiper .swiper-slide {
        @apply cursor-pointer;
    }

    .categories-swiper .swiper-slide .text-overlay {
        @apply transition-all duration-500 ease-in-out;
    }

    .categories-swiper .swiper-slide:hover .text-overlay {
        @apply opacity-100;
    }

    .categories-swiper .swiper-slide:hover .text-overlay > div {
        @apply translate-y-0;
    }

    /* Services Swiper Staggered Effects */
    .services-swiper .swiper-slide {
        @apply transition-transform duration-500 ease-out cursor-pointer;
        height: auto;
        display: flex;
        align-items: stretch;
    }

    /* Staggered classes applied via JavaScript */
    .services-swiper .swiper-slide.staggered-even {
        transform: translateY(90px);
    }

    .services-swiper .swiper-slide.staggered-odd {
        transform: translateY(0);
    }

    /* Even slides hover effect */
    .services-swiper .swiper-slide.staggered-even:hover:not(.elevated) {
        transform: translateY(80px) !important;
    }

    /* Odd slides hover effect */
    .services-swiper .swiper-slide.staggered-odd:hover:not(.elevated) {
        transform: translateY(-10px) !important;
    }

    /* Service card additional styling */
    .services-swiper .service-card {
        @apply transition-all duration-300 ease-out;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        width: 100%;
        min-height: 400px;
    }

    .services-swiper .swiper-slide.elevated .service-card {
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        transform: scale(1.02);
    }

    /* Animation keyframes */
    @keyframes slideUp {
        from {
            transform: translateY(90px);
            opacity: 0.8;
        }
        to {
            transform: translateY(-20px);
            opacity: 1;
        }
    }

    @keyframes slideDown {
        from {
            transform: translateY(-20px);
            opacity: 1;
        }
        to {
            transform: translateY(90px);
            opacity: 0.8;
        }
    }

    .slide-up-animation {
        animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .slide-down-animation {
        animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    /* Gradient backgrounds for service cards */
    .bg-gradient6 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient7 {
        background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
    }

    .bg-gradient8 {
        background: linear-gradient(135deg, #04389D 0%, #17C1F5 100%);
    }

    /* Service card hover improvements */
    .services-swiper .service-card {
        position: relative;
        isolation: isolate;
    }

    .services-swiper .service-card:hover .relative.z-10 {
        z-index: 0 !important;
    }

    .services-swiper .service-card:hover .absolute.opacity-0 {
        opacity: 1 !important;
        z-index: 20 !important;
    }

    /* Ensure proper stacking context */
    .services-swiper .service-card > div {
        position: relative;
    }

    .services-swiper .service-card .absolute {
        position: absolute !important;
    }

    /* Force overlay to appear on top when hovering */
    .service-card:hover .relative {
        z-index: 1 !important;
    }

    .service-card:hover .absolute {
        z-index: 999 !important;
        opacity: 1 !important;
    }

    /* Alternative approach - hide image on hover */
    .service-card:hover img {
        opacity: 0.1;
        transition: opacity 0.3s ease;
    }

    /* Ensure text is visible in overlay */
    .service-card .absolute h3,
    .service-card .absolute p,
    .service-card .absolute div {
        position: relative;
        z-index: 10;
        color: white !important;
    }

    .service-card:hover .absolute h3,
    .service-card:hover .absolute p {
        color: white !important;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    /* Make sure background gradient is visible */
    .service-card:hover .bg-gradient6 {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        opacity: 0.95 !important;
    }

    /* Complete CSS-only hover solution */
    .service-card .service-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        z-index: 5;
        transition: all 0.3s ease;
        padding: 1rem 2rem;
        color: white;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        padding-top: 3rem;
    }

    .service-card:hover .service-overlay {
        opacity: 1;
        z-index: 15;
    }

    .service-card:hover .service-content {
        z-index: 1;
    }
    .swiper-pagination-bullet {
        @apply w-3 h-3 bg-primary-border;
        @apply opacity-100;
    }

    .swiper-pagination-bullet-active {
        @apply w-3 h-3 bg-secondary-main; /* Kích thước và màu khi active */
    }
    .swiper-button-prev,
    .swiper-button-next {
        @apply w-11 h-11 bg-opacity-100 rounded-full flex items-center justify-center text-gray-800  focus:outline-none transition-all duration-200;
    }

    .swiper-button-prev::after,
    .swiper-button-next::after {
        @apply text-xl font-bold text-primary-main;
    }

    .swiper-button-prev {
        @apply left-0;
    }

    .swiper-button-next {
        @apply right-0;
    }

    .swiper-button-prev.swiper-button-disabled,
    .swiper-button-next.swiper-button-disabled {
        @apply opacity-50 cursor-not-allowed;
    }
    .swiper-pagination.unset {
        @apply top-[unset] !important; /* Adjust top position */
        position: unset !important;
    }
    .swiper-pagination-bullet {
        @apply w-[10px] h-[10px] bg-primary-gray;
    }

    .swiper-pagination-bullet-active {
        @apply w-12 h-[10px] bg-secondary-main rounded-2xl; /* Active bullet size and color */
    }

    .services-swiper .swiper-slide {
        transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        height: auto; /* Allow natural height */
        display: flex; /* Use flexbox for better control */
        align-items: stretch; /* Stretch to full height */
    }

    /* Staggered classes applied via JavaScript */
    .services-swiper .swiper-slide.staggered-even {
        transform: translateY(90px);
    }

    .services-swiper .swiper-slide.staggered-odd {
        transform: translateY(0);
    }

    .services-swiper .swiper-button-next:after,
    .services-swiper .swiper-button-prev:after {
        color: #ffffff;
    }

    /* Animation keyframes */
    @keyframes slideUp {
        from {
            transform: translateY(90px);
            opacity: 0.8;
        }
        to {
            transform: translateY(-20px);
            opacity: 1;
        }
    }

    @keyframes slideDown {
        from {
            transform: translateY(-20px);
            opacity: 1;
        }
        to {
            transform: translateY(90px);
            opacity: 0.8;
        }
    }

    .slide-up-animation {
        animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .slide-down-animation {
        animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
}

/* Custom Utilities */
@layer utilities {
    .text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .text-shadow-lg {
        text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.2);
    }

    .gradient-text {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
    }

    .gradient-bg {
        @apply bg-gradient-to-r from-blue-600 to-purple-600;
    }

    .gradient-bg-hover {
        @apply hover:bg-gradient-to-r hover:from-blue-700 hover:to-purple-700;
    }

    .glass {
        backdrop-filter: blur(10px);
        @apply bg-white bg-opacity-20 border border-white border-opacity-20;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Aspect Ratios */
    .aspect-video {
        aspect-ratio: 16 / 9;
    }

    .aspect-square {
        aspect-ratio: 1 / 1;
    }

    .aspect-portrait {
        aspect-ratio: 3 / 4;
    }
}

/* Mobile Filter Sidebar Styles */
@media (max-width: 767px) {
    .filter-sidebar {
        width: 320px !important;
        max-width: 85vw !important;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    }

    .mobile-filter-overlay {
        backdrop-filter: blur(2px);
    }
}

/* Video Modal Styles */
.video-modal {
    animation: fadeIn 0.3s ease-in-out;
    backdrop-filter: blur(4px);
}

.video-modal-open {
    animation: modalSlideIn 0.4s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.video-modal .relative {
    animation: videoSlideUp 0.3s ease-out;
}

@keyframes videoSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Video modal responsiveness */
@media (max-width: 768px) {
    .video-modal .relative {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}
