<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Order::with('user')->latest();
        
        // Lọc theo trạng thái
        if ($request->has('status') && $request->status != '') {
            $query->where('status', $request->status);
        }
        
        // Lọc theo trạng thái thanh toán
        if ($request->has('payment_status') && $request->payment_status != '') {
            $query->where('payment_status', $request->payment_status);
        }
        
        // Tìm kiếm theo Order ID
        if ($request->has('order_number') && $request->order_number != '') {
            $query->where('order_number', 'like', '%' . $request->order_number . '%');
        }
        
        // Lọc theo ngày (từ)
        if ($request->has('from_date') && $request->from_date != '') {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        
        // Lọc theo ngày (đến)
        if ($request->has('to_date') && $request->to_date != '') {
            $query->whereDate('created_at', '<=', $request->to_date);
        }
        
        $orders = $query->paginate(10);
        
        // Lấy các trạng thái đơn hàng để dùng cho form filter
        $statuses = [
            'pending' => 'Chờ xử lý',
            'processing' => 'Đang xử lý',
            'shipped' => 'Đã giao vận chuyển',
            'delivered' => 'Đã giao hàng',
            'cancelled' => 'Đã hủy',
            'refunded' => 'Đã hoàn tiền'
        ];
        
        $paymentStatuses = [
            'pending' => 'Chờ thanh toán',
            'paid' => 'Đã thanh toán',
            'failed' => 'Thanh toán thất bại',
            'refunded' => 'Đã hoàn tiền'
        ];
        
        return view('admin.orders.index', compact('orders', 'statuses', 'paymentStatuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Không cho tạo đơn hàng mới từ admin
        return redirect()->route('admin.orders.index')
            ->with('error', 'Không thể tạo đơn hàng mới từ trang admin. Đơn hàng chỉ được tạo từ khách hàng.');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Không cho tạo đơn hàng mới từ admin
        return redirect()->route('admin.orders.index')
            ->with('error', 'Không thể tạo đơn hàng mới từ trang admin. Đơn hàng chỉ được tạo từ khách hàng.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        $order->load(['user', 'details.product']);
        return view('admin.orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Order $order)
    {
        $order->load(['user', 'details.product']);
        
        $statuses = [
            'pending' => 'Chờ xử lý',
            'processing' => 'Đang xử lý',
            'shipped' => 'Đã giao vận chuyển',
            'delivered' => 'Đã giao hàng',
            'cancelled' => 'Đã hủy',
            'refunded' => 'Đã hoàn tiền'
        ];
        
        $paymentStatuses = [
            'pending' => 'Chờ thanh toán',
            'paid' => 'Đã thanh toán',
            'failed' => 'Thanh toán thất bại',
            'refunded' => 'Đã hoàn tiền'
        ];
        
        return view('admin.orders.edit', compact('order', 'statuses', 'paymentStatuses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled,refunded',
            'payment_status' => 'required|in:pending,paid,failed,refunded',
            'admin_note' => 'nullable|string|max:1000',
        ]);
        
        // Lưu trạng thái trước khi cập nhật để kiểm tra sự thay đổi
        $oldStatus = $order->status;
        
        // Cập nhật thông tin đơn hàng
        $order->status = $request->status;
        $order->payment_status = $request->payment_status;
        $order->admin_note = $request->admin_note;
        
        // Cập nhật các trường thời gian dựa trên trạng thái
        if ($request->status == 'cancelled' && $oldStatus != 'cancelled') {
            $order->cancelled_at = now();
        }
        
        if ($request->status == 'delivered' && $oldStatus != 'delivered') {
            $order->delivered_at = now();
        }
        
        $order->save();
        
        return redirect()->route('admin.orders.show', $order->id)
            ->with('success', 'Cập nhật đơn hàng thành công.');
    }

    /**
     * Update order status quickly.
     */
    public function updateStatus(Request $request, Order $order)
    {
        $request->validate([
            'status' => 'required|in:pending,processing,shipped,delivered,cancelled,refunded',
        ]);
        
        // Lưu trạng thái trước khi cập nhật để kiểm tra sự thay đổi
        $oldStatus = $order->status;
        
        // Cập nhật trạng thái đơn hàng
        $order->status = $request->status;
        
        // Cập nhật các trường thời gian dựa trên trạng thái
        if ($request->status == 'cancelled' && $oldStatus != 'cancelled') {
            $order->cancelled_at = now();
        }
        
        if ($request->status == 'delivered' && $oldStatus != 'delivered') {
            $order->delivered_at = now();
        }
        
        $order->save();
        
        return redirect()->back()->with('success', 'Cập nhật trạng thái đơn hàng thành công.');
    }

    /**
     * Update payment status quickly.
     */
    public function updatePaymentStatus(Request $request, Order $order)
    {
        $request->validate([
            'payment_status' => 'required|in:pending,paid,failed,refunded',
        ]);
        
        // Cập nhật trạng thái thanh toán
        $order->payment_status = $request->payment_status;
        $order->save();
        
        return redirect()->back()->with('success', 'Cập nhật trạng thái thanh toán thành công.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order)
    {
        try {
            DB::beginTransaction();
            
            // Xóa chi tiết đơn hàng trước
            $order->details()->delete();
            
            // Sau đó xóa đơn hàng
            $order->delete();
            
            DB::commit();
            
            return redirect()->route('admin.orders.index')
                ->with('success', 'Xóa đơn hàng thành công.');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->route('admin.orders.index')
                ->with('error', 'Lỗi khi xóa đơn hàng: ' . $e->getMessage());
        }
    }
}
