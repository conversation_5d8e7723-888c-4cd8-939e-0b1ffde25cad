<?php

namespace App\Models;

use Spatie\Permission\Models\Permission as SpatiePermission;

class Permission extends SpatiePermission
{
    protected $fillable = [
        'name',
        'guard_name',
        'group',
        'description',
    ];

    // You can add custom scopes for filtering
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }
}
