<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;

class WishlistController extends Controller
{
    /**
     * Hiển thị trang wishlist
     */
    public function index()
    {
        $wishlistItems = $this->getWishlistItems();
        $products = [];
        
        if (!empty($wishlistItems)) {
            $products = Product::whereIn('id', $wishlistItems)->get();
            
            \Log::info('Wishlist products: ' . $products->count());
            \Log::info('Wishlist items: ' . json_encode($wishlistItems));
        }
        
        foreach ($products as $product) {
            if (empty($product->image)) {
                $product->image = 'images/placeholder.jpg';
            }
        }
        
        return view('templates.auvista.pages.wishlist', [
            'products' => $products,
            'title' => 'Danh sách yêu thích',
            'breadcrumb' => [
                'Trang chủ' => route('home'),
                'Danh sách yêu thích' => route('wishlist')
            ]
        ]);
    }
    
    /**
     * Thêm sản phẩm vào wishlist
     */
    public function add(Request $request)
    {
        $productId = $request->input('product_id');
        $wishlistItems = $this->getWishlistItems();
        
        if (!in_array($productId, $wishlistItems)) {
            $wishlistItems[] = $productId;
            $this->updateWishlistCookie(array_values($wishlistItems));
        }
        
        return response()->json([
            'success' => true,
            'count' => count($wishlistItems)
        ]);
    }
    
    /**
     * Xóa sản phẩm khỏi wishlist
     */
    public function remove(Request $request)
    {
        $productId = $request->input('product_id');
        $wishlistItems = $this->getWishlistItems();
        
        $key = array_search($productId, $wishlistItems);
        if ($key !== false) {
            unset($wishlistItems[$key]);
            $this->updateWishlistCookie(array_values($wishlistItems));
        }
        
        return response()->json([
            'success' => true,
            'count' => count($wishlistItems)
        ]);
    }
    
    /**
     * Xóa tất cả sản phẩm khỏi danh sách yêu thích
     */
    public function clear()
    {
        Cookie::queue(Cookie::forget('wishlist'));
        
        return redirect()->back()->with('success', 'Đã xóa tất cả sản phẩm khỏi danh sách yêu thích');
    }
    
    /**
     * Lấy wishlist items từ cookie
     */
    private function getWishlistItems()
    {
        $wishlist = Cookie::get('wishlist');
        return $wishlist ? json_decode($wishlist, true) : [];
    }
    
    /**
     * Cập nhật cookie wishlist
     */
    private function updateWishlistCookie(array $items)
    {
        Cookie::queue('wishlist', json_encode($items), 60 * 24 * 30); // Cookie tồn tại 30 ngày
    }
} 