{"__meta": {"id": "01K08YXPZ35FB1RHHW4VN1B571", "datetime": "2025-07-16 13:32:03", "utime": **********.299992, "method": "GET", "uri": "/san-pham/test", "ip": "127.0.0.1"}, "messages": {"count": 3, "messages": [{"message": "[13:32:02] LOG.info: ProductController@show {\n    \"slug\": \"test\",\n    \"locale\": \"vi\",\n    \"available_locales\": [\n        \"vi\",\n        \"en\"\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.050112, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:02] LOG.info: Product found {\n    \"product_id\": 152,\n    \"product_lang\": \"vi\",\n    \"product_name\": \"test123\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.063713, "xdebug_link": null, "collector": "log"}, {"message": "[13:32:02] LOG.error: syntax error, unexpected token \"===\", expecting end of file (View: H:\\laragon\\www\\auvista\\resources\\views\\templates\\auvista\\products\\products-detail.blade.php) {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.46753, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.589998, "end": **********.300027, "duration": 1.710028886795044, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": **********.589998, "relative_start": 0, "end": **********.902045, "relative_end": **********.902045, "duration": 0.*****************, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.902063, "relative_start": 0.*****************, "end": **********.300031, "relative_end": 4.0531158447265625e-06, "duration": 1.***************, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.036288, "relative_start": 0.****************, "end": **********.039018, "relative_end": **********.039018, "duration": 0.0027298927307128906, "duration_str": "2.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.069813, "relative_start": 0.****************, "end": **********.30004, "relative_end": 1.3113021850585938e-05, "duration": 1.***************, "duration_str": "1.23s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.auvista.products.products-detail", "start": **********.071747, "relative_start": 0.****************, "end": **********.071747, "relative_end": **********.071747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.679578, "relative_start": 1.0895800590515137, "end": **********.679578, "relative_end": **********.679578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.681427, "relative_start": 1.0914289951324463, "end": **********.681427, "relative_end": **********.681427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.681839, "relative_start": 1.0918409824371338, "end": **********.681839, "relative_end": **********.681839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.682392, "relative_start": 1.0923938751220703, "end": **********.682392, "relative_end": **********.682392, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.682684, "relative_start": 1.0926859378814697, "end": **********.682684, "relative_end": **********.682684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.682903, "relative_start": 1.092905044555664, "end": **********.682903, "relative_end": **********.682903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.683046, "relative_start": 1.093048095703125, "end": **********.683046, "relative_end": **********.683046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.683264, "relative_start": 1.0932660102844238, "end": **********.683264, "relative_end": **********.683264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.683543, "relative_start": 1.0935449600219727, "end": **********.683543, "relative_end": **********.683543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.683959, "relative_start": 1.0939610004425049, "end": **********.683959, "relative_end": **********.683959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.684282, "relative_start": 1.0942840576171875, "end": **********.684282, "relative_end": **********.684282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.241003, "relative_start": 1.****************, "end": **********.241003, "relative_end": **********.241003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.241732, "relative_start": 1.6517338752746582, "end": **********.241732, "relative_end": **********.241732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.242149, "relative_start": 1.652151107788086, "end": **********.242149, "relative_end": **********.242149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.242681, "relative_start": 1.6526830196380615, "end": **********.242681, "relative_end": **********.242681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.243026, "relative_start": 1.6530280113220215, "end": **********.243026, "relative_end": **********.243026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.261889, "relative_start": 1.6718909740447998, "end": **********.261889, "relative_end": **********.261889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.288093, "relative_start": 1.6980950832366943, "end": **********.288093, "relative_end": **********.288093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.288672, "relative_start": 1.698673963546753, "end": **********.288672, "relative_end": **********.288672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.289356, "relative_start": 1.6993579864501953, "end": **********.289356, "relative_end": **********.289356, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.289682, "relative_start": 1.6996839046478271, "end": **********.289682, "relative_end": **********.289682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.289962, "relative_start": 1.6999640464782715, "end": **********.289962, "relative_end": **********.289962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 45161392, "peak_usage_str": "43MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "syntax error, unexpected token \"===\", expecting end of file (View: H:\\laragon\\www\\auvista\\resources\\views\\templates\\auvista\\products\\products-detail.blade.php)", "code": 0, "file": "storage/framework/views/01715d72ef4c4b09769e2184565045cc.php", "line": 660, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1737102087 data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">[object ParseError]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">[object ParseError]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23221 title=\"4 occurrences\">#3221</a><samp data-depth=5 id=sf-dump-1737102087-ref23221 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23187 title=\"4 occurrences\">#3187</a><samp data-depth=5 id=sf-dump-1737102087-ref23187 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tests</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&lt;p&gt;hello&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">500000.00</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>100</span>\n            \"<span class=sf-dump-key>image_url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">products/01JZSBNV44KAWQDP2AXY8VQKCH.png</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"153 characters\">[&quot;products/gallery/01JZSBNV4D572X01VY71AVSZYZ.png&quot;, &quot;products/gallery/01JZSBNV4K9689G155CWS5HQZW.png&quot;, &quot;products/gallery/01JZSBNV4PHKRCEB7VT7BV7Q8Q.png&quot;]</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Vietnam</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"3 characters\">90%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"81 characters\">[{&quot;key&quot;: &quot;k&#237;ch th&#432;&#7899;c&quot;, &quot;value&quot;: &quot;100kg&quot;}, {&quot;key&quot;: &quot;kh&#7889;i l&#432;&#7907;ng&quot;, &quot;value&quot;: &quot;500g&quot;}]</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kh&#244;ng c&#243;</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:07:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:42:31</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tests</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&lt;p&gt;hello&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">500000.00</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>100</span>\n            \"<span class=sf-dump-key>image_url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">products/01JZSBNV44KAWQDP2AXY8VQKCH.png</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"153 characters\">[&quot;products/gallery/01JZSBNV4D572X01VY71AVSZYZ.png&quot;, &quot;products/gallery/01JZSBNV4K9689G155CWS5HQZW.png&quot;, &quot;products/gallery/01JZSBNV4PHKRCEB7VT7BV7Q8Q.png&quot;]</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Vietnam</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"3 characters\">90%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"81 characters\">[{&quot;key&quot;: &quot;k&#237;ch th&#432;&#7899;c&quot;, &quot;value&quot;: &quot;100kg&quot;}, {&quot;key&quot;: &quot;kh&#7889;i l&#432;&#7907;ng&quot;, &quot;value&quot;: &quot;500g&quot;}]</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kh&#244;ng c&#243;</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:07:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:42:31</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>category</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3161</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">am-thanh-hoi-thao</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"107 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883; v&#7899;i kh&#7843; n&#259;ng truy&#7873;n t&#7843;i &#226;m thanh r&#245; r&#224;ng v&#224; &#7893;n &#273;&#7883;nh.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#194;m thanh h&#7897;i th&#7843;o - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"54 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&#226;m thanh h&#7897;i th&#7843;o, conference audio, h&#7879; th&#7889;ng &#226;m thanh, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">am-thanh-hoi-thao</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"107 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883; v&#7899;i kh&#7843; n&#259;ng truy&#7873;n t&#7843;i &#226;m thanh r&#245; r&#224;ng v&#224; &#7893;n &#273;&#7883;nh.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#194;m thanh h&#7897;i th&#7843;o - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"54 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&#226;m thanh h&#7897;i th&#7843;o, conference audio, h&#7879; th&#7889;ng &#226;m thanh, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">seo_title</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"15 characters\">seo_description</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">seo_keywords</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">thumbnail</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            \"<span class=sf-dump-key>brand</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Brand\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Brand</span></span> {<a class=sf-dump-ref>#3149</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bose</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bose</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"83 characters\">Th&#432;&#417;ng hi&#7879;u M&#7929; n&#7893;i ti&#7871;ng v&#7899;i c&#244;ng ngh&#7879; ch&#7889;ng &#7891;n v&#224; h&#7879; th&#7889;ng &#226;m thanh ch&#7845;t l&#432;&#7907;ng cao</span>\"\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bose</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bose</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"83 characters\">Th&#432;&#417;ng hi&#7879;u M&#7929; n&#7893;i ti&#7871;ng v&#7899;i c&#244;ng ngh&#7879; ch&#7889;ng &#7891;n v&#224; h&#7879; th&#7889;ng &#226;m thanh ch&#7845;t l&#432;&#7907;ng cao</span>\"\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n              +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3157</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n            <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n            <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23158 title=\"4 occurrences\">#3158</a><samp data-depth=5 id=sf-dump-1737102087-ref23158 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23190 title=\"4 occurrences\">#3190</a><samp data-depth=5 id=sf-dump-1737102087-ref23190 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3134</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>128</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"49 characters\">H&#7879; th&#7889;ng Micro h&#7897;i th&#7843;o kh&#244;ng d&#226;y Shure Microflex</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"49 characters\">he-thong-micro-hoi-thao-khong-day-shure-microflex</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-BHGME3</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"123 characters\">H&#7879; th&#7889;ng micro kh&#244;ng d&#226;y linh ho&#7841;t v&#224; &#273;&#225;ng tin c&#7853;y, cung c&#7845;p &#226;m thanh r&#245; r&#224;ng v&#224; t&#7921; nhi&#234;n cho c&#225;c bu&#7893;i h&#7897;i th&#7843;o quan tr&#7885;ng.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>46</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"2 characters\">M&#7929;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>128</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"49 characters\">H&#7879; th&#7889;ng Micro h&#7897;i th&#7843;o kh&#244;ng d&#226;y Shure Microflex</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"49 characters\">he-thong-micro-hoi-thao-khong-day-shure-microflex</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-BHGME3</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"123 characters\">H&#7879; th&#7889;ng micro kh&#244;ng d&#226;y linh ho&#7841;t v&#224; &#273;&#225;ng tin c&#7853;y, cung c&#7845;p &#226;m thanh r&#245; r&#224;ng v&#224; t&#7921; nhi&#234;n cho c&#225;c bu&#7893;i h&#7897;i th&#7843;o quan tr&#7885;ng.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>46</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"2 characters\">M&#7929;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3133</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>129</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Shure Microflex Wireless Conference Microphone System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"53 characters\">shure-microflex-wireless-conference-microphone-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-NWTPXQ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"115 characters\">A flexible and reliable wireless microphone system that delivers clear and natural audio for important conferences.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>37</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USA</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>129</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Shure Microflex Wireless Conference Microphone System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"53 characters\">shure-microflex-wireless-conference-microphone-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-NWTPXQ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"115 characters\">A flexible and reliable wireless microphone system that delivers clear and natural audio for important conferences.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>37</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USA</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3121</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>138</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">H&#7879; th&#7889;ng h&#7897;i th&#7843;o Bosch Dicentis</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">he-thong-hoi-thao-bosch-dicentis</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ABLHGZ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"94 characters\">N&#7873;n t&#7843;ng h&#7897;i th&#7843;o d&#7921;a tr&#234;n IP v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh tuy&#7879;t v&#7901;i v&#224; kh&#7843; n&#259;ng m&#7903; r&#7897;ng linh ho&#7841;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>33</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#272;&#7913;c</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>138</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">H&#7879; th&#7889;ng h&#7897;i th&#7843;o Bosch Dicentis</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">he-thong-hoi-thao-bosch-dicentis</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ABLHGZ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"94 characters\">N&#7873;n t&#7843;ng h&#7897;i th&#7843;o d&#7921;a tr&#234;n IP v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh tuy&#7879;t v&#7901;i v&#224; kh&#7843; n&#259;ng m&#7903; r&#7897;ng linh ho&#7841;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>33</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#272;&#7913;c</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3122</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>139</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Bosch Dicentis Conference System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">bosch-dicentis-conference-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-YYBLGG</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"86 characters\">An IP-based conference platform with excellent audio quality and flexible scalability.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>94</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Germany</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>139</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Bosch Dicentis Conference System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">bosch-dicentis-conference-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-YYBLGG</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"86 characters\">An IP-based conference platform with excellent audio quality and flexible scalability.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>94</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Germany</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23158 title=\"4 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23158 title=\"4 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref22048 title=\"4 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref24 title=\"4 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23221 title=\"4 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23187 title=\"4 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23158 title=\"4 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-1737102087-ref23190 title=\"4 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1737102087\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            });\n", "            // <PERSON><PERSON><PERSON> tối đa 8 sản phẩm gần nhất để đủ cho slider\n", "            $viewedProducts = array_slice(array_reverse($viewedProducts), 0, 8);\n", "=======\n", "\n", "            {{-- <PERSON> Block --}}\n", "            @component('templates.auvista.blocks.review', ['product' => $product, 'reviews' => $reviews, 'reviewsCount' => $reviewsCount, 'averageRating' => $averageRating])\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F01715d72ef4c4b09769e2184565045cc.php&line=660", "ajax": false, "filename": "01715d72ef4c4b09769e2184565045cc.php", "line": "660"}}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "syntax error, unexpected token \"===\", expecting end of file", "code": 0, "file": "storage/framework/views/01715d72ef4c4b09769e2184565045cc.php", "line": 660, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a><samp data-depth=5 id=sf-dump-*********-ref23221 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a><samp data-depth=5 id=sf-dump-*********-ref23187 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tests</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&lt;p&gt;hello&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">500000.00</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>100</span>\n            \"<span class=sf-dump-key>image_url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">products/01JZSBNV44KAWQDP2AXY8VQKCH.png</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"153 characters\">[&quot;products/gallery/01JZSBNV4D572X01VY71AVSZYZ.png&quot;, &quot;products/gallery/01JZSBNV4K9689G155CWS5HQZW.png&quot;, &quot;products/gallery/01JZSBNV4PHKRCEB7VT7BV7Q8Q.png&quot;]</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Vietnam</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"3 characters\">90%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"81 characters\">[{&quot;key&quot;: &quot;k&#237;ch th&#432;&#7899;c&quot;, &quot;value&quot;: &quot;100kg&quot;}, {&quot;key&quot;: &quot;kh&#7889;i l&#432;&#7907;ng&quot;, &quot;value&quot;: &quot;500g&quot;}]</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kh&#244;ng c&#243;</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:07:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:42:31</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>152</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n            \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">test</span>\"\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tests</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&lt;p&gt;hello&lt;/p&gt;</span>\"\n            \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1000000.00</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"9 characters\">500000.00</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>100</span>\n            \"<span class=sf-dump-key>image_url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">products/01JZSBNV44KAWQDP2AXY8VQKCH.png</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"153 characters\">[&quot;products/gallery/01JZSBNV4D572X01VY71AVSZYZ.png&quot;, &quot;products/gallery/01JZSBNV4K9689G155CWS5HQZW.png&quot;, &quot;products/gallery/01JZSBNV4PHKRCEB7VT7BV7Q8Q.png&quot;]</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n            \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n            \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Vietnam</span>\"\n            \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n            \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"3 characters\">C&#225;i</span>\"\n            \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"3 characters\">90%</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"81 characters\">[{&quot;key&quot;: &quot;k&#237;ch th&#432;&#7899;c&quot;, &quot;value&quot;: &quot;100kg&quot;}, {&quot;key&quot;: &quot;kh&#7889;i l&#432;&#7907;ng&quot;, &quot;value&quot;: &quot;500g&quot;}]</span>\"\n            \"<span class=sf-dump-key>usage_instructions</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kh&#244;ng c&#243;</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n            \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>5</span>\n            \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:07:06</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:42:31</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n            \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n            \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>category</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Category\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Category</span></span> {<a class=sf-dump-ref>#3161</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">categories</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">am-thanh-hoi-thao</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"107 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883; v&#7899;i kh&#7843; n&#259;ng truy&#7873;n t&#7843;i &#226;m thanh r&#245; r&#224;ng v&#224; &#7893;n &#273;&#7883;nh.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#194;m thanh h&#7897;i th&#7843;o - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"54 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&#226;m thanh h&#7897;i th&#7843;o, conference audio, h&#7879; th&#7889;ng &#226;m thanh, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"17 characters\">am-thanh-hoi-thao</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"107 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883; v&#7899;i kh&#7843; n&#259;ng truy&#7873;n t&#7843;i &#226;m thanh r&#245; r&#224;ng v&#224; &#7893;n &#273;&#7883;nh.</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n                \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"27 characters\">&#194;m thanh h&#7897;i th&#7843;o - AV Plus</span>\"\n                \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"54 characters\">H&#7879; th&#7889;ng &#226;m thanh chuy&#234;n nghi&#7879;p cho h&#7897;i th&#7843;o, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>seo_keywords</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&#226;m thanh h&#7897;i th&#7843;o, conference audio, h&#7879; th&#7889;ng &#226;m thanh, h&#7897;i ngh&#7883;</span>\"\n                \"<span class=sf-dump-key>thumbnail</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:29</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">string</span>\"\n                \"<span class=sf-dump-key>parent_id</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"9 characters\">parent_id</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"9 characters\">seo_title</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"15 characters\">seo_description</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">seo_keywords</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"9 characters\">thumbnail</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            \"<span class=sf-dump-key>brand</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Brand\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Brand</span></span> {<a class=sf-dump-ref>#3149</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"6 characters\">brands</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bose</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bose</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"83 characters\">Th&#432;&#417;ng hi&#7879;u M&#7929; n&#7893;i ti&#7871;ng v&#7899;i c&#244;ng ngh&#7879; ch&#7889;ng &#7891;n v&#224; h&#7879; th&#7889;ng &#226;m thanh ch&#7845;t l&#432;&#7907;ng cao</span>\"\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:9</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Bose</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bose</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"83 characters\">Th&#432;&#417;ng hi&#7879;u M&#7929; n&#7893;i ti&#7871;ng v&#7899;i c&#244;ng ngh&#7879; ch&#7889;ng &#7891;n v&#224; h&#7879; th&#7889;ng &#226;m thanh ch&#7845;t l&#432;&#7907;ng cao</span>\"\n                \"<span class=sf-dump-key>image</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-08 17:15:30</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []\n              +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []\n            </samp>}\n            \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#3157</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n            <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n            <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n            <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n            <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n            <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n            <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n            <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n            <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n            <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n            <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n            <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n            <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n            <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n            <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n            <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n            <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n            <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n            <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n            <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n            <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n            <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n            <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n            <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n            <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n            <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n            <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n        </samp>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23158 title=\"5 occurrences\">#3158</a><samp data-depth=5 id=sf-dump-*********-ref23158 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a><samp data-depth=5 id=sf-dump-*********-ref23190 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3134</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>128</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"49 characters\">H&#7879; th&#7889;ng Micro h&#7897;i th&#7843;o kh&#244;ng d&#226;y Shure Microflex</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"49 characters\">he-thong-micro-hoi-thao-khong-day-shure-microflex</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-BHGME3</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"123 characters\">H&#7879; th&#7889;ng micro kh&#244;ng d&#226;y linh ho&#7841;t v&#224; &#273;&#225;ng tin c&#7853;y, cung c&#7845;p &#226;m thanh r&#245; r&#224;ng v&#224; t&#7921; nhi&#234;n cho c&#225;c bu&#7893;i h&#7897;i th&#7843;o quan tr&#7885;ng.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>46</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"2 characters\">M&#7929;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>128</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"49 characters\">H&#7879; th&#7889;ng Micro h&#7897;i th&#7843;o kh&#244;ng d&#226;y Shure Microflex</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"49 characters\">he-thong-micro-hoi-thao-khong-day-shure-microflex</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-BHGME3</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"123 characters\">H&#7879; th&#7889;ng micro kh&#244;ng d&#226;y linh ho&#7841;t v&#224; &#273;&#225;ng tin c&#7853;y, cung c&#7845;p &#226;m thanh r&#245; r&#224;ng v&#224; t&#7921; nhi&#234;n cho c&#225;c bu&#7893;i h&#7897;i th&#7843;o quan tr&#7885;ng.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>46</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"2 characters\">M&#7929;</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">12 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3133</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>129</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Shure Microflex Wireless Conference Microphone System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"53 characters\">shure-microflex-wireless-conference-microphone-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-NWTPXQ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"115 characters\">A flexible and reliable wireless microphone system that delivers clear and natural audio for important conferences.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>37</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USA</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>129</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Shure Microflex Wireless Conference Microphone System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"53 characters\">shure-microflex-wireless-conference-microphone-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-NWTPXQ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"115 characters\">A flexible and reliable wireless microphone system that delivers clear and natural audio for important conferences.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"12 characters\">120000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>37</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USA</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">12 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3121</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>138</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">H&#7879; th&#7889;ng h&#7897;i th&#7843;o Bosch Dicentis</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">he-thong-hoi-thao-bosch-dicentis</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ABLHGZ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"94 characters\">N&#7873;n t&#7843;ng h&#7897;i th&#7843;o d&#7921;a tr&#234;n IP v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh tuy&#7879;t v&#7901;i v&#224; kh&#7843; n&#259;ng m&#7903; r&#7897;ng linh ho&#7841;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>33</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#272;&#7913;c</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>138</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">H&#7879; th&#7889;ng h&#7897;i th&#7843;o Bosch Dicentis</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">he-thong-hoi-thao-bosch-dicentis</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-ABLHGZ</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"94 characters\">N&#7873;n t&#7843;ng h&#7897;i th&#7843;o d&#7921;a tr&#234;n IP v&#7899;i ch&#7845;t l&#432;&#7907;ng &#226;m thanh tuy&#7879;t v&#7901;i v&#224; kh&#7843; n&#259;ng m&#7903; r&#7897;ng linh ho&#7841;t.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>33</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">vi</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#272;&#7913;c</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"8 characters\">24 th&#225;ng</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"8 characters\">H&#7879; th&#7889;ng</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">M&#7899;i 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref>#3122</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">products</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>139</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Bosch Dicentis Conference System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">bosch-dicentis-conference-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-YYBLGG</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"86 characters\">An IP-based conference platform with excellent audio quality and flexible scalability.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>94</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Germany</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:32</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>139</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Bosch Dicentis Conference System</span>\"\n                \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"32 characters\">bosch-dicentis-conference-system</span>\"\n                \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"10 characters\">AVP-YYBLGG</span>\"\n                \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"86 characters\">An IP-based conference platform with excellent audio quality and flexible scalability.</span>\"\n                \"<span class=sf-dump-key>content</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"11 characters\">95000000.00</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>stock</span>\" => <span class=sf-dump-num>94</span>\n                \"<span class=sf-dump-key>image_url</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>gallery</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n                \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n                \"<span class=sf-dump-key>origin</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Germany</span>\"\n                \"<span class=sf-dump-key>warranty</span>\" => \"<span class=sf-dump-str title=\"9 characters\">24 months</span>\"\n                \"<span class=sf-dump-key>unit</span>\" => \"<span class=sf-dump-str title=\"6 characters\">System</span>\"\n                \"<span class=sf-dump-key>condition</span>\" => \"<span class=sf-dump-str title=\"8 characters\">New 100%</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>technical_specs</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>usage_instructions</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>downloads</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-num>15</span>\n                \"<span class=sf-dump-key>meta_title</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.0</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>rating_distribution</span>\" => <span class=sf-dump-const>null</span>\n                \"<span class=sf-dump-key>is_featured</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_new</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>is_best_seller</span>\" => <span class=sf-dump-num>0</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-10 12:01:35</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_new</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>is_best_seller</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n                \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>sale_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>stock</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>gallery</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>average_rating</span>\" => \"<span class=sf-dump-str title=\"5 characters\">float</span>\"\n                \"<span class=sf-dump-key>reviews_count</span>\" => \"<span class=sf-dump-str title=\"7 characters\">integer</span>\"\n                \"<span class=sf-dump-key>product_images</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>specifications</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>technical_specs</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n                \"<span class=sf-dump-key>downloads</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:39</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"10 characters\">sale_price</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"5 characters\">stock</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n                <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"11 characters\">is_featured</span>\"\n                <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"6 characters\">is_new</span>\"\n                <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"14 characters\">is_best_seller</span>\"\n                <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"9 characters\">image_url</span>\"\n                <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n                <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"11 characters\">category_id</span>\"\n                <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"8 characters\">brand_id</span>\"\n                <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">average_rating</span>\"\n                <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"13 characters\">reviews_count</span>\"\n                <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">ingredients</span>\"\n                <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"8 characters\">benefits</span>\"\n                <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"12 characters\">target_users</span>\"\n                <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"18 characters\">usage_instructions</span>\"\n                <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"10 characters\">advantages</span>\"\n                <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"14 characters\">packaging_info</span>\"\n                <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"17 characters\">contraindications</span>\"\n                <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"12 characters\">product_type</span>\"\n                <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"6 characters\">volume</span>\"\n                <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"13 characters\">sugar_content</span>\"\n                <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">daily_limit</span>\"\n                <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"20 characters\">storage_instructions</span>\"\n                <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"12 characters\">manufacturer</span>\"\n                <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"14 characters\">product_images</span>\"\n                <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">specifications</span>\"\n                <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"15 characters\">technical_specs</span>\"\n                <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"9 characters\">downloads</span>\"\n                <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"6 characters\">origin</span>\"\n                <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"8 characters\">warranty</span>\"\n                <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"4 characters\">unit</span>\"\n                <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"9 characters\">condition</span>\"\n                <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"4 characters\">lang</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23158 title=\"5 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"83 characters\">H:\\laragon\\www\\auvista\\storage\\framework\\views/01715d72ef4c4b09769e2184565045cc.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23158 title=\"5 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23158 title=\"5 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"91 characters\">H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22048 title=\"5 occurrences\">#2048</a> &#8230;25}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref24 title=\"5 occurrences\">#4</a> &#8230;44}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23221 title=\"5 occurrences\">#3221</a>}\n        \"<span class=sf-dump-key>product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Product\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Product</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23187 title=\"5 occurrences\">#3187</a>}\n        \"<span class=sf-dump-key>reviews</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23158 title=\"5 occurrences\">#3158</a>}\n        \"<span class=sf-dump-key>reviewsCount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>averageRating</span>\" => <span class=sf-dump-num>0.0</span>\n        \"<span class=sf-dump-key>distribution</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>5</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>4</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>3</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>2</span> => <span class=sf-dump-num>0</span>\n          <span class=sf-dump-key>1</span> => <span class=sf-dump-num>0</span>\n        </samp>]\n        \"<span class=sf-dump-key>related_products</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref23190 title=\"5 occurrences\">#3190</a>}\n        \"<span class=sf-dump-key>breadcrumbs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Trang ch&#7911;</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"17 characters\">&#194;m thanh h&#7897;i th&#7843;o</span>\"\n              \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">https://auvista.test/danh-muc-san-pham/am-thanh-hoi-thao</span>\"\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>false</span>\n            </samp>]\n            <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">test123</span>\"\n              \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-const>true</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>192</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>161</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>79</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>920</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>887</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">app/Http/Middleware/LocaleMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">App\\Http\\Middleware\\LocaleMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>88</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            });\n", "            // <PERSON><PERSON><PERSON> tối đa 8 sản phẩm gần nhất để đủ cho slider\n", "            $viewedProducts = array_slice(array_reverse($viewedProducts), 0, 8);\n", "=======\n", "\n", "            {{-- <PERSON> Block --}}\n", "            @component('templates.auvista.blocks.review', ['product' => $product, 'reviews' => $reviews, 'reviewsCount' => $reviewsCount, 'averageRating' => $averageRating])\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F01715d72ef4c4b09769e2184565045cc.php&line=660", "ajax": false, "filename": "01715d72ef4c4b09769e2184565045cc.php", "line": "660"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 23, "nb_templates": 23, "templates": [{"name": "templates.auvista.products.products-detail", "param_count": null, "params": [], "start": **********.071729, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/products/products-detail.blade.phptemplates.auvista.products.products-detail", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fproducts%2Fproducts-detail.blade.php&line=1", "ajax": false, "filename": "products-detail.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.679559, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.681407, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.68182, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php&line=1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.682375, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.682669, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.682888, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php&line=1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.683032, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php&line=1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.683248, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php&line=1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.683529, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.683943, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.684264, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php&line=1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.240936, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php&line=1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.241668, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.242081, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.24261, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php&line=1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.242962, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.26182, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php&line=1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.288071, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.288608, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php&line=1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.289338, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.289666, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.289928, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0053300000000000005, "accumulated_duration_str": "5.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `products` where `slug` = 'test' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["test", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.05083, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 20.075}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.055904, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 20.075, "width_percent": 13.321}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.0588782, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 33.396, "width_percent": 12.008}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (152)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.061487, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:151", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 151}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=151", "ajax": false, "filename": "ProductController.php", "line": "151"}, "connection": "auvista", "explain": null, "start_percent": 45.403, "width_percent": 24.015}, {"sql": "select * from `products` where `category_id` = 7 and `id` != 152 and `status` = 1 limit 4", "type": "query", "params": [], "bindings": [7, 152, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 244}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.063949, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:244", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=244", "ajax": false, "filename": "ProductController.php", "line": "244"}, "connection": "auvista", "explain": null, "start_percent": 69.418, "width_percent": 15.76}, {"sql": "select count(*) as aggregate from `product_reviews` where `product_reviews`.`product_id` = 152 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 247}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.065644, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:247", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 247}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=247", "ajax": false, "filename": "ProductController.php", "line": "247"}, "connection": "auvista", "explain": null, "start_percent": 85.178, "width_percent": 9.006}, {"sql": "select count(*) as aggregate from `product_reviews` where `product_reviews`.`product_id` = 152 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [152], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 275}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.0667121, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:275", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/ProductController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\ProductController.php", "line": 275}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=275", "ajax": false, "filename": "ProductController.php", "line": "275"}, "connection": "auvista", "explain": null, "start_percent": 94.184, "width_percent": 5.816}]}, "models": {"data": {"App\\Models\\Product": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 7, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://auvista.test/san-pham/test", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\ProductController@show", "uri": "GET san-pham/{slug}", "controller": "App\\Http\\Controllers\\ProductController@show<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=136\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/san-pham", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FProductController.php&line=136\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/ProductController.php:136-314</a>", "middleware": "web", "duration": "1.74s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1032073569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1032073569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1730526043 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1730526043\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-690334875 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">https://auvista.test/san-pham</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjFKZ0lqblhlSStiQU9yaG5PRkpwTXc9PSIsInZhbHVlIjoiS3gzMXNCZlBNUXhSUWwrUzFvTE5BUzN5UEJYZmp5WFFyWUpUV1ZkR2Y2U2RQcDcwUCsrU2piVXNsV082c1BPQ1QxekxSZVVsZUJidnNtS1Z5anU1Sm01QzVjdDQ3MGRwU1lmeUNvbWFJZXV5UjRmZ2R5RW9wNUFmSDRuT3FWc1kiLCJtYWMiOiI3M2IwY2E2ZDY4NDI4Y2U2MjI5Mzc2YmY1ZjA0MWU4YjVhNTkxZTk5MmZmYzNlMDYwNzY5YjkyMmY4MjcwYmZlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IitOcHBTU1lJTnlpY3ppNGM4dWF2R3c9PSIsInZhbHVlIjoiejFObU1rRUdXZTFXdk95dXJQdGRBbVJvRFdvUVpSZVdDQnZ5dmx1Zm1ORWZQdTlCWm8xR2FqZElHSmhxTktIS2hTVlB0YlNWVk1weTJMeUVDRkErc3VpTHc2Q25OY0VUVERIanRjNG1BMlRMRDNmTnBrdkRrTS9MVWxKaUQ2TzMiLCJtYWMiOiJkNDU5MjA3NWQ4YzdmN2E4NGFiNWVmZjM4YWVlN2IxOTk0ZDYwNThjZTEzY2MwMjI5MzMyZDgwMDAzZDlkMDBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690334875\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1398262630 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1398262630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-235831462 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 06:32:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-235831462\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-890187879 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://auvista.test/san-pham/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>152</span> => <span class=sf-dump-num>1</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890187879\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://auvista.test/san-pham/test", "action_name": "products.show", "controller_action": "App\\Http\\Controllers\\ProductController@show"}, "badge": "500 Internal Server Error"}}