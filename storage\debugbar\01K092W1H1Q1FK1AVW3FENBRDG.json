{"__meta": {"id": "01K092W1H1Q1FK1AVW3FENBRDG", "datetime": "2025-07-16 14:41:02", "utime": **********.88266, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[14:41:02] LOG.info: CartPopup loadCart called: {\n    \"cartItems\": [],\n    \"session_cart\": [],\n    \"total\": 0,\n    \"session_id\": \"wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.84745, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752651661.547648, "end": **********.882696, "duration": 1.335047960281372, "duration_str": "1.34s", "measures": [{"label": "Booting", "start": 1752651661.547648, "relative_start": 0, "end": **********.40941, "relative_end": **********.40941, "duration": 0.****************, "duration_str": "862ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.409426, "relative_start": 0.****************, "end": **********.882698, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "473ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.667661, "relative_start": 1.****************, "end": **********.674247, "relative_end": **********.674247, "duration": 0.0065860748291015625, "duration_str": "6.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.794937, "relative_start": 1.****************, "end": **********.879128, "relative_end": **********.879128, "duration": 0.*****************, "duration_str": "84.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.auvista.pages.home", "start": **********.798207, "relative_start": 1.****************, "end": **********.798207, "relative_end": **********.798207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.layouts.default", "start": **********.805594, "relative_start": 1.****************, "end": **********.805594, "relative_end": **********.805594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.components.product_styles", "start": **********.811108, "relative_start": 1.2634601593017578, "end": **********.811108, "relative_end": **********.811108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.header", "start": **********.811652, "relative_start": 1.2640039920806885, "end": **********.811652, "relative_end": **********.811652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.824857, "relative_start": 1.2772090435028076, "end": **********.824857, "relative_end": **********.824857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.839506, "relative_start": 1.2918579578399658, "end": **********.839506, "relative_end": **********.839506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.breadcrumb", "start": **********.840427, "relative_start": 1.2927789688110352, "end": **********.840427, "relative_end": **********.840427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "start": **********.848559, "relative_start": 1.3009109497070312, "end": **********.848559, "relative_end": **********.848559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.footer", "start": **********.85712, "relative_start": 1.3094720840454102, "end": **********.85712, "relative_end": **********.85712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 42992568, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "templates.auvista.pages.home", "param_count": null, "params": [], "start": **********.798186, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/pages/home.blade.phptemplates.auvista.pages.home", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fpages%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "templates.auvista.layouts.default", "param_count": null, "params": [], "start": **********.805577, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/layouts/default.blade.phptemplates.auvista.layouts.default", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "templates.auvista.components.product_styles", "param_count": null, "params": [], "start": **********.811089, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/components/product_styles.blade.phptemplates.auvista.components.product_styles", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fcomponents%2Fproduct_styles.blade.php&line=1", "ajax": false, "filename": "product_styles.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.header", "param_count": null, "params": [], "start": **********.811636, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.phptemplates.auvista.blocks.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.824838, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.839491, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.breadcrumb", "param_count": null, "params": [], "start": **********.840413, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/breadcrumb.blade.phptemplates.auvista.blocks.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "param_count": null, "params": [], "start": **********.848543, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.phpH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-popup.blade.php&line=1", "ajax": false, "filename": "cart-popup.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.footer", "param_count": null, "params": [], "start": **********.8571, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.phptemplates.auvista.blocks.footer", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.052489999999999995, "accumulated_duration_str": "52.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `banners` where `position` = 'slider' and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": ["slider", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BannerRepository.php", "line": 42}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.711585, "duration": 0.01889, "duration_str": "18.89ms", "memory": 0, "memory_str": null, "filename": "BannerRepository.php:42", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BannerRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BannerRepository.php", "line": 42}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FBannerRepository.php&line=42", "ajax": false, "filename": "BannerRepository.php", "line": "42"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 35.988}, {"sql": "select * from `categories` where `status` = 1 order by `updated_at` asc limit 3", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/CategoryRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\CategoryRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 62}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.732105, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "CategoryRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/CategoryRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\CategoryRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FCategoryRepository.php&line=29", "ajax": false, "filename": "CategoryRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 35.988, "width_percent": 5.963}, {"sql": "select * from `products` where `is_new` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7361372, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 41.951, "width_percent": 2.934}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7406602, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 44.885, "width_percent": 1.524}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.742215, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:29", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=29", "ajax": false, "filename": "ProductRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 46.409, "width_percent": 0.819}, {"sql": "select * from `products` where `is_best_seller` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.743277, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 47.228, "width_percent": 0.953}, {"sql": "select * from `categories` where `categories`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.744435, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 48.181, "width_percent": 0.552}, {"sql": "select * from `brands` where `brands`.`id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.745339, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:45", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=45", "ajax": false, "filename": "ProductRepository.php", "line": "45"}, "connection": "auvista", "explain": null, "start_percent": 48.733, "width_percent": 0.457}, {"sql": "select * from `products` where `is_featured` = 1 and `status` = 1 order by `created_at` desc limit 8", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.746276, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 15, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 49.19, "width_percent": 1.429}, {"sql": "select * from `categories` where `categories`.`id` in (3, 5, 7, 9, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.747809, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 50.619, "width_percent": 0.781}, {"sql": "select * from `brands` where `brands`.`id` in (2, 3, 5, 11, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.748917, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:61", "source": {"index": 20, "namespace": null, "name": "app/Repositories/ProductRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\ProductRepository.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FProductRepository.php&line=61", "ajax": false, "filename": "ProductRepository.php", "line": "61"}, "connection": "auvista", "explain": null, "start_percent": 51.4, "width_percent": 0.61}, {"sql": "select `brands`.*, (select count(*) from `products` where `brands`.`id` = `products`.`brand_id`) as `products_count` from `brands` where `status` = 1 order by `products_count` desc, `name` asc limit 3", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BrandRepository.php", "line": 29}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 76}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7527552, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "BrandRepository.php:29", "source": {"index": 15, "namespace": null, "name": "app/Repositories/BrandRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\BrandRepository.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FBrandRepository.php&line=29", "ajax": false, "filename": "BrandRepository.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 52.01, "width_percent": 3.239}, {"sql": "select * from `posts` where `status` = 'published' and `published_at` is not null order by `published_at` desc limit 3", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.755423, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 15, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 55.249, "width_percent": 4.725}, {"sql": "select * from `users` where `users`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.768783, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 20, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 59.973, "width_percent": 5.925}, {"sql": "select `categories`.*, `post_category`.`post_id` as `pivot_post_id`, `post_category`.`category_id` as `pivot_category_id`, `post_category`.`is_primary` as `pivot_is_primary`, `post_category`.`created_at` as `pivot_created_at`, `post_category`.`updated_at` as `pivot_updated_at` from `categories` inner join `post_category` on `categories`.`id` = `post_category`.`category_id` where `post_category`.`post_id` in (15, 16, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 77}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.7793748, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "PostRepository.php:144", "source": {"index": 19, "namespace": null, "name": "app/Repositories/PostRepository.php", "file": "H:\\laragon\\www\\auvista\\app\\Repositories\\PostRepository.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FRepositories%2FPostRepository.php&line=144", "ajax": false, "filename": "PostRepository.php", "line": "144"}, "connection": "auvista", "explain": null, "start_percent": 65.898, "width_percent": 4.782}, {"sql": "select * from `page_translations` where `type` = 'static_page' and `code` = 'home' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["static_page", "home", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 20}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 80}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.786182, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "TranslationHelper.php:20", "source": {"index": 16, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FTranslationHelper.php&line=20", "ajax": false, "filename": "TranslationHelper.php", "line": "20"}, "connection": "auvista", "explain": null, "start_percent": 70.68, "width_percent": 3.124}, {"sql": "select * from `static_pages` where `static_pages`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 21}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 80}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.789418, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "TranslationHelper.php:21", "source": {"index": 21, "namespace": null, "name": "app/Helpers/TranslationHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\TranslationHelper.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FTranslationHelper.php&line=21", "ajax": false, "filename": "TranslationHelper.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 73.805, "width_percent": 1.943}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 86}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.7911391, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HomeController.php:86", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\HomeController.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=86", "ajax": false, "filename": "HomeController.php", "line": "86"}, "connection": "auvista", "explain": null, "start_percent": 75.748, "width_percent": 0.61}, {"sql": "select * from `settings` where `name` = 'site_logo' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_logo", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.806653, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 76.357, "width_percent": 1.467}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8124878, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 77.824, "width_percent": 1.067}, {"sql": "select * from `menus` where `location` = 'main-menu' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["main-menu", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8329, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 78.891, "width_percent": 2.191}, {"sql": "select * from `menu_items` where `menu_id` = 5 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.835562, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 81.082, "width_percent": 2.115}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.837729, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 83.197, "width_percent": 0.876}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.858009, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 84.073, "width_percent": 1.257}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.859339, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 85.331, "width_percent": 0.762}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8604221, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 86.093, "width_percent": 1.581}, {"sql": "select * from `settings` where `name` = 'contact_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8621678, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 87.674, "width_percent": 1.486}, {"sql": "select * from `settings` where `name` = 'office_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["office_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.864002, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 89.16, "width_percent": 1.162}, {"sql": "select * from `settings` where `name` = 'contact_email' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_email", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8655698, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 90.322, "width_percent": 0.8}, {"sql": "select * from `menus` where `location` = 'footer-support' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-support", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.866626, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 91.122, "width_percent": 0.762}, {"sql": "select * from `menu_items` where `menu_id` = 6 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.86764, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 91.884, "width_percent": 1.105}, {"sql": "select * from `menus` where `location` = 'footer-policy' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-policy", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.869008, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 92.989, "width_percent": 0.724}, {"sql": "select * from `menu_items` where `menu_id` = 7 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.8700001, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:28", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=28", "ajax": false, "filename": "MenuHelper.php", "line": "28"}, "connection": "auvista", "explain": null, "start_percent": 93.713, "width_percent": 0.991}, {"sql": "select * from `settings` where `name` = 'facebook_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["facebook_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.871509, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 94.704, "width_percent": 2.61}, {"sql": "select * from `settings` where `name` = 'instagram_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["instagram_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.873551, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 97.314, "width_percent": 0.667}, {"sql": "select * from `settings` where `name` = 'youtube_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["youtube_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.874491, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 97.981, "width_percent": 0.629}, {"sql": "select * from `settings` where `name` = 'site_copyright' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_copyright", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.875418, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 98.609, "width_percent": 0.61}, {"sql": "select * from `settings` where `name` = 'designer_credit' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["designer_credit", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.876483, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 99.219, "width_percent": 0.781}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Setting": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Product": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Brand": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "App\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PageTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPageTranslation.php&line=1", "ajax": false, "filename": "PageTranslation.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}}, "count": 67, "is_counter": true}, "livewire": {"data": {"cart-count #cKdR9bZFEzRwfjyxQNAV": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"cKdR9bZFEzRwfjyxQNAV\"\n]", "cart-count #aNE5iFHWoxYuCqdBc2hO": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"aNE5iFHWoxYuCqdBc2hO\"\n]", "cart-popup #m1dPBPb16hYasNfOPqeR": "array:4 [\n  \"data\" => array:2 [\n    \"cartItems\" => []\n    \"total\" => 0\n  ]\n  \"name\" => \"cart-popup\"\n  \"component\" => \"App\\Livewire\\CartPopup\"\n  \"id\" => \"m1dPBPb16hYasNfOPqeR\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index", "uri": "GET /", "controller": "App\\Http\\Controllers\\HomeController@index<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=58\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=58\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/HomeController.php:58-101</a>", "middleware": "web", "duration": "1.29s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1660782702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1660782702\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1071374252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1071374252\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">https://auvista.test/san-pham?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkRhNHJkY1dVMXVUaWR3Yi9ZcjI0dkE9PSIsInZhbHVlIjoiTGQwV2p2ZnhOK0RXd1Y0akREZ1czUEliSWdxYW5BaFZvMS9Hb2NHOHZzakNWYXNPYnYvY1JZaWVVMDR6ZndYbVJ0VnFuWm9Ea2cxdUxjTkVqcnBVVW56bW5ySTVRZkY4cnVBRGFPL28xMFR1MmgxdGQ3S3V1dmJHNlczR0dYNEMiLCJtYWMiOiJlODYzYjkxNTViY2IzZWQzYTkyYTIwNTRkZDAzMzMwNjJmZTExMjE4NmEyZDlmMzY4YTBlM2EwY2Q4ZTU0MGM0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InF0RFJ1T2Nqektuc3FZSnU4YkY3VkE9PSIsInZhbHVlIjoiZmdKcnRBNnhST3BjYXUyS0lUZEZMTWRzV3pBRlZrdjlrM3JUTFgyNGdOV3JJWUdlaVI5SkNGWDRFNGJiVmo0bU9mRzVNRmU2S2NnVXlxVkNwUnZuYzBzVzdWVVZWMUdRTG5pc0xCSnM0T1ZoZXVIV3lTYjNIYkRhdkVsYURUUE0iLCJtYWMiOiIzMWE2NDA4OTAyNmFlMDU0ZWU3M2Q1ZDdmY2RhYjE0YTljNDM2Mzk0M2ZhOWMzM2VjZmIwMzhlY2UxNjRkOGVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-729951143 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wbuDXXcLTPxaqdyenlsh54PZ03WXIxpJ2BmrVd9u</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729951143\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-63577339 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 16 Jul 2025 07:41:02 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63577339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-562654672 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Whhr5DGICuGQXSU9IxiCYWmvVYRFZnG0B3qaMekI</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>152</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>126</span>\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-num>128</span>\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-num>124</span>\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-num>122</span>\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-num>138</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-562654672\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test", "action_name": "home", "controller_action": "App\\Http\\Controllers\\HomeController@index"}, "badge": null}}