<?php

namespace App\Filament\Resources\ProductReviewResource\Pages;

use App\Filament\Resources\ProductReviewResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\IconEntry;
use Filament\Support\Enums\FontWeight;

class ViewProductReview extends ViewRecord
{
    protected static string $resource = ProductReviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Thông tin sản phẩm')
                    ->schema([
                        TextEntry::make('product.name')
                            ->label('Tên sản phẩm')
                            ->weight(FontWeight::Bold),
                        
                        TextEntry::make('product.sku')
                            ->label('Mã sản phẩm'),
                    ])
                    ->columns(2),

                Section::make('Thông tin người đánh giá')
                    ->schema([
                        TextEntry::make('reviewer_name')
                            ->label('Tên người đánh giá')
                            ->weight(FontWeight::Bold),
                        
                        TextEntry::make('reviewer_email')
                            ->label('Email')
                            ->copyable(),
                        
                        TextEntry::make('reviewer_phone')
                            ->label('Số điện thoại')
                            ->copyable(),
                        
                        TextEntry::make('user.name')
                            ->label('Tài khoản liên kết')
                            ->placeholder('Không có'),
                    ])
                    ->columns(2),

                Section::make('Nội dung đánh giá')
                    ->schema([
                        TextEntry::make('rating')
                            ->label('Đánh giá sao')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                '1' => 'danger',
                                '2' => 'warning', 
                                '3' => 'gray',
                                '4' => 'success',
                                '5' => 'success',
                                default => 'gray',
                            })
                            ->formatStateUsing(fn (string $state): string => $state . ' ⭐'),
                        
                        TextEntry::make('comment')
                            ->label('Bình luận')
                            ->columnSpanFull()
                            ->prose(),
                        
                        IconEntry::make('recommend')
                            ->label('Đề xuất sản phẩm')
                            ->boolean(),
                        
                        IconEntry::make('verified_purchase')
                            ->label('Đã mua hàng')
                            ->boolean(),
                        
                        TextEntry::make('helpful_count')
                            ->label('Số lượt hữu ích')
                            ->numeric(),
                        
                        IconEntry::make('status')
                            ->label('Hiển thị')
                            ->boolean(),
                    ])
                    ->columns(3),

                Section::make('Phản hồi từ Admin')
                    ->schema([
                        TextEntry::make('admin_reply')
                            ->label('Nội dung phản hồi')
                            ->columnSpanFull()
                            ->prose()
                            ->placeholder('Chưa có phản hồi'),
                        
                        TextEntry::make('admin.name')
                            ->label('Admin phản hồi')
                            ->placeholder('Chưa có'),
                        
                        TextEntry::make('admin_reply_at')
                            ->label('Thời gian phản hồi')
                            ->dateTime('d/m/Y H:i')
                            ->placeholder('Chưa có'),
                    ])
                    ->columns(2),

                Section::make('Thông tin hệ thống')
                    ->schema([
                        TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),
                        
                        TextEntry::make('updated_at')
                            ->label('Cập nhật lần cuối')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),
            ]);
    }
}
