<?php

namespace Database\Seeders;

use App\Models\StaticPage;
use App\Models\PageTranslation;
use App\Models\User;
use Illuminate\Database\Seeder;

class StaticPageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Lấy user đầu tiên từ bảng users
        $admin = User::first();

        if (!$admin) {
            $this->command->warn('Không tìm thấy user nào trong database. Vui lòng chạy DatabaseSeeder trước.');
            return;
        }

        $pages = [
            // Customer Support Pages
            // Home
            [
                'code' => 'home',
                'vi' => [
                    'title' => 'Trang chủ',
                    'slug' => 'trang-chu',
                    'content' => '<h2>Trang chủ</h2><p>Nội dung trang chủ sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Trang chủ - AV Plus',
                    'seo_description' => 'Trang chủ của AV Plus',
                ],
                'en' => [
                    'title' => 'Home',
                    'slug' => 'home',
                    'content' => '<h2>Home</h2><p>Home content will be updated here.</p>',
                    'seo_title' => 'Home - AV Plus',
                    'seo_description' => 'Home page of AV Plus',
                ]
            ],
            [
                'code' => 'shopping-guide',
                'vi' => [
                    'title' => 'Hướng dẫn mua hàng',
                    'slug' => 'huong-dan-mua-hang',
                    'content' => '<h2>Hướng dẫn mua hàng</h2><p>Nội dung hướng dẫn mua hàng sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Hướng dẫn mua hàng - AV Plus',
                    'seo_description' => 'Hướng dẫn chi tiết cách mua hàng tại AV Plus',
                ],
                'en' => [
                    'title' => 'Shopping Guide',
                    'slug' => 'shopping-guide',
                    'content' => '<h2>Shopping Guide</h2><p>Shopping guide content will be updated here.</p>',
                    'seo_title' => 'Shopping Guide - AV Plus',
                    'seo_description' => 'Detailed guide on how to shop at AV Plus',
                ]
            ],
            [
                'code' => 'payment-guide',
                'vi' => [
                    'title' => 'Hướng dẫn thanh toán',
                    'slug' => 'huong-dan-thanh-toan',
                    'content' => '<h2>Hướng dẫn thanh toán</h2><p>Nội dung hướng dẫn thanh toán sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Hướng dẫn thanh toán - AV Plus',
                    'seo_description' => 'Hướng dẫn chi tiết các phương thức thanh toán',
                ],
                'en' => [
                    'title' => 'Payment Guide',
                    'slug' => 'payment-guide',
                    'content' => '<h2>Payment Guide</h2><p>Payment guide content will be updated here.</p>',
                    'seo_title' => 'Payment Guide - AV Plus',
                    'seo_description' => 'Detailed guide on payment methods',
                ]
            ],
            [
                'code' => 'agent-registration',
                'vi' => [
                    'title' => 'Đăng ký đại lý',
                    'slug' => 'dang-ky-dai-ly',
                    'content' => '<h2>Đăng ký đại lý</h2><p>Nội dung đăng ký đại lý sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Đăng ký đại lý - AV Plus',
                    'seo_description' => 'Thông tin đăng ký làm đại lý của AV Plus',
                ],
                'en' => [
                    'title' => 'Agent Registration',
                    'slug' => 'agent-registration',
                    'content' => '<h2>Agent Registration</h2><p>Agent registration content will be updated here.</p>',
                    'seo_title' => 'Agent Registration - AV Plus',
                    'seo_description' => 'Information on becoming an AV Plus agent',
                ]
            ],
            // Policy Pages
            [
                'code' => 'warranty-policy',
                'vi' => [
                    'title' => 'Chính sách bảo hành',
                    'slug' => 'chinh-sach-bao-hanh',
                    'content' => '<h2>Chính sách bảo hành</h2><p>Nội dung chính sách bảo hành sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Chính sách bảo hành - AV Plus',
                    'seo_description' => 'Chính sách bảo hành sản phẩm của AV Plus',
                ],
                'en' => [
                    'title' => 'Warranty Policy',
                    'slug' => 'warranty-policy',
                    'content' => '<h2>Warranty Policy</h2><p>Warranty policy content will be updated here.</p>',
                    'seo_title' => 'Warranty Policy - AV Plus',
                    'seo_description' => 'AV Plus product warranty policy',
                ]
            ],
            [
                'code' => 'privacy-policy',
                'vi' => [
                    'title' => 'Chính sách bảo mật',
                    'slug' => 'chinh-sach-bao-mat',
                    'content' => '<h2>Chính sách bảo mật</h2><p>Nội dung chính sách bảo mật sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Chính sách bảo mật - AV Plus',
                    'seo_description' => 'Chính sách bảo mật thông tin của AV Plus',
                ],
                'en' => [
                    'title' => 'Privacy Policy',
                    'slug' => 'privacy-policy',
                    'content' => '<h2>Privacy Policy</h2><p>Privacy policy content will be updated here.</p>',
                    'seo_title' => 'Privacy Policy - AV Plus',
                    'seo_description' => 'AV Plus privacy and data protection policy',
                ]
            ],
            [
                'code' => 'shopping-policy',
                'vi' => [
                    'title' => 'Chính sách mua hàng',
                    'slug' => 'chinh-sach-mua-hang',
                    'content' => '<h2>Chính sách mua hàng</h2><p>Nội dung chính sách mua hàng sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Chính sách mua hàng - AV Plus',
                    'seo_description' => 'Chính sách mua hàng của AV Plus',
                ],
                'en' => [
                    'title' => 'Shopping Policy',
                    'slug' => 'shopping-policy',
                    'content' => '<h2>Shopping Policy</h2><p>Shopping policy content will be updated here.</p>',
                    'seo_title' => 'Shopping Policy - AV Plus',
                    'seo_description' => 'AV Plus shopping policy',
                ]
            ],
            [
                'code' => 'shipping-policy',
                'vi' => [
                    'title' => 'Chính sách vận chuyển',
                    'slug' => 'chinh-sach-van-chuyen',
                    'content' => '<h2>Chính sách vận chuyển</h2><p>Nội dung chính sách vận chuyển sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Chính sách vận chuyển - AV Plus',
                    'seo_description' => 'Chính sách vận chuyển và giao hàng của AV Plus',
                ],
                'en' => [
                    'title' => 'Shipping Policy',
                    'slug' => 'shipping-policy',
                    'content' => '<h2>Shipping Policy</h2><p>Shipping policy content will be updated here.</p>',
                    'seo_title' => 'Shipping Policy - AV Plus',
                    'seo_description' => 'AV Plus shipping and delivery policy',
                ]
            ],
            [
                'code' => 'return-policy',
                'vi' => [
                    'title' => 'Chính sách đổi trả',
                    'slug' => 'chinh-sach-doi-tra',
                    'content' => '<h2>Chính sách đổi trả</h2><p>Nội dung chính sách đổi trả sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Chính sách đổi trả - AV Plus',
                    'seo_description' => 'Chính sách đổi trả và hoàn tiền của AV Plus',
                ],
                'en' => [
                    'title' => 'Return Policy',
                    'slug' => 'return-policy',
                    'content' => '<h2>Return Policy</h2><p>Return policy content will be updated here.</p>',
                    'seo_title' => 'Return Policy - AV Plus',
                    'seo_description' => 'AV Plus return and refund policy',
                ]
            ],
            // Main Menu Pages
            [
                'code' => 'about-us',
                'vi' => [
                    'title' => 'Về chúng tôi',
                    'slug' => 've-chung-toi',
                    'content' => '<h2>Về chúng tôi</h2><p>Nội dung về chúng tôi sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Về chúng tôi - AV Plus',
                    'seo_description' => 'Thông tin về công ty AV Plus',
                ],
                'en' => [
                    'title' => 'About Us',
                    'slug' => 'about-us',
                    'content' => '<h2>About Us</h2><p>About us content will be updated here.</p>',
                    'seo_title' => 'About Us - AV Plus',
                    'seo_description' => 'Information about AV Plus company',
                ]
            ],
            [
                'code' => 'solutions',
                'vi' => [
                    'title' => 'Giải pháp',
                    'slug' => 'giai-phap',
                    'content' => '<h2>Giải pháp</h2><p>Nội dung về giải pháp sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Giải pháp - AV Plus',
                    'seo_description' => 'Các giải pháp âm thanh của AV Plus',
                ],
                'en' => [
                    'title' => 'Solutions',
                    'slug' => 'solutions',
                    'content' => '<h2>Solutions</h2><p>Solutions content will be updated here.</p>',
                    'seo_title' => 'Solutions - AV Plus',
                    'seo_description' => 'AV Plus audio solutions',
                ]
            ],
            [
                'code' => 'brands',
                'vi' => [
                    'title' => 'Thương hiệu',
                    'slug' => 'thuong-hieu',
                    'content' => '<h2>Thương hiệu</h2><p>Nội dung về thương hiệu sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Thương hiệu - AV Plus',
                    'seo_description' => 'Các thương hiệu đối tác của AV Plus',
                ],
                'en' => [
                    'title' => 'Brands',
                    'slug' => 'brands',
                    'content' => '<h2>Brands</h2><p>Brands content will be updated here.</p>',
                    'seo_title' => 'Brands - AV Plus',
                    'seo_description' => 'AV Plus partner brands',
                ]
            ],
            [
                'code' => 'projects',
                'vi' => [
                    'title' => 'Dự án',
                    'slug' => 'du-an',
                    'content' => '<h2>Dự án</h2><p>Nội dung về dự án sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Dự án - AV Plus',
                    'seo_description' => 'Các dự án đã thực hiện của AV Plus',
                ],
                'en' => [
                    'title' => 'Projects',
                    'slug' => 'projects',
                    'content' => '<h2>Projects</h2><p>Projects content will be updated here.</p>',
                    'seo_title' => 'Projects - AV Plus',
                    'seo_description' => 'AV Plus completed projects',
                ]
            ],
            [
                'code' => 'contact',
                'vi' => [
                    'title' => 'Liên hệ',
                    'slug' => 'lien-he',
                    'content' => '<h2>Liên hệ</h2><p>Nội dung liên hệ sẽ được cập nhật tại đây.</p>',
                    'seo_title' => 'Liên hệ - AV Plus',
                    'seo_description' => 'Thông tin liên hệ của AV Plus',
                ],
                'en' => [
                    'title' => 'Contact',
                    'slug' => 'contact',
                    'content' => '<h2>Contact</h2><p>Contact content will be updated here.</p>',
                    'seo_title' => 'Contact - AV Plus',
                    'seo_description' => 'AV Plus contact information',
                ]
            ],
        ];

        foreach ($pages as $pageData) {
            $code = $pageData['code'];

            // Create Vietnamese page
            $viPage = StaticPage::firstOrCreate(
                ['slug' => $pageData['vi']['slug'], 'lang' => 'vi'],
                [
                    'title' => $pageData['vi']['title'],
                    'content' => $pageData['vi']['content'],
                    'seo_title' => $pageData['vi']['seo_title'],
                    'seo_description' => $pageData['vi']['seo_description'],
                    'status' => 'published',
                    'author_id' => $admin->id,
                    'lang' => 'vi',
                ]
            );

            // Create English page
            $enPage = StaticPage::firstOrCreate(
                ['slug' => $pageData['en']['slug'], 'lang' => 'en'],
                [
                    'title' => $pageData['en']['title'],
                    'content' => $pageData['en']['content'],
                    'seo_title' => $pageData['en']['seo_title'],
                    'seo_description' => $pageData['en']['seo_description'],
                    'status' => 'published',
                    'author_id' => $admin->id,
                    'lang' => 'en',
                ]
            );

            // Create page translations
            PageTranslation::firstOrCreate(
                ['type' => 'static_page', 'code' => $code, 'lang' => 'vi'],
                ['page_id' => $viPage->id]
            );

            PageTranslation::firstOrCreate(
                ['type' => 'static_page', 'code' => $code, 'lang' => 'en'],
                ['page_id' => $enPage->id]
            );
        }

        $this->command->info('Đã tạo thành công ' . count($pages) * 2 . ' trang tĩnh và ' . count($pages) * 2 . ' bản dịch cho 2 ngôn ngữ.');
    }
}
