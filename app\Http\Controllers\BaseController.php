<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\View;

class BaseController extends Controller
{
    use AuthorizesRequests, ValidatesRequests;
    
    /**
     * Đường dẫn đến template
     *
     * @var string
     */
    protected $templatePath = 'templates.auvista';
    
    /**
     * Layout sử dụng cho template
     *
     * @var string
     */
    protected $layout = 'layouts.default';
    
    /**
     * Khởi tạo controller
     */
    public function __construct()
    {
        // Thiết lập biến layout cho tất cả view
        View::share('templatePath', $this->templatePath);
        View::share('layout', $this->templatePath . '.' . $this->layout);
    }
    
    /**
     * Lấy đường dẫn đầy đủ đến view
     *
     * @param string $viewName Tên view
     * @return string
     */
    protected function getViewPath($viewName)
    {
        return 'templates.auvista.' . $viewName;
    }
} 