@extends('templates.auvista.layouts.default')

@section('content')
    <!-- Main Content -->
    <main id="main" id="site-main">
        <div class="page-wrapper page-about">
            <!-- <PERSON> Slider -->
            <div
                class="categories-swiper swiper-slider"
                data-items="1"
                data-mobile="1"
                data-tablet="1"
                data-desktop="1"
                data-large="1"
                data-xlarge="1"
                data-spacing="0"
                data-loop="true"
                data-navigation="true"
                data-autoplay="true"
            >
                <div class="swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide relative group overflow-hidden">
                            <img
                                src="{{ asset('storage/images/img-slide-1.jpg') }}"
                                alt="demo-1"
                                class="w-full h-full object-cover group-hover:scale-110 duration-500"
                            />
                            <!-- Text Overlay -->
                            <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
                                <div class="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                    <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                                        Lorem ipsum dolor sit amet
                                    </h3>
                                    <p class="text-lg md:text-xl max-w-2xl mx-auto px-4">
                                        Consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide relative group overflow-hidden">
                            <img
                                src="{{ asset('storage/images/img-slide-2.jpg') }}"
                                alt="demo-2"
                                class="w-full h-full object-cover group-hover:scale-110 duration-500"
                            />
                            <!-- Text Overlay -->
                            <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
                                <div class="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                    <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                                        Lorem ipsum dolor sit amet
                                    </h3>
                                    <p class="text-lg md:text-xl max-w-2xl mx-auto px-4">
                                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide relative group overflow-hidden">
                            <img
                                src="{{ asset('storage/images/img-slide-3.jpg') }}"
                                alt="demo-3"
                                class="w-full h-full object-cover group-hover:scale-110 duration-500"
                            />
                            <!-- Text Overlay -->
                            <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
                                <div class="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                    <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                                        Lorem ipsum dolor sit amet
                                    </h3>
                                    <p class="text-lg md:text-xl max-w-2xl mx-auto px-4">
                                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide relative group overflow-hidden">
                            <img
                                src="{{ asset('storage/images/img-slide-4.jpg') }}"
                                alt="demo-4"
                                class="w-full h-full object-cover group-hover:scale-110 duration-500"
                            />
                            <!-- Text Overlay -->
                            <div class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
                                <div class="text-center text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                                    <h3 class="text-2xl md:text-3xl lg:text-4xl font-bold mb-4">
                                        Lorem ipsum dolor sit amet
                                    </h3>
                                    <p class="text-lg md:text-xl max-w-2xl mx-auto px-4">
                                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Pagination -->
                    <div class="swiper-pagination"></div>
                </div>
            </div>

            <!-- About Section -->
            <div class="relative z-10">
                <div class="bg-gradient8 pt-16 lg:pt-[86px] pb-16 lg:pb-[86px] text-white">
                    <div class="container mx-auto">
                        <div class="grid grid-cols-1 lg:grid-cols-2 items-center gap-4 lg:gap-[30px]">
                            <div class="col-span-1">
                                <div class="rounded-3xl overflow-hidden">
                                    <img
                                        src="{{ asset('storage/images/image-about.png') }}"
                                        alt="image-about"
                                        class="w-full h-auto"
                                    />
                                </div>
                            </div>
                            <div class="col-span-1 lg:-order-1">
                                <h1 class="font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6">
                                    {{ __('messages.about_us') }}
                                </h1>
                                <div class="lg:text-xl leading-[1.4] space-y-5">
                                    <p>
                                        Lorem ipsum dolor sit amet consectetur. Quam amet mauris consectetur vulputate eget nulla phasellus euismod diam. Magna nascetur libero ipsum at ultricies velit arcu. Dictumst neque ullamcorper sem viverra in sit potenti nunc. Mi pretium odio tellus elit pulvinar. Vel pellentesque dui eget nisl est at scelerisque. Ut magna molestie odio in volutpat.
                                    </p>
                                    <p>
                                        Turpis pellentesque vel enim enim adipiscing nunc vitae adipiscing eget. Feugiat pellentesque etiam lectus orci elit. Leo adipiscing consectetur trLorem ipsum dolor sit amet consectetur. Quam amet mauris consectetur vulputate eget nulla phasellus euismod diam. Magna nascetur libero ipsum at ultricies velit arcu.
                                    </p>
                                </div>
                                <div class="mt-[30px]">
                                    <button class="text-secondary-main bg-white rounded-full px-[30px] h-11 font-bold leading-[1.4]">
                                        <span>{{ __('messages.explore_products') }}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <img src="{{ asset('images/wave.svg') }}" alt="wave" />
                </div>
            </div>

            <!-- Services Section -->
            <div class="bg-img1 bg-no-repeat bg-center bg-cover mt-12 lg:mt-[70px] pb-12 lg:pb-[70px]">
                <h2 class="container mx-auto text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                    {{ __('messages.services_provided') }}
                </h2>
                <div class="services-swiper services-slider swiper-slider relative">
                    <div class="container mx-auto">
                        <div class="overflow-x-hidden -mb-[30px] sm:pb-[90px]">
                            <div class="swiper -ml-2 lg:-ml-[15px] -mr-2 lg:-mr-[15px] overflow-visible h-auto">
                                <div class="swiper-wrapper items-start h-auto">
                                    <!-- Service Card 1 -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('storage/images/sv-1.jpg') }}"
                                                    alt="sv-1"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="service-overlay">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 2 -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('storage/images/sv-2.jpg') }}"
                                                    alt="sv-2"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="service-overlay">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 3 -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('storage/images/sv-3.jpg') }}"
                                                    alt="sv-3"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="service-overlay">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 4 -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('storage/images/sv-4.jpg') }}"
                                                    alt="sv-4"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 5 (Duplicate of Card 1) -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('images/sv-1.jpg') }}"
                                                    alt="sv-1"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 6 (Duplicate of Card 2) -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('images/sv-2.jpg') }}"
                                                    alt="sv-2"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 7 (Duplicate of Card 3) -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('images/sv-3.jpg') }}"
                                                    alt="sv-3"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Service Card 8 (Duplicate of Card 4) -->
                                    <div class="swiper-slide px-2 lg:px-[15px]">
                                        <div class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full">
                                            <div class="relative z-10 group-hover:z-0 h-full w-full">
                                                <img
                                                    src="{{ asset('images/sv-4.jpg') }}"
                                                    alt="sv-4"
                                                    class="w-full h-full object-cover"
                                                />
                                                <div class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7">
                                                    <p class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2">
                                                        Lorem ipsum dolor sit amet
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4">
                                                <h3 class="text-xl md:text-2xl font-bold mb-4">
                                                    Lorem ipsum dolor sit amet
                                                </h3>
                                                <div class="w-[50px] h-[2px] bg-white mb-4"></div>
                                                <p class="text-white/90 leading-relaxed">
                                                    Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam sit aliquet enim viverra lobortis fusce mauris at. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate. Diam tortor. Lorem ipsum dolor sit amet consectetur. Venenatis sed habitant id sed dapibus bibendum vulputate.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-button-next bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none right-0"></div>
                    <div class="swiper-button-prev bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none left-0"></div>
                </div>
            </div>

            <!-- Products Section -->
            <div class="bg-white pt-12 lg:pt-[70px] pb-12 lg:pb-[70px]">
                <div class="container mx-auto">
                    <h2 class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10">
                        {{ __('messages.featured_products') }}
                    </h2>
                    <div class="swiper-slider relative -ml-2 -mr-2 lg:-ml-[15px] lg:-mr-[15px]"
                        data-items="1.3"
                        data-mobile="1.7"
                        data-tablet="2.5"
                        data-desktop="3.5"
                        data-large="4"
                        data-xlarge="4"
                        data-spacing="0"
                        data-loop="true"
                        data-navigation="true"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                        data-equal-height="true"
                    >
                        <div class="swiper">
                            <div class="swiper-wrapper items-stretch">
                                @if(isset($featuredProducts) && count($featuredProducts) > 0)
                                    @foreach($featuredProducts as $product)
                                        <!-- Product Card -->
                                        <div class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px] h-auto">
                                            <div class="product-card bg-white rounded-xl shadow-product hover:shadow-product transition-shadow duration-300 overflow-hidden group h-full flex flex-col">
                                                <div class="relative">
                                                    <!-- Product Image -->
                                                    <div class="">
                                                        <a href="{{ route('products.show', $product->slug) }}" class="aspect-square overflow-hidden block">
                                                            @php
                                                                $imagePath = null;
                                                                if ($product->image_url) {
                                                                    $imagePath = getImageUrl($product->image_url, 'product', 400, 400);
                                                                                                                                 } else {
                                                                     // Placeholder images từ storage
                                                                     $placeholders = ['image-product-1.png', 'image-product-2.png', 'image-product-3.png', 'image-product-4.png'];
                                                                     $placeholder = $placeholders[($loop->index) % count($placeholders)];
                                                                     $imagePath = asset('storage/images/' . $placeholder);
                                                                 }
                                                            @endphp
                                                            <img
                                                                src="{{ $imagePath }}"
                                                                alt="{{ $product->name }}"
                                                                class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                            />
                                                        </a>
                                                    </div>

                                                    <!-- Badges -->
                                                    <div class="absolute top-4 left-4 flex flex-col gap-1">
                                                        @if($product->sale_price && $product->sale_price < $product->price)
                                                            @php
                                                                $discount = round((($product->price - $product->sale_price) / $product->price) * 100);
                                                            @endphp
                                                            <span class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1">
                                                                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z" fill="white"/>
                                                                </svg>
                                                                -{{ $discount }}%
                                                            </span>
                                                        @endif
                                                        
                                                        @if($product->is_featured)
                                                            <span class="bg-primary-badge2 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                                                                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M6 1L7.5 4.5H11L8.25 6.75L9.75 10.25L6 8L2.25 10.25L3.75 6.75L1 4.5H4.5L6 1Z" fill="white"/>
                                                                </svg>
                                                                {{ __('messages.featured') }}
                                                            </span>
                                                        @endif
                                                        
                                                        @if($product->is_best_seller)
                                                            <span class="bg-primary-badge3 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1">
                                                                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M6 1.5C3.24 1.5 1 3.74 1 6.5C1 9.26 3.24 11.5 6 11.5C8.76 11.5 11 9.26 11 6.5C11 3.74 8.76 1.5 6 1.5ZM6.165 9C6.075 9.03 5.92 9.03 5.83 9C5.05 8.735 3.3 7.62 3.3 5.73C3.3 4.895 3.97 4.22 4.8 4.22C5.29 4.22 5.725 4.455 6 4.825C6.27 4.46 6.71 4.22 7.2 4.22C8.03 4.22 8.7 4.895 8.7 5.73C8.7 7.62 6.95 8.735 6.165 9Z" fill="white"/>
                                                                </svg>
                                                                {{ __('messages.best_seller') }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>

                                                <!-- Product Info -->
                                                <div class="px-4 pb-4 flex-1 flex flex-col">
                                                    <div>
                                                        <span class="text-sm text-primary-gray font-medium">
                                                            {{ optional($product->category)->name ?? __('messages.audio_equipment') }}
                                                        </span>
                                                    </div>
                                                    <h3 class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4 flex-1">
                                                        <a href="{{ route('products.show', $product->slug) }}" class="text-primary-base hover:text-secondary-main transition-colors">
                                                            {{ $product->name }}
                                                        </a>
                                                    </h3>
                                                    <div class="flex items-center justify-between gap-1 mt-auto">
                                                        <div class="flex flex-col">
                                                            <div class="flex items-center gap-2">
                                                                <span class="text-primary-price font-bold text-lg">
                                                                    {{ formatPrice($product->sale_price ?: $product->price) }}
                                                                </span>
                                                            </div>
                                                            @if($product->sale_price && $product->sale_price < $product->price)
                                                                <span class="text-primary-gray font-medium line-through">
                                                                    {{ formatPrice($product->price) }}
                                                                </span>
                                                            @endif
                                                        </div>
                                                        
                                                        <!-- Add to Cart Button -->
                                                        <button class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg add-to-cart-btn"
                                                            data-product-id="{{ $product->id }}"
                                                            data-quantity="1"
                                                        >
                                                            <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z" fill="white"/>
                                                                <path d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z" fill="white"/>
                                                                <path d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z" fill="white"/>
                                                                <path d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z" fill="white"/>
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <!-- Thông báo khi không có sản phẩm -->
                                    <div class="swiper-slide w-full">
                                        <div class="text-center py-16">
                                            <div class="text-gray-400 mb-4">
                                                <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                </svg>
                                            </div>
                                            <p class="text-lg text-gray-500 mb-2">Chưa có sản phẩm nào</p>
                                            <p class="text-gray-400">Vui lòng quay lại sau</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <!-- Pagination -->
                        <div class="swiper-pagination relative mt-5 lg:mt-[40px]"></div>
                        <!-- Navigation buttons -->
                        <div class="swiper-button-next bg-transparent xl:-right-8"></div>
                        <div class="swiper-button-prev bg-transparent xl:-left-8"></div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="bg-gradient1 pt-12 pb-12 lg:pt-[70px] lg:pb-[70px] relative bg-cover bg-center bg-no-repeat" style="background-image: url('{{ asset('images/bg-home-2.jpg') }}');">
                <div class="container mx-auto relative z-10">
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-[30px] items-center">
                        <!-- Item 1 -->
                        <div class="col-span-1">
                            <div class="relative flex justify-center">
                                <svg width="242" class="h-auto" viewBox="0 0 242 242" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g filter="url(#filter0_d_1_4895)">
                                        <circle cx="121" cy="113" r="103" fill="white" />
                                    </g>
                                    <path d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z" fill="url(#paint0_linear_1_4895)" />
                                    <circle cx="121" cy="113" r="92" fill="white" />
                                    <defs>
                                        <filter id="filter0_d_1_4895" x="0" y="0" width="242" height="242" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="8" />
                                            <feGaussianBlur stdDeviation="9" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4895" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4895" result="shape" />
                                        </filter>
                                        <linearGradient id="paint0_linear_1_4895" x1="24.5742" y1="113" x2="217.425" y2="113" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#04389D" />
                                            <stop offset="1" stop-color="#17C1F5" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main">
                                        <span class="counter" data-target="5000">0</span>+
                                    </span>
                                </div>
                            </div>
                            <p class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center">
                                {{ __('messages.trusted_customers') }}
                            </p>
                        </div>

                        <!-- Item 2 -->
                        <div class="col-span-1">
                            <div class="relative flex justify-center">
                                <svg width="242" class="h-auto" viewBox="0 0 242 242" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g filter="url(#filter0_d_1_4896)">
                                        <circle cx="121" cy="113" r="103" fill="white" />
                                    </g>
                                    <path d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z" fill="url(#paint0_linear_1_4896)" />
                                    <circle cx="121" cy="113" r="92" fill="white" />
                                    <defs>
                                        <filter id="filter0_d_1_4896" x="0" y="0" width="242" height="242" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="8" />
                                            <feGaussianBlur stdDeviation="9" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4896" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4896" result="shape" />
                                        </filter>
                                        <linearGradient id="paint0_linear_1_4896" x1="24.5742" y1="113" x2="217.425" y2="113" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#04389D" />
                                            <stop offset="1" stop-color="#17C1F5" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main">
                                        <span class="counter" data-target="320">0</span>+
                                    </span>
                                </div>
                            </div>
                            <p class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center">
                                {{ __('messages.completed_projects') }}
                            </p>
                        </div>

                        <!-- Item 3 -->
                        <div class="col-span-1">
                            <div class="relative flex justify-center">
                                <svg width="242" class="h-auto" viewBox="0 0 242 242" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g filter="url(#filter0_d_1_4897)">
                                        <circle cx="121" cy="113" r="103" fill="white" />
                                    </g>
                                    <path d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z" fill="url(#paint0_linear_1_4897)" />
                                    <circle cx="121" cy="113" r="92" fill="white" />
                                    <defs>
                                        <filter id="filter0_d_1_4897" x="0" y="0" width="242" height="242" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="8" />
                                            <feGaussianBlur stdDeviation="9" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4897" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4897" result="shape" />
                                        </filter>
                                        <linearGradient id="paint0_linear_1_4897" x1="24.5742" y1="113" x2="217.425" y2="113" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#04389D" />
                                            <stop offset="1" stop-color="#17C1F5" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main">
                                        <span class="counter" data-target="5000">0</span>+
                                    </span>
                                </div>
                            </div>
                            <p class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center">
                                {{ __('messages.participated_projects') }}
                            </p>
                        </div>

                        <!-- Item 4 -->
                        <div class="col-span-1">
                            <div class="relative flex justify-center">
                                <svg width="242" class="h-auto" viewBox="0 0 242 242" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g filter="url(#filter0_d_1_4900)">
                                        <circle cx="121" cy="113" r="103" fill="white" />
                                    </g>
                                    <path d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z" fill="url(#paint0_linear_1_4900)" />
                                    <circle cx="121" cy="113" r="92" fill="white" />
                                    <defs>
                                        <filter id="filter0_d_1_4900" x="0" y="0" width="242" height="242" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                            <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                            <feOffset dy="8" />
                                            <feGaussianBlur stdDeviation="9" />
                                            <feComposite in2="hardAlpha" operator="out" />
                                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
                                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4900" />
                                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4900" result="shape" />
                                        </filter>
                                        <linearGradient id="paint0_linear_1_4900" x1="24.5742" y1="113" x2="217.425" y2="113" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#04389D" />
                                            <stop offset="1" stop-color="#17C1F5" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <span class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main">
                                        <span class="counter" data-target="20000">0</span>+
                                    </span>
                                </div>
                            </div>
                            <p class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center">
                                {{ __('messages.devices_sold') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Partners Section -->
            <div class="pt-12 pb-12 lg:pt-[70px] lg:pb-[70px]">
                <div class="container mx-auto">
                    <div class="grid grid-cols-3 gap-[30px] items-center">
                        <div class="col-span-1">
                            <h2 class="font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6">
                                {{ __('messages.partners') }}
                            </h2>
                            <p class="lg:text-xl leading-[1.4]">
                                {{ __('messages.partners_thank_you') }}
                            </p>
                        </div>
                        <div class="col-span-2">
                            <div class="grid grid-cols-4 gap-4 items-center">
                                @php
                                    $partners = [
                                        ['name' => 'Celto Acoustique', 'image' => 'partner-1.png'],
                                        ['name' => 'JBL', 'image' => 'partner-2.png'],
                                        ['name' => 'TEV', 'image' => 'partner-3.png'],
                                        ['name' => 'TEV Pro', 'image' => 'partner-4.png'],
                                        ['name' => 'NextCorp', 'image' => 'partner-5.png'],
                                        ['name' => 'Bosch', 'image' => 'partner-6.png'],
                                        ['name' => 'Yamaha', 'image' => 'partner-7.png'],
                                        ['name' => 'Celto', 'image' => 'partner-8.png'],
                                        ['name' => 'JBL', 'image' => 'partner-9.png'],
                                        ['name' => 'TEV', 'image' => 'partner-10.png'],
                                        ['name' => 'TEV Pro', 'image' => 'partner-11.png'],
                                        ['name' => 'HP Poly', 'image' => 'partner-12.png'],
                                    ];
                                @endphp
                                
                                @foreach($partners as $partner)
                                    <div class="col-span-1">
                                        <img 
                                            src="{{ asset('images/' . $partner['image']) }}" 
                                            alt="{{ $partner['name'] }}" 
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Customer Testimonials Section -->
            <div class="bg-primary-grey pt-12 pb-12 lg:pt-[70px] lg:pb-[70px]">
                <div class="container mx-auto">
                    <h2 class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6 lg:mb-10">
                        {{ __('messages.customers') }}
                    </h2>
                    <div class="swiper-slider relative"
                        data-items="1"
                        data-mobile="1" 
                        data-tablet="1"
                        data-desktop="1"
                        data-large="1"
                        data-xlarge="1"
                        data-spacing="0"
                        data-loop="true"
                        data-navigation="true"
                        data-autoplay="true"
                        data-autoplay-delay="3000"
                    >
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                <!-- Testimonial 1 -->
                                <div class="swiper-slide">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-[30px] items-center">
                                        <div class="col-span-1">
                                            <div class="relative">
                                                <img 
                                                    src="{{ asset('images/img-customer.jpg') }}" 
                                                    alt="Customer Image" 
                                                    class="w-full aspect-[1.5] h-auto object-cover rounded-[20px]"
                                                />
                                            </div>
                                        </div>
                                        <div class="col-span-1">
                                            <span class="mb-4 block">
                                                <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M13.0419 20.5107C12.5944 20.5107 12.165 20.5785 11.7377 20.6404C11.8761 20.1776 12.0186 19.7068 12.2473 19.2839C12.476 18.6695 12.8332 18.1369 13.1883 17.6004C13.4853 17.0199 14.009 16.6269 14.3942 16.1302C14.7975 15.6475 15.3473 15.3263 15.7827 14.9254C16.21 14.5065 16.7698 14.2971 17.2153 14.0018C17.6808 13.7365 18.0861 13.4433 18.5194 13.3037L19.6009 12.8608L20.552 12.4679L19.5788 8.60205L18.381 8.88929C17.9978 8.98504 17.5303 9.09675 16.9986 9.2304C16.4548 9.33013 15.875 9.60341 15.2289 9.85276C14.5908 10.136 13.8525 10.3275 13.1663 10.7823C12.476 11.2172 11.6795 11.5802 10.9772 12.1627C10.2971 12.7631 9.47642 13.2837 8.87047 14.0477C8.20835 14.7618 7.55425 15.5119 7.04662 16.3656C6.45873 17.1795 6.05945 18.0731 5.6381 18.9568C5.25687 19.8405 4.94989 20.7441 4.69908 21.6218C4.22356 23.3811 4.01087 25.0527 3.92861 26.483C3.86039 27.9152 3.90052 29.1061 3.98479 29.9678C4.01489 30.3747 4.07107 30.7697 4.11119 31.043L4.16136 31.3781L4.21352 31.3661C4.57039 33.0234 5.39191 34.5464 6.58306 35.7589C7.7742 36.9714 9.28629 37.8239 10.9444 38.2177C12.6025 38.6116 14.3389 38.5307 15.9527 37.9844C17.5665 37.4381 18.9918 36.4487 20.0636 35.1308C21.1355 33.8128 21.8101 32.2201 22.0095 30.5369C22.2089 28.8537 21.9248 27.1487 21.1903 25.6193C20.4557 24.0899 19.3006 22.7984 17.8586 21.8944C16.4166 20.9903 14.7466 20.5106 13.0419 20.5107ZM35.1127 20.5107C34.6653 20.5107 34.2359 20.5785 33.8085 20.6404C33.947 20.1776 34.0894 19.7068 34.3182 19.2839C34.5469 18.6695 34.904 18.1369 35.2592 17.6004C35.5561 17.0199 36.0798 16.6269 36.465 16.1302C36.8683 15.6475 37.4181 15.3263 37.8535 14.9254C38.2809 14.5065 38.8407 14.2971 39.2861 14.0018C39.7516 13.7365 40.1569 13.4433 40.5903 13.3037L41.6718 12.8608L42.6228 12.4679L41.6497 8.60205L40.4518 8.88929C40.0686 8.98504 39.6011 9.09675 39.0694 9.2304C38.5257 9.33013 37.9458 9.60341 37.2997 9.85276C36.6637 10.138 35.9233 10.3275 35.2371 10.7843C34.5469 11.2192 33.7503 11.5822 33.0481 12.1647C32.3679 12.7651 31.5473 13.2857 30.9413 14.0477C30.2792 14.7618 29.6251 15.5119 29.1175 16.3656C28.5296 17.1795 28.1303 18.0731 27.7089 18.9568C27.3277 19.8405 27.0207 20.7441 26.7699 21.6218C26.2944 23.3811 26.0817 25.0527 25.9995 26.483C25.9312 27.9152 25.9714 29.1061 26.0556 29.9678C26.0857 30.3747 26.1419 30.7697 26.182 31.043L26.2322 31.3781L26.2844 31.3661C26.6412 33.0234 27.4628 34.5464 28.6539 35.7589C29.845 36.9714 31.3571 37.8239 33.0153 38.2177C34.6734 38.6116 36.4098 38.5307 38.0236 37.9844C39.6374 37.4381 41.0626 36.4487 42.1345 35.1308C43.2063 33.8128 43.8809 32.2201 44.0803 30.5369C44.2797 28.8537 43.9957 27.1487 43.2611 25.6193C42.5266 24.0899 41.3715 22.7984 39.9295 21.8944C38.4875 20.9903 36.8175 20.5106 35.1127 20.5107Z" fill="url(#paint0_linear_1_4959)"/>
                                                    <defs>
                                                        <linearGradient id="paint0_linear_1_4959" x1="24.0182" y1="8.60205" x2="24.0182" y2="38.4634" gradientUnits="userSpaceOnUse">
                                                            <stop stop-color="#04389D"/>
                                                            <stop offset="1" stop-color="#17C1F5"/>
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                            </span>
                                            <h4 class="text-xl leading-[1.4] font-bold mb-2">
                                                Nguyễn Văn A
                                            </h4>
                                            <div class="mb-6">
                                                <svg width="116" height="21" viewBox="0 0 116 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M11.4417 3.36221L12.9083 6.29554C13.1083 6.70387 13.6417 7.09554 14.0917 7.17054L16.75 7.61221C18.45 7.89554 18.85 9.12887 17.625 10.3455L15.5583 12.4122C15.2083 12.7622 15.0167 13.4372 15.125 13.9205L15.7167 16.4789C16.1833 18.5039 15.1083 19.2872 13.3167 18.2289L10.825 16.7539C10.375 16.4872 9.63332 16.4872 9.17499 16.7539L6.68332 18.2289C4.89999 19.2872 3.81665 18.4955 4.28332 16.4789L4.87499 13.9205C4.98332 13.4372 4.79165 12.7622 4.44165 12.4122L2.37499 10.3455C1.15832 9.12887 1.54999 7.89554 3.24999 7.61221L5.90832 7.17054C6.34999 7.09554 6.88332 6.70387 7.08332 6.29554L8.54999 3.36221C9.34999 1.77054 10.65 1.77054 11.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M35.4417 3.36221L36.9083 6.29554C37.1083 6.70387 37.6417 7.09554 38.0917 7.17054L40.75 7.61221C42.45 7.89554 42.85 9.12887 41.625 10.3455L39.5583 12.4122C39.2083 12.7622 39.0167 13.4372 39.125 13.9205L39.7167 16.4789C40.1833 18.5039 39.1083 19.2872 37.3167 18.2289L34.825 16.7539C34.375 16.4872 33.6333 16.4872 33.175 16.7539L30.6833 18.2289C28.9 19.2872 27.8167 18.4955 28.2833 16.4789L28.875 13.9205C28.9833 13.4372 28.7917 12.7622 28.4417 12.4122L26.375 10.3455C25.1583 9.12887 25.55 7.89554 27.25 7.61221L29.9083 7.17054C30.35 7.09554 30.8833 6.70387 31.0833 6.29554L32.55 3.36221C33.35 1.77054 34.65 1.77054 35.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M59.4417 3.36221L60.9084 6.29554C61.1084 6.70387 61.6417 7.09554 62.0917 7.17054L64.75 7.61221C66.45 7.89554 66.85 9.12887 65.625 10.3455L63.5584 12.4122C63.2084 12.7622 63.0167 13.4372 63.125 13.9205L63.7167 16.4789C64.1834 18.5039 63.1084 19.2872 61.3167 18.2289L58.825 16.7539C58.375 16.4872 57.6334 16.4872 57.175 16.7539L54.6834 18.2289C52.9 19.2872 51.8167 18.4955 52.2834 16.4789L52.875 13.9205C52.9834 13.4372 52.7917 12.7622 52.4417 12.4122L50.375 10.3455C49.1584 9.12887 49.55 7.89554 51.25 7.61221L53.9084 7.17054C54.35 7.09554 54.8834 6.70387 55.0834 6.29554L56.55 3.36221C57.35 1.77054 58.65 1.77054 59.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M83.4417 3.36221L84.9084 6.29554C85.1084 6.70387 85.6417 7.09554 86.0917 7.17054L88.75 7.61221C90.45 7.89554 90.85 9.12887 89.625 10.3455L87.5584 12.4122C87.2084 12.7622 87.0167 13.4372 87.125 13.9205L87.7167 16.4789C88.1834 18.5039 87.1084 19.2872 85.3167 18.2289L82.825 16.7539C82.375 16.4872 81.6334 16.4872 81.175 16.7539L78.6834 18.2289C76.9 19.2872 75.8167 18.4955 76.2834 16.4789L76.875 13.9205C76.9834 13.4372 76.7917 12.7622 76.4417 12.4122L74.375 10.3455C73.1584 9.12887 73.55 7.89554 75.25 7.61221L77.9084 7.17054C78.35 7.09554 78.8834 6.70387 79.0834 6.29554L80.55 3.36221C81.35 1.77054 82.65 1.77054 83.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M107.442 3.36221L108.908 6.29554C109.108 6.70387 109.642 7.09554 110.092 7.17054L112.75 7.61221C114.45 7.89554 114.85 9.12887 113.625 10.3455L111.558 12.4122C111.208 12.7622 111.017 13.4372 111.125 13.9205L111.717 16.4789C112.183 18.5039 111.108 19.2872 109.317 18.2289L106.825 16.7539C106.375 16.4872 105.633 16.4872 105.175 16.7539L102.683 18.2289C100.9 19.2872 99.8167 18.4955 100.283 16.4789L100.875 13.9205C100.983 13.4372 100.792 12.7622 100.442 12.4122L98.375 10.3455C97.1584 9.12887 97.55 7.89554 99.25 7.61221L101.908 7.17054C102.35 7.09554 102.883 6.70387 103.083 6.29554L104.55 3.36221C105.35 1.77054 106.65 1.77054 107.442 3.36221Z" fill="#FDC514"/>
                                                </svg>
                                            </div>
                                            <p class="text-lg leading-[1.4] text-[#2B2928]">
                                                AV Plus đã cung cấp cho công ty chúng tôi một hệ thống âm thanh hội nghị chất lượng cao. Đội ngũ kỹ thuật chuyên nghiệp, tư vấn tận tình và dịch vụ hậu mãi tốt. Chúng tôi rất hài lòng với sản phẩm và dịch vụ của AV Plus.
                                            </p>
                                            <button class="bg-gradient2 px-6 h-11 rounded-full text-white font-bold hover:bg-gradient8 transition-colors duration-300 mt-5 lg:mt-8">
                                                <span>{{ __('messages.share_your_experience') }}</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Testimonial 2 -->
                                <div class="swiper-slide">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-[30px] items-center">
                                        <div class="col-span-1">
                                            <div class="relative">
                                                <img 
                                                    src="{{ asset('images/img-customer.jpg') }}" 
                                                    alt="Customer Image" 
                                                    class="w-full aspect-[1.5] h-auto object-cover rounded-[20px]"
                                                />
                                            </div>
                                        </div>
                                        <div class="col-span-1">
                                            <span class="mb-4 block">
                                                <svg width="49" height="49" viewBox="0 0 49 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M13.0419 20.5107C12.5944 20.5107 12.165 20.5785 11.7377 20.6404C11.8761 20.1776 12.0186 19.7068 12.2473 19.2839C12.476 18.6695 12.8332 18.1369 13.1883 17.6004C13.4853 17.0199 14.009 16.6269 14.3942 16.1302C14.7975 15.6475 15.3473 15.3263 15.7827 14.9254C16.21 14.5065 16.7698 14.2971 17.2153 14.0018C17.6808 13.7365 18.0861 13.4433 18.5194 13.3037L19.6009 12.8608L20.552 12.4679L19.5788 8.60205L18.381 8.88929C17.9978 8.98504 17.5303 9.09675 16.9986 9.2304C16.4548 9.33013 15.875 9.60341 15.2289 9.85276C14.5908 10.136 13.8525 10.3275 13.1663 10.7823C12.476 11.2172 11.6795 11.5802 10.9772 12.1627C10.2971 12.7631 9.47642 13.2837 8.87047 14.0477C8.20835 14.7618 7.55425 15.5119 7.04662 16.3656C6.45873 17.1795 6.05945 18.0731 5.6381 18.9568C5.25687 19.8405 4.94989 20.7441 4.69908 21.6218C4.22356 23.3811 4.01087 25.0527 3.92861 26.483C3.86039 27.9152 3.90052 29.1061 3.98479 29.9678C4.01489 30.3747 4.07107 30.7697 4.11119 31.043L4.16136 31.3781L4.21352 31.3661C4.57039 33.0234 5.39191 34.5464 6.58306 35.7589C7.7742 36.9714 9.28629 37.8239 10.9444 38.2177C12.6025 38.6116 14.3389 38.5307 15.9527 37.9844C17.5665 37.4381 18.9918 36.4487 20.0636 35.1308C21.1355 33.8128 21.8101 32.2201 22.0095 30.5369C22.2089 28.8537 21.9248 27.1487 21.1903 25.6193C20.4557 24.0899 19.3006 22.7984 17.8586 21.8944C16.4166 20.9903 14.7466 20.5106 13.0419 20.5107ZM35.1127 20.5107C34.6653 20.5107 34.2359 20.5785 33.8085 20.6404C33.947 20.1776 34.0894 19.7068 34.3182 19.2839C34.5469 18.6695 34.904 18.1369 35.2592 17.6004C35.5561 17.0199 36.0798 16.6269 36.465 16.1302C36.8683 15.6475 37.4181 15.3263 37.8535 14.9254C38.2809 14.5065 38.8407 14.2971 39.2861 14.0018C39.7516 13.7365 40.1569 13.4433 40.5903 13.3037L41.6718 12.8608L42.6228 12.4679L41.6497 8.60205L40.4518 8.88929C40.0686 8.98504 39.6011 9.09675 39.0694 9.2304C38.5257 9.33013 37.9458 9.60341 37.2997 9.85276C36.6637 10.138 35.9233 10.3275 35.2371 10.7843C34.5469 11.2192 33.7503 11.5822 33.0481 12.1647C32.3679 12.7651 31.5473 13.2857 30.9413 14.0477C30.2792 14.7618 29.6251 15.5119 29.1175 16.3656C28.5296 17.1795 28.1303 18.0731 27.7089 18.9568C27.3277 19.8405 27.0207 20.7441 26.7699 21.6218C26.2944 23.3811 26.0817 25.0527 25.9995 26.483C25.9312 27.9152 25.9714 29.1061 26.0556 29.9678C26.0857 30.3747 26.1419 30.7697 26.182 31.043L26.2322 31.3781L26.2844 31.3661C26.6412 33.0234 27.4628 34.5464 28.6539 35.7589C29.845 36.9714 31.3571 37.8239 33.0153 38.2177C34.6734 38.6116 36.4098 38.5307 38.0236 37.9844C39.6374 37.4381 41.0626 36.4487 42.1345 35.1308C43.2063 33.8128 43.8809 32.2201 44.0803 30.5369C44.2797 28.8537 43.9957 27.1487 43.2611 25.6193C42.5266 24.0899 41.3715 22.7984 39.9295 21.8944C38.4875 20.9903 36.8175 20.5106 35.1127 20.5107Z" fill="url(#paint0_linear_1_4960)"/>
                                                    <defs>
                                                        <linearGradient id="paint0_linear_1_4960" x1="24.0182" y1="8.60205" x2="24.0182" y2="38.4634" gradientUnits="userSpaceOnUse">
                                                            <stop stop-color="#04389D"/>
                                                            <stop offset="1" stop-color="#17C1F5"/>
                                                        </linearGradient>
                                                    </defs>
                                                </svg>
                                            </span>
                                            <h4 class="text-xl leading-[1.4] font-bold mb-2">
                                                Trần Thị B
                                            </h4>
                                            <div class="mb-6">
                                                <svg width="116" height="21" viewBox="0 0 116 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M11.4417 3.36221L12.9083 6.29554C13.1083 6.70387 13.6417 7.09554 14.0917 7.17054L16.75 7.61221C18.45 7.89554 18.85 9.12887 17.625 10.3455L15.5583 12.4122C15.2083 12.7622 15.0167 13.4372 15.125 13.9205L15.7167 16.4789C16.1833 18.5039 15.1083 19.2872 13.3167 18.2289L10.825 16.7539C10.375 16.4872 9.63332 16.4872 9.17499 16.7539L6.68332 18.2289C4.89999 19.2872 3.81665 18.4955 4.28332 16.4789L4.87499 13.9205C4.98332 13.4372 4.79165 12.7622 4.44165 12.4122L2.37499 10.3455C1.15832 9.12887 1.54999 7.89554 3.24999 7.61221L5.90832 7.17054C6.34999 7.09554 6.88332 6.70387 7.08332 6.29554L8.54999 3.36221C9.34999 1.77054 10.65 1.77054 11.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M35.4417 3.36221L36.9083 6.29554C37.1083 6.70387 37.6417 7.09554 38.0917 7.17054L40.75 7.61221C42.45 7.89554 42.85 9.12887 41.625 10.3455L39.5583 12.4122C39.2083 12.7622 39.0167 13.4372 39.125 13.9205L39.7167 16.4789C40.1833 18.5039 39.1083 19.2872 37.3167 18.2289L34.825 16.7539C34.375 16.4872 33.6333 16.4872 33.175 16.7539L30.6833 18.2289C28.9 19.2872 27.8167 18.4955 28.2833 16.4789L28.875 13.9205C28.9833 13.4372 28.7917 12.7622 28.4417 12.4122L26.375 10.3455C25.1583 9.12887 25.55 7.89554 27.25 7.61221L29.9083 7.17054C30.35 7.09554 30.8833 6.70387 31.0833 6.29554L32.55 3.36221C33.35 1.77054 34.65 1.77054 35.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M59.4417 3.36221L60.9084 6.29554C61.1084 6.70387 61.6417 7.09554 62.0917 7.17054L64.75 7.61221C66.45 7.89554 66.85 9.12887 65.625 10.3455L63.5584 12.4122C63.2084 12.7622 63.0167 13.4372 63.125 13.9205L63.7167 16.4789C64.1834 18.5039 63.1084 19.2872 61.3167 18.2289L58.825 16.7539C58.375 16.4872 57.6334 16.4872 57.175 16.7539L54.6834 18.2289C52.9 19.2872 51.8167 18.4955 52.2834 16.4789L52.875 13.9205C52.9834 13.4372 52.7917 12.7622 52.4417 12.4122L50.375 10.3455C49.1584 9.12887 49.55 7.89554 51.25 7.61221L53.9084 7.17054C54.35 7.09554 54.8834 6.70387 55.0834 6.29554L56.55 3.36221C57.35 1.77054 58.65 1.77054 59.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M83.4417 3.36221L84.9084 6.29554C85.1084 6.70387 85.6417 7.09554 86.0917 7.17054L88.75 7.61221C90.45 7.89554 90.85 9.12887 89.625 10.3455L87.5584 12.4122C87.2084 12.7622 87.0167 13.4372 87.125 13.9205L87.7167 16.4789C88.1834 18.5039 87.1084 19.2872 85.3167 18.2289L82.825 16.7539C82.375 16.4872 81.6334 16.4872 81.175 16.7539L78.6834 18.2289C76.9 19.2872 75.8167 18.4955 76.2834 16.4789L76.875 13.9205C76.9834 13.4372 76.7917 12.7622 76.4417 12.4122L74.375 10.3455C73.1584 9.12887 73.55 7.89554 75.25 7.61221L77.9084 7.17054C78.35 7.09554 78.8834 6.70387 79.0834 6.29554L80.55 3.36221C81.35 1.77054 82.65 1.77054 83.4417 3.36221Z" fill="#FDC514"/>
                                                    <path d="M107.442 3.36221L108.908 6.29554C109.108 6.70387 109.642 7.09554 110.092 7.17054L112.75 7.61221C114.45 7.89554 114.85 9.12887 113.625 10.3455L111.558 12.4122C111.208 12.7622 111.017 13.4372 111.125 13.9205L111.717 16.4789C112.183 18.5039 111.108 19.2872 109.317 18.2289L106.825 16.7539C106.375 16.4872 105.633 16.4872 105.175 16.7539L102.683 18.2289C100.9 19.2872 99.8167 18.4955 100.283 16.4789L100.875 13.9205C100.983 13.4372 100.792 12.7622 100.442 12.4122L98.375 10.3455C97.1584 9.12887 97.55 7.89554 99.25 7.61221L101.908 7.17054C102.35 7.09554 102.883 6.70387 103.083 6.29554L104.55 3.36221C105.35 1.77054 106.65 1.77054 107.442 3.36221Z" fill="#FDC514"/>
                                                </svg>
                                            </div>
                                            <p class="text-lg leading-[1.4] text-[#2B2928]">
                                                Dịch vụ lắp đặt hệ thống âm thanh tại trường học của chúng tôi rất chuyên nghiệp. Âm thanh rõ ràng, chất lượng tốt. Đội ngũ hỗ trợ nhiệt tình và có kiến thức chuyên môn cao.
                                            </p>
                                            <button class="bg-gradient2 px-6 h-11 rounded-full text-white font-bold hover:bg-gradient8 transition-colors duration-300 mt-5 lg:mt-8">
                                                <span>{{ __('messages.share_your_experience') }}</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-button-next"></div>
                        <div class="swiper-button-prev"></div>
                    </div>
                </div>
            </div>

            <!-- Final Call to Action Section -->
            <div class="pt-12 lg:pt-0">
                <div class="">
                    <div class="flex flex-col lg:flex-row gap-[30px] items-center">
                        <div class="flex-col w-full lg:max-w-[36.71875%] pl-4 pr-4 lg:pr-0">
                            <div class="ml-auto w-full lg:max-w-[448px]">
                                <img
                                    src="{{ asset('images/logo-small.png') }}"
                                    alt="Logo Small"
                                    class="w-full h-auto max-w-[130px] aspect-[3.02325581] mb-6"
                                />
                                <h3 class="text-xl font-bold bg-gradienttext bg-clip-text text-transparent mb-1">
                                    {{ __('messages.where_sound_meets_vision') }}
                                </h3>
                                <p class="leading-[1.4] mb-6">
                                    Lorem ipsum dolor sit amet consectetur. Tempor scelerisque rhoncus aenean ut odio elementum sagittis facilisis. Et hac maecenas interdum eu.
                                </p>
                                <button class="bg-gradient2 px-6 w-full h-11 lg:h-[52px] text-sm leading-[1.4] rounded-lg text-white font-bold hover:bg-gradient8 transition-colors duration-300">
                                    {{ __('messages.hotline') }}: 09885 99 2222
                                </button>
                            </div>
                        </div>
                        <div class="flex-col w-full">
                            <div class="relative">
                                <img
                                    src="{{ asset('images/img-sound.jpg') }}"
                                    alt="Sound Image"
                                    class="w-full h-auto aspect-[1.94581281] object-cover"
                                />
                                <div class="absolute bg-gradientwhite w-full max-w-[210px] h-full left-0 top-0"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Banner -->
            <div>
                <a href="#">
                    <img
                        src="{{ asset('images/banner-contact.jpg') }}"
                        alt="Banner Contact"
                        class="w-full h-auto object-cover"
                    />
                </a>
            </div>
        </div>
    </main>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Counter Animation Function
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16); // 60fps
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            // Format number with thousands separator
            const formattedNumber = Math.floor(current).toLocaleString('vi-VN');
            element.textContent = formattedNumber;
        }, 16);
    }

    // Intersection Observer for scroll trigger
    const observerOptions = {
        threshold: 0.5, // Trigger when 50% of element is visible
        rootMargin: '0px 0px -100px 0px' // Start animation 100px before element comes into view
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));

                // Only animate if not already animated
                if (!counter.classList.contains('animated')) {
                    counter.classList.add('animated');
                    animateCounter(counter, target, 2500); // 2.5 seconds duration
                }
            }
        });
    }, observerOptions);

    // Observe all counter elements
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        observer.observe(counter);
    });
});

// Services Swiper with Staggered Effect
document.addEventListener("DOMContentLoaded", function () {
    if (typeof Swiper === "undefined") {
        console.error("Swiper is not loaded!");
        return;
    }

    // Initialize Services Swiper
    const servicesSwiper = new Swiper(".services-swiper .swiper", {
        slidesPerView: 1,
        spaceBetween: 0,
        loop: false,
        autoplay: false,
        navigation: {
            nextEl: ".services-swiper .swiper-button-next",
            prevEl: ".services-swiper .swiper-button-prev",
        },
        breakpoints: {
            0: {
                slidesPerView: 2,
                spaceBetween: 0,
            },
            550: {
                slidesPerView: 2,
                spaceBetween: 0,
            },
            768: {
                slidesPerView: 2.5,
                spaceBetween: 0,
            },
            1024: {
                slidesPerView: 3,
                spaceBetween: 0,
            },
            1280: {
                slidesPerView: 4,
                spaceBetween: 0,
            },
        },
    });

    // Function to apply staggered effect
    function applyStaggeredEffect() {
        const allSlides = document.querySelectorAll(
            ".services-swiper .swiper-slide:not(.swiper-slide-duplicate)"
        );

        if (allSlides.length === 0) {
            console.log("No slides found!");
            return;
        }

        // Reset all slides
        const allSlidesIncludingDuplicates = document.querySelectorAll(
            ".services-swiper .swiper-slide"
        );

        allSlidesIncludingDuplicates.forEach((slide) => {
            slide.classList.remove("staggered-even", "staggered-odd");
            if (!slide.classList.contains("elevated")) {
                slide.style.transform = "";
            }
        });

        const activeIndex = servicesSwiper.realIndex || 0;

        // Get current slidesPerView for current breakpoint
        let currentSlidesPerView = 1;
        if (window.innerWidth >= 1280) {
            currentSlidesPerView = 4;
        } else if (window.innerWidth >= 1024) {
            currentSlidesPerView = 3;
        } else if (window.innerWidth >= 768) {
            currentSlidesPerView = 2.5;
        } else if (window.innerWidth >= 550) {
            currentSlidesPerView = 2;
        } else {
            currentSlidesPerView = 2;
        }

        // Apply staggered effect to visible slides
        for (let i = 0; i < currentSlidesPerView && i < allSlides.length; i++) {
            const slideIndex = (activeIndex + i) % allSlides.length;
            const slide = allSlides[slideIndex];

            if (slide && !slide.classList.contains("elevated")) {
                if (i % 2 === 1) {
                    slide.classList.add("staggered-even");
                    const offset = window.innerWidth < 768 ? "45px" : "90px";
                    slide.style.transform = `translateY(${offset})`;
                    slide.style.transition = "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
                } else {
                    slide.classList.add("staggered-odd");
                    slide.style.transform = "translateY(0px)";
                    slide.style.transition = "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
                }
            }
        }
    }

    // Apply initial staggered effect
    setTimeout(() => {
        applyStaggeredEffect();
    }, 500);

    // Track current elevated slide
    let currentElevated = null;

    // Handle slide change
    servicesSwiper.on("slideChange", function () {
        // Reset elevated states
        const serviceSlides = document.querySelectorAll(
            ".services-swiper .swiper-slide:not(.swiper-slide-duplicate)"
        );
        serviceSlides.forEach((slide) => {
            slide.classList.remove("elevated", "slide-up-animation", "slide-down-animation");
        });
        currentElevated = null;

        // Reapply staggered effect
        setTimeout(() => {
            applyStaggeredEffect();
        }, 150);
    });

    // Handle transition end
    servicesSwiper.on("transitionEnd", function () {
        setTimeout(() => {
            applyStaggeredEffect();
        }, 100);
    });

    // Add click animation functionality
    document.querySelector(".services-swiper").addEventListener("click", function (e) {
        const clickedSlide = e.target.closest(".swiper-slide");
        if (!clickedSlide) return;

        // Remove elevated class from previously clicked slide
        if (currentElevated && currentElevated !== clickedSlide) {
            currentElevated.classList.remove("elevated", "slide-up-animation");
        }

        // Toggle elevation for clicked slide
        if (clickedSlide.classList.contains("elevated")) {
            clickedSlide.classList.remove("elevated", "slide-up-animation");
            clickedSlide.classList.add("slide-down-animation");
            currentElevated = null;
        } else {
            clickedSlide.classList.add("elevated", "slide-up-animation");
            clickedSlide.classList.remove("slide-down-animation");
            currentElevated = clickedSlide;
        }
    });

    // Handle window resize
    window.addEventListener("resize", function () {
        setTimeout(() => {
            applyStaggeredEffect();
        }, 300);
    });
});
</script>
@endpush