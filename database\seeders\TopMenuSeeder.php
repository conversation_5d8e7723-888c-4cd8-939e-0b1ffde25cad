    /**
     * Seeds the top menu items.
     */
    private function seedTopMenuItems()
    {
        $menu = $this->createTopMenu();
        if (!$menu) {
            return;
        }

        // Tìm trang Giới thiệu
        $aboutPage = StaticPage::where('slug', 'gioi-thieu')->first();

        // 1. Giới thiệu
        MenuItem::firstOrCreate(
            [
                'menu_id' => $menu->id,
                'title' => 'Giới thiệu',
            ],
            [
                'url' => $aboutPage ? '/gioi-thieu' : '#',
                'order' => 1,
                'target' => '_self',
                'type' => 'page',
                'model_type' => $aboutPage ? get_class($aboutPage) : null,
                'model_id' => $aboutPage ? $aboutPage->id : null,
                'status' => true,
                'icon' => 'fa fa-info-circle',
            ]
        );

        // 2. Tin tức
        // Tìm danh mục tin tức
        $newsCategory = Category::where('type', 'post')->where('slug', 'tin-tuc')->first();

        MenuItem::firstOrCreate(
            [
                'menu_id' => $menu->id,
                'title' => 'Tin tức',
            ],
            [
                'url' => $newsCategory ? '/news/category/' . $newsCategory->slug : '/news.html',
                'order' => 2,
                'target' => '_self',
                'type' => 'category',
                'model_type' => $newsCategory ? get_class($newsCategory) : null,
                'model_id' => $newsCategory ? $newsCategory->id : null,
                'status' => true,
                'icon' => 'fa fa-newspaper-o',
            ]
        );

        // 3. Liên hệ
        MenuItem::firstOrCreate(
            [
                'menu_id' => $menu->id,
                'title' => 'Liên hệ',
            ],
            [
                'url' => '/contact.html',
                'order' => 3,
                'target' => '_self',
                'type' => 'custom',
                'status' => true,
                'icon' => 'fa fa-envelope-o',
            ]
        );

        // 4. Đăng nhập
        MenuItem::firstOrCreate(
            [
                'menu_id' => $menu->id,
                'title' => 'Đăng nhập',
            ],
            [
                'url' => '/login',
                'order' => 4,
                'target' => '_self',
                'type' => 'custom',
                'status' => true,
                'icon' => 'fa fa-sign-in',
            ]
        );

        // 5. Đăng ký
        MenuItem::firstOrCreate(
            [
                'menu_id' => $menu->id,
                'title' => 'Đăng ký',
            ],
            [
                'url' => '/register',
                'order' => 5,
                'target' => '_self',
                'type' => 'custom',
                'status' => true,
                'icon' => 'fa fa-user-plus',
            ]
        );
    } 