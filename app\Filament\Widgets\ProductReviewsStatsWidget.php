<?php

namespace App\Filament\Widgets;

use App\Models\ProductReview;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class ProductReviewsStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Tổng số bình luận
        $totalReviews = ProductReview::count();
        
        // Bình luận chờ phản hồi
        $pendingReviews = ProductReview::whereNull('admin_reply')->count();
        
        // Bình luận đã phản hồi
        $repliedReviews = ProductReview::whereNotNull('admin_reply')->count();
        
        // Đánh giá trung bình
        $averageRating = ProductReview::avg('rating');
        
        // Bình luận trong 7 ngày qua
        $recentReviews = ProductReview::where('created_at', '>=', now()->subDays(7))->count();
        
        // Bình luận đánh giá thấp (1-2 sao)
        $lowRatingReviews = ProductReview::whereIn('rating', [1, 2])->count();

        return [
            Stat::make('Tổng bình luận', $totalReviews)
                ->description('Tất cả bình luận sản phẩm')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('primary'),

            Stat::make('Chờ phản hồi', $pendingReviews)
                ->description('Bình luận chưa có phản hồi')
                ->descriptionIcon('heroicon-m-clock')
                ->color($pendingReviews > 0 ? 'warning' : 'success')
                ->url(route('filament.admin.resources.product-reviews.index', ['activeTab' => 'pending'])),

            Stat::make('Đã phản hồi', $repliedReviews)
                ->description('Bình luận đã được phản hồi')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Đánh giá TB', number_format($averageRating, 1))
                ->description('Điểm đánh giá trung bình')
                ->descriptionIcon('heroicon-m-star')
                ->color($averageRating >= 4 ? 'success' : ($averageRating >= 3 ? 'warning' : 'danger')),

            Stat::make('7 ngày qua', $recentReviews)
                ->description('Bình luận mới trong tuần')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info'),

            Stat::make('Đánh giá thấp', $lowRatingReviews)
                ->description('1-2 sao cần chú ý')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($lowRatingReviews > 0 ? 'danger' : 'success')
                ->url(route('filament.admin.resources.product-reviews.index', ['activeTab' => 'low_rating'])),
        ];
    }
}
