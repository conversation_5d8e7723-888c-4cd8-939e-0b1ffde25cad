<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductPromotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'start_date',
        'end_date',
        'promotion_items',
        'locations',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'promotion_items' => 'array',
        'locations' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Quan hệ với sản phẩm
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_promotion_pivot');
    }

    /**
     * Kiểm tra khuyến mãi có đang hoạt động không
     */
    public function isActive()
    {
        $today = now()->startOfDay();
        return $this->is_active && 
               $today->gte($this->start_date) && 
               $today->lte($this->end_date);
    }
}
