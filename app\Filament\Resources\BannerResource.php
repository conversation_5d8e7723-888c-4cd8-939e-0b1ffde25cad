<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BannerResource\Pages;
use App\Models\Banner;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;

class BannerResource extends BaseResource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Quản lý Banner';

    protected static ?string $navigationGroup = 'Hệ thống';

    protected static ?int $navigationSort = 80;
    
    protected static ?string $modelLabel = 'Banner';
    protected static ?string $pluralModelLabel = 'Banners';
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('Tabs')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('Thông tin cơ bản')
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tên Banner')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) => 
                                        $operation === 'create' ? $set('slug', Str::slug($state)) : null),

                                Forms\Components\TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->unique(Banner::class, 'slug', ignoreRecord: true)
                                    ->maxLength(255),

                                Forms\Components\Select::make('type')
                                    ->label('Loại')
                                    ->options([
                                        'banner' => 'Banner',
                                        'slider' => 'Slider',
                                        'partner' => 'Đối tác',
                                    ])
                                    ->default('banner')
                                    ->required(),

                                Forms\Components\TextInput::make('position')
                                    ->label('Vị trí')
                                    ->maxLength(255)
                                    ->helperText('Vị trí hiển thị (ví dụ: header, sidebar, footer)'),

                                Forms\Components\TextInput::make('order')
                                    ->label('Thứ tự')
                                    ->numeric()
                                    ->default(0),

                                Forms\Components\TextInput::make('url')
                                    ->label('Đường dẫn')
                                    ->url()
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('description')
                                    ->label('Mô tả')
                                    ->maxLength(1000)
                                    ->columnSpanFull(),
                            ]),

                        Forms\Components\Tabs\Tab::make('Hình ảnh')
                            ->schema([
                                Forms\Components\FileUpload::make('image')
                                    ->label('Hình ảnh')
                                    ->image()
                                    ->imageEditor()
                                    ->directory('uploads/banners')
                                    ->columnSpanFull(),
                            ]),

                        Forms\Components\Tabs\Tab::make('Trạng thái & thời gian')
                            ->schema([
                                Forms\Components\Toggle::make('status')
                                    ->label('Kích hoạt')
                                    ->default(true),

                                Forms\Components\DateTimePicker::make('start_date')
                                    ->label('Ngày bắt đầu')
                                    ->placeholder('Không giới hạn'),

                                Forms\Components\DateTimePicker::make('end_date')
                                    ->label('Ngày kết thúc')
                                    ->placeholder('Không giới hạn')
                                    ->after('start_date'),
                            ]),
                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->disk('public')
                    ->square()
                    ->size(60)
                    ->visibility('public')
                    ->getStateUsing(function ($record) {
                        if ($record->image && Storage::disk('public')->exists($record->image)) {
                            return $record->image;
                        }
                        return null;
                    })
                    ->defaultImageUrl(asset(Config::get('filament-media.defaults.placeholder_image.url'))),
                    
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),

                Tables\Columns\TextColumn::make('type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'banner' => 'Banner',
                        'slider' => 'Slider',
                        'partner' => 'Đối tác',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'banner' => 'success',
                        'slider' => 'warning',
                        'partner' => 'info',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('position')
                    ->label('Vị trí')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('order')
                    ->label('Thứ tự')
                    ->sortable(),

                Tables\Columns\IconColumn::make('status')
                    ->label('Kích hoạt')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Loại')
                    ->options([
                        'banner' => 'Banner',
                        'slider' => 'Slider',
                        'partner' => 'Đối tác',
                    ]),

                Tables\Filters\SelectFilter::make('position')
                    ->label('Vị trí')
                    ->options(fn () => Banner::distinct()->pluck('position', 'position')->filter()->toArray()),

                Tables\Filters\TernaryFilter::make('status')
                    ->label('Kích hoạt'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->iconButton()
                    ->modalWidth('xl')
                    ->slideOver(),
                
                Tables\Actions\EditAction::make()
                    ->iconButton()
                    ->modalWidth('xl')
                    ->slideOver(),
                
                Tables\Actions\DeleteAction::make()
                    ->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    
                    Tables\Actions\BulkAction::make('toggle_active')
                        ->label('Bật/tắt trạng thái')
                        ->icon('heroicon-o-check-circle')
                        ->action(function (Tables\Actions\BulkAction $action, \Illuminate\Database\Eloquent\Collection $records): void {
                            $records->each(function (Banner $record): void {
                                $record->update(['status' => !$record->status]);
                            });
                            
                            $action->success();
                        }),
                ]),
            ])
            ->emptyStateHeading('Chưa có Banner')
            ->emptyStateDescription('Bạn có thể tạo mới banner, slider và logo đối tác tại đây.')
            ->emptyStateIcon('heroicon-o-photo');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
        ];
    }
} 