<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RefreshDatabaseCommand extends Command
{
    protected $signature = 'db:refresh';
    protected $description = 'Refresh database and run seeders';

    public function handle()
    {
        $this->warn('⚠ Resetting database...');
        $this->call('migrate:refresh');

        $this->info('✅ Running seeders...');
        $this->call('db:seed');

        $this->info('🎉 Database refreshed and seeded successfully!');
    }
}
