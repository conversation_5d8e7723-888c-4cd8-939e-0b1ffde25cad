<!DOCTYPE html>
<html lang="vi">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Home Page - AVPlus</title>
        <!-- Swiper CSS -->
        <link rel="stylesheet" href="../dist/css/swiper-bundle.min.css" />
        <link rel="stylesheet" href="../dist/css/style.css" />
        <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon" />
    </head>
    <body class="bg-white">
        <header data-property-1="Default" class="bg-primary-main">
            <div class="header-wrapper container mx-auto">
                <div
                    class="self-stretch flex flex-col justify-start items-start"
                >
                    <div
                        class="self-stretch inline-flex justify-between items-center pt-4 pb-4 md:pb-0 gap-4"
                    >
                        <div class="logo">
                            <a href="index.html"
                                ><img
                                    class="w-41 h-14 rounded"
                                    src="assets/images/logo.png"
                                    alt="AVPlus Logo"
                            /></a>
                        </div>
                        <div
                            class="hidden md:flex justify-start items-center w-full max-w-[880px]"
                        >
                            <form
                                class="flex items-center w-full focus:outline-none focus:ring-0 focus:border-none"
                                action="search.html"
                                method="get"
                            >
                                <label for="search" class="sr-only"
                                    >Tìm kiếm</label
                                >
                                <input
                                    type="text"
                                    id="search"
                                    name="q"
                                    class="block w-full md:max-w-[836px] h-11 px-3 py-2 focus:right-0 rounded-l-md border-gray-300 md:text-lg"
                                    placeholder="Tìm kiếm sản phẩm..."
                                    autocomplete="off"
                                />
                                <button
                                    type="submit"
                                    class="inline-flex items-center px-3 py-3 h-11 bg-brand-gradient text-white rounded-r-md focus:outline-none focus:ring-0 focus:border-none"
                                >
                                    <svg
                                        class="w-5 h-5"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
                                        />
                                    </svg>
                                </button>
                            </form>
                        </div>
                        <div class="flex justify-start items-center gap-6">
                            <a
                                href="pages/checkout.html"
                                class="flex justify-start items-center gap-2"
                            >
                                <div class="w-6 h-6 relative">
                                    <svg
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z"
                                            fill="white"
                                        />
                                    </svg>
                                </div>
                                <span
                                    class="hidden lg:flex whitespace-nowrap justify-start text-white md:text-lg font-medium font-neue leading-relaxed"
                                >
                                    Giỏ hàng
                                </span>
                            </a>
                            <div class="flex justify-start items-center gap-3">
                                <a href="news-detail.html" class="w-[34px] h-6">
                                    <img
                                        class=""
                                        src="assets/images/flag-vi.png"
                                        alt="Flag VN Logo"
                                    />
                                </a>
                                <a href="news-detail.html" class="w-[34px] h-6">
                                    <img
                                        class=""
                                        src="assets/images/flag-en.png"
                                        alt="Flag EN Logo"
                                    />
                                </a>
                            </div>
                            <!-- Mobile Menu Button -->
                            <button
                                class="menu-mobile-toggle md:hidden flex items-center justify-center bg-white w-8 h-8 text-secondary-main rounded-md"
                            >
                                <svg
                                    class="w-6 h-6"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M4 6h16M4 12h16M4 18h16"
                                    ></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div
                        id="site-navigation"
                        class="navigation self-stretch hidden md:inline-flex py-3 justify-center items-center"
                    >
                        <div
                            class="flex justify-start items-center gap-10 lg:text-lg"
                        >
                            <a
                                href="pages/about.html"
                                class="nav-link text-white"
                                >Về chúng tôi</a
                            >
                            <a
                                href="pages/categories.html"
                                class="nav-link text-white"
                                >Sản phẩm</a
                            >
                            <a
                                href="pages/solutions.html"
                                class="nav-link text-white"
                                >Giải pháp</a
                            >
                            <a
                                href="pages/brand-detail.html"
                                class="nav-link text-white"
                                >Thương hiệu</a
                            >
                            <a
                                href="pages/projects.html"
                                class="nav-link text-white"
                                >Dự án</a
                            >
                            <a
                                href="pages/news.html"
                                class="nav-link text-white"
                                >Tin tức</a
                            >
                            <a
                                href="pages/contact.html"
                                class="nav-link text-white"
                                >Liên hệ</a
                            >
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Menu Overlay -->
        <div
            class="mobile-menu-overlay fixed inset-0 bg-black/50 z-40 hidden"
        ></div>

        <!-- Mobile Menu Sidebar -->
        <div
            class="mobile-menu-sidebar fixed top-0 left-0 h-full w-80 bg-primary-main z-50 transform -translate-x-full transition-transform duration-300 ease-in-out hidden"
        >
            <div class="flex flex-col h-full">
                <!-- Header -->
                <div
                    class="flex items-center justify-between p-4 border-b border-white/20"
                >
                    <div class="logo">
                        <img
                            class="w-32 h-10 rounded"
                            src="assets/images/logo.png"
                            alt="AVPlus Logo"
                        />
                    </div>
                    <button class="close-mobile-menu text-white p-2">
                        <svg
                            class="w-6 h-6"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"
                            ></path>
                        </svg>
                    </button>
                </div>

                <!-- Search Bar -->
                <div class="p-4 border-b border-white/20">
                    <form
                        class="flex items-center"
                        action="search.html"
                        method="get"
                    >
                        <input
                            type="text"
                            name="q"
                            class="block w-full h-11 px-3 py-2 rounded-l-md border-gray-300 text-gray-900"
                            placeholder="Tìm kiếm sản phẩm..."
                            autocomplete="off"
                        />
                        <button
                            type="submit"
                            class="inline-flex items-center px-3 py-3 h-11 bg-brand-gradient text-white rounded-r-md"
                        >
                            <svg
                                class="w-5 h-5"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
                                />
                            </svg>
                        </button>
                    </form>
                </div>

                <!-- Menu Items -->
                <div class="flex-1 p-4">
                    <nav class="space-y-4">
                        <a
                            href="pages/about.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Về chúng tôi
                        </a>
                        <a
                            href="pages/categories.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Sản phẩm
                        </a>
                        <a
                            href="pages/solutions.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Giải pháp
                        </a>
                        <a
                            href="pages/brand-detail.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Thương hiệu
                        </a>
                        <a
                            href="pages/projects.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Dự án
                        </a>
                        <a
                            href="pages/news.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Tin tức
                        </a>
                        <a
                            href="pages/contact.html"
                            class="mobile-nav-link block py-3 px-4 text-white text-lg font-medium hover:bg-white/10 rounded-lg transition-colors"
                        >
                            Liên hệ
                        </a>
                    </nav>
                </div>

                <!-- Footer -->
                <div class="p-4 border-t border-white/20">
                    <div class="flex items-center justify-center gap-4">
                        <a href="#" class="flex items-center gap-2 text-white">
                            <div class="w-6 h-6">
                                <svg
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z"
                                        fill="white"
                                    />
                                    <path
                                        d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z"
                                        fill="white"
                                    />
                                    <path
                                        d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z"
                                        fill="white"
                                    />
                                    <path
                                        d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z"
                                        fill="white"
                                    />
                                </svg>
                            </div>
                            <span class="text-sm">Giỏ hàng</span>
                        </a>
                        <div class="flex gap-2">
                            <a href="#" class="w-8 h-5">
                                <img
                                    src="assets/images/flag-vi.png"
                                    alt="VN"
                                    class="w-full h-full object-cover rounded"
                                />
                            </a>
                            <a href="#" class="w-8 h-5">
                                <img
                                    src="assets/images/flag-en.png"
                                    alt="EN"
                                    class="w-full h-full object-cover rounded"
                                />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main id="main" id="site-main">
            <div class="page-wrapper page-about">
                <div
                    class="categories-swiper swiper-slider"
                    data-items="1"
                    data-mobile="1"
                    data-tablet="1"
                    data-desktop="1"
                    data-large="1"
                    data-xlarge="1"
                    data-spacing="0"
                    data-loop="true"
                    data-navigation="true"
                    data-autoplay="true"
                >
                    <div class="swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <img
                                    src="assets/images/img-slide-1.jpg"
                                    alt="demo-1"
                                    class="w-full h-full object-cover hover:scale-110 duration-500"
                                />
                            </div>
                            <div class="swiper-slide">
                                <img
                                    src="assets/images/img-slide-1.jpg"
                                    alt="demo-1"
                                    class="w-full h-full object-cover hover:scale-110 duration-500"
                                />
                            </div>
                        </div>
                        <!-- Pagination -->
                        <div class="swiper-pagination"></div>
                        <!-- Navigation buttons -->
                        <!-- <div
                            class="swiper-button-next bg-primary-background3 right-0"
                        ></div>
                        <div
                            class="swiper-button-prev bg-primary-background3 left-0"
                        ></div> -->
                    </div>
                </div>
                <div class="relative z-10">
                    <div
                        class="bg-gradient8 pt-16 lg:pt-[86px] pb-16 lg:pb-[86px] text-white"
                    >
                        <div class="container mx-auto">
                            <div
                                class="grid grid-cols-1 lg:grid-cols-2 items-center gap-4 lg:gap-[30px]"
                            >
                                <div class="col-span-1">
                                    <div class="rounded-3xl overflow-hidden">
                                        <img
                                            src="assets/images/image-about.png"
                                            alt="image-about"
                                            class="w-full h-auto"
                                        />
                                    </div>
                                </div>
                                <div class="col-span-1 lg:-order-1">
                                    <h1
                                        class="font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6"
                                    >
                                        Về chúng tôi
                                    </h1>
                                    <div
                                        class="lg:text-xl leading-[1.4] space-y-5"
                                    >
                                        <p>
                                            Lorem ipsum dolor sit amet
                                            consectetur. Quam amet mauris
                                            consectetur vulputate eget nulla
                                            phasellus euismod diam. Magna
                                            nascetur libero ipsum at ultricies
                                            velit arcu. Dictumst neque
                                            ullamcorper sem viverra in sit
                                            potenti nunc. Mi pretium odio tellus
                                            elit pulvinar. Vel pellentesque dui
                                            eget nisl est at scelerisque. Ut
                                            magna molestie odio in volutpat.
                                        </p>
                                        <p>
                                            Turpis pellentesque vel enim enim
                                            adipiscing nunc vitae adipiscing
                                            eget. Feugiat pellentesque etiam
                                            lectus orci elit. Leo adipiscing
                                            consectetur trLorem ipsum dolor sit
                                            amet consectetur. Quam amet mauris
                                            consectetur vulputate eget nulla
                                            phasellus euismod diam. Magna
                                            nascetur libero ipsum at ultricies
                                            velit arcu.
                                        </p>
                                    </div>
                                    <div class="mt-[30px]">
                                        <button
                                            class="text-secondary-main bg-white rounded-full px-[30px] h-11 font-bold leading-[1.4]"
                                        >
                                            <span>Khám phá sản phẩm</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <img src="assets/images/wave.svg" alt="wave" />
                    </div>
                </div>

                <div
                    class="bg-img1 bg-no-repeat bg-center bg-cover mt-12 lg:mt-[70px] pb-12 lg:pb-[70px]"
                >
                    <h2
                        class="container mx-auto text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10"
                    >
                        Dịch vụ cung cấp
                    </h2>
                    <div class="services-swiper services-slider relative">
                        <div class="container mx-auto">
                            <div
                                class="overflow-x-hidden -mb-[30px] sm:pb-[90px]"
                            >
                                <div
                                    class="swiper -ml-2 lg:-ml-[15px] -mr-2 lg:-mr-[15px] overflow-visible h-auto"
                                >
                                    <div
                                        class="swiper-wrapper items-start h-auto"
                                    >
                                        <!-- Service Card 1 - Blue gradient -->
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-1.jpg"
                                                        alt="sv-1"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-2.jpg"
                                                        alt="sv-2"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-3.jpg"
                                                        alt="sv-3"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-4.jpg"
                                                        alt="sv-4"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-1.jpg"
                                                        alt="sv-1"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-2.jpg"
                                                        alt="sv-2"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-3.jpg"
                                                        alt="sv-3"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="swiper-slide px-2 lg:px-[15px]"
                                        >
                                            <div
                                                class="service-card relative rounded-xl overflow-hidden shadow-box aspect-[0.637065637] group w-full"
                                            >
                                                <div
                                                    class="relative z-10 group-hover:z-0 h-full w-full"
                                                >
                                                    <img
                                                        src="assets/images/sv-4.jpg"
                                                        alt="sv-4"
                                                        class="w-full h-full object-cover"
                                                    />
                                                    <div
                                                        class="absolute bottom-0 w-full h-auto left-0 px-4 lg:px-8 pb-[50px] pt-14 sm:pt-20 md:pt-32 lg:pt-40 xl:pt-[200px] bg-gradient7"
                                                    >
                                                        <p
                                                            class="text-primary-white2 pb-4 leading-[1.4] block text-base md:text-xl lg:text-2xl font-bold relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-[50px] after:h-[2px] after:bg-primary-white2"
                                                        >
                                                            Lorem ipsum dolor
                                                            sit amet
                                                        </p>
                                                    </div>
                                                </div>
                                                <div
                                                    class="absolute opacity-0 group-hover:z-10 group-hover:opacity-100 transition-all w-full h-full top-0 left-0 inset-0 bg-gradient6 pt-4 md:pt-8 lg:pt-12 xl:pt-18 px-4 lg:px-8 text-white overflow-auto pb-4"
                                                >
                                                    <h3
                                                        class="text-xl md:text-2xl font-bold mb-4"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet
                                                    </h3>
                                                    <div
                                                        class="w-[50px] h-[2px] bg-white mb-4"
                                                    ></div>
                                                    <p
                                                        class="text-white/90 leading-relaxed"
                                                    >
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate. Diam sit
                                                        aliquet enim viverra
                                                        lobortis fusce mauris
                                                        at. Diam tortor. Lorem
                                                        ipsum dolor sit amet
                                                        consectetur. Venenatis
                                                        sed habitant id sed
                                                        dapibus bibendum
                                                        vulputate. Diam tortor.
                                                        Lorem ipsum dolor sit
                                                        amet consectetur.
                                                        Venenatis sed habitant
                                                        id sed dapibus bibendum
                                                        vulputate.
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            class="swiper-button-next bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none right-0"
                        ></div>
                        <div
                            class="swiper-button-prev bg-primary-base md:w-[30px] md:h-[70px] lg:w-[52px] lg:h-[104px] rounded-none left-0"
                        ></div>
                    </div>
                </div>
                <div class="bg-white pt-12 lg:pt-[70px] pb-12 lg:pb-[70px]">
                    <div class="container mx-auto">
                        <h2
                            class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-10"
                        >
                            Sản phẩm nổi bật
                        </h2>
                        <div
                            class="swiper-slider relative -ml-2 -mr-2 lg:-ml-[15px] lg:-mr-[15px]"
                            data-items="1.3"
                            data-mobile="1.7"
                            data-tablet="2.5"
                            data-desktop="3.5"
                            data-large="4"
                            data-xlarge="4"
                            data-spacing="0"
                            data-loop="true"
                            data-navigation="true"
                            data-autoplay="true"
                            data-autoplay-delay="3000"
                        >
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <!-- Product Card 1 -->
                                    <div
                                        class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]"
                                    >
                                        <div
                                            class="product-card bg-white rounded-xl shadow-product hover:shadow-product transition-shadow duration-300 overflow-hidden group"
                                        >
                                            <div class="relative">
                                                <!-- Product Image -->
                                                <div class="">
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="aspect-square overflow-hidden"
                                                    >
                                                        <img
                                                            src="assets/images/image-product-1.png"
                                                            alt="Hệ thống hội nghị truyền hình Polycom CX5500"
                                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    </a>
                                                </div>

                                                <!-- Badges -->
                                                <div
                                                    class="absolute top-4 left-4 flex flex-col gap-1"
                                                >
                                                    <span
                                                        class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        -12%
                                                    </span>
                                                    <span
                                                        class="bg-primary-badge2 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="12"
                                                            viewBox="0 0 12 12"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M6 1L7.5 4.5H11L8.25 6.75L9.75 10.25L6 8L2.25 10.25L3.75 6.75L1 4.5H4.5L6 1Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        Nổi bật
                                                    </span>
                                                    <span
                                                        class="bg-primary-badge3 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M6 1.5C3.24 1.5 1 3.74 1 6.5C1 9.26 3.24 11.5 6 11.5C8.76 11.5 11 9.26 11 6.5C11 3.74 8.76 1.5 6 1.5ZM6.165 9C6.075 9.03 5.92 9.03 5.83 9C5.05 8.735 3.3 7.62 3.3 5.73C3.3 4.895 3.97 4.22 4.8 4.22C5.29 4.22 5.725 4.455 6 4.825C6.27 4.46 6.71 4.22 7.2 4.22C8.03 4.22 8.7 4.895 8.7 5.73C8.7 7.62 6.95 8.735 6.165 9Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        Bán chạy
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Product Info -->
                                            <div class="px-4 pb-4">
                                                <div>
                                                    <span
                                                        class="text-sm text-primary-gray font-medium"
                                                        >Thiết bị âm thanh</span
                                                    >
                                                </div>
                                                <h3
                                                    class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4"
                                                >
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="text-primary-base hover:text-secondary-main transition-colors"
                                                        >Hệ thống hội nghị
                                                        truyền hình Polycom
                                                        CX5500</a
                                                    >
                                                </h3>
                                                <div
                                                    class="flex items-center justify-between gap-1"
                                                >
                                                    <div class="flex flex-col">
                                                        <div
                                                            class="flex items-center gap-2"
                                                        >
                                                            <span
                                                                class="text-primary-price font-bold text-lg"
                                                                >68.500.000đ</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="text-primary-gray font-medium line-through"
                                                            >70.050.000đ</span
                                                        >
                                                    </div>
                                                    <!-- Add to Cart Button -->
                                                    <button
                                                        class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
                                                    >
                                                        <svg
                                                            width="23"
                                                            height="22"
                                                            viewBox="0 0 23 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Product Card 2 -->
                                    <div
                                        class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]"
                                    >
                                        <div
                                            class="product-card bg-white rounded-xl shadow-product hover:shadow-product transition-shadow duration-300 overflow-hidden group"
                                        >
                                            <div class="relative">
                                                <!-- Product Image -->
                                                <div class="">
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="aspect-square overflow-hidden"
                                                    >
                                                        <img
                                                            src="assets/images/image-product-2.png"
                                                            alt="Hệ thống hội nghị truyền hình Polycom CX5500"
                                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    </a>
                                                </div>

                                                <!-- Badges -->
                                                <div
                                                    class="absolute top-4 left-4 flex flex-col gap-1"
                                                >
                                                    <span
                                                        class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        -12%
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Product Info -->
                                            <div class="px-4 pb-4">
                                                <div>
                                                    <span
                                                        class="text-sm text-primary-gray font-medium"
                                                        >Thiết bị âm thanh</span
                                                    >
                                                </div>
                                                <h3
                                                    class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4"
                                                >
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="text-primary-base hover:text-secondary-main transition-colors"
                                                        >Loa Kèm Micro Hội Nghị
                                                        YAMAHA YVC-1000</a
                                                    >
                                                </h3>
                                                <div
                                                    class="flex items-center justify-between gap-1"
                                                >
                                                    <div class="flex flex-col">
                                                        <div
                                                            class="flex items-center gap-2"
                                                        >
                                                            <span
                                                                class="text-primary-price font-bold text-lg"
                                                                >68.500.000đ</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="text-primary-gray font-medium line-through"
                                                            >70.050.000đ</span
                                                        >
                                                    </div>
                                                    <!-- Add to Cart Button -->
                                                    <button
                                                        class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
                                                    >
                                                        <svg
                                                            width="23"
                                                            height="22"
                                                            viewBox="0 0 23 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Product Card 3 -->
                                    <div
                                        class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]"
                                    >
                                        <div
                                            class="product-card bg-white rounded-xl shadow-product hover:shadow-product transition-shadow duration-300 overflow-hidden group"
                                        >
                                            <div class="relative">
                                                <!-- Product Image -->
                                                <div class="">
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="aspect-square overflow-hidden"
                                                    >
                                                        <img
                                                            src="assets/images/image-product-3.png"
                                                            alt="Hệ thống hội nghị truyền hình Polycom CX5500"
                                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    </a>
                                                </div>

                                                <!-- Badges -->
                                                <div
                                                    class="absolute top-4 left-4 flex flex-col gap-1"
                                                >
                                                    <span
                                                        class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        -12%
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Product Info -->
                                            <div class="px-4 pb-4">
                                                <div>
                                                    <span
                                                        class="text-sm text-primary-gray font-medium"
                                                        >Thiết bị trình
                                                        chiếu</span
                                                    >
                                                </div>
                                                <h3
                                                    class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4"
                                                >
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="text-primary-base hover:text-secondary-main transition-colors"
                                                        >Hệ thống hội nghị
                                                        truyền hình Yealink
                                                        VC800</a
                                                    >
                                                </h3>
                                                <div
                                                    class="flex items-center justify-between gap-1"
                                                >
                                                    <div class="flex flex-col">
                                                        <div
                                                            class="flex items-center gap-2"
                                                        >
                                                            <span
                                                                class="text-primary-price font-bold text-lg"
                                                                >68.500.000đ</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="text-primary-gray font-medium line-through"
                                                            >70.050.000đ</span
                                                        >
                                                    </div>
                                                    <!-- Add to Cart Button -->
                                                    <button
                                                        class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
                                                    >
                                                        <svg
                                                            width="23"
                                                            height="22"
                                                            viewBox="0 0 23 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Product Card 4 -->
                                    <div
                                        class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]"
                                    >
                                        <div
                                            class="product-card bg-white rounded-xl shadow-product hover:shadow-product transition-shadow duration-300 overflow-hidden group"
                                        >
                                            <div class="relative">
                                                <!-- Product Image -->
                                                <div class="">
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="aspect-square overflow-hidden"
                                                    >
                                                        <img
                                                            src="assets/images/image-product-4.png"
                                                            alt="Hệ thống hội nghị truyền hình Polycom CX5500"
                                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    </a>
                                                </div>

                                                <!-- Badges -->
                                                <div
                                                    class="absolute top-4 left-4 flex flex-col gap-1"
                                                >
                                                    <span
                                                        class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        -12%
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Product Info -->
                                            <div class="px-4 pb-4">
                                                <div>
                                                    <span
                                                        class="text-sm text-primary-gray font-medium"
                                                        >Thiết bị âm thanh</span
                                                    >
                                                </div>
                                                <h3
                                                    class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4"
                                                >
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="text-primary-base hover:text-secondary-main transition-colors"
                                                        >Loa liền công suất
                                                        Electro-Voice (EV)
                                                        ELX200-12P</a
                                                    >
                                                </h3>
                                                <div
                                                    class="flex items-center justify-between gap-1"
                                                >
                                                    <div class="flex flex-col">
                                                        <div
                                                            class="flex items-center gap-2"
                                                        >
                                                            <span
                                                                class="text-primary-price font-bold text-lg"
                                                                >68.500.000đ</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="text-primary-gray font-medium line-through"
                                                            >70.050.000đ</span
                                                        >
                                                    </div>
                                                    <!-- Add to Cart Button -->
                                                    <button
                                                        class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
                                                    >
                                                        <svg
                                                            width="23"
                                                            height="22"
                                                            viewBox="0 0 23 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Product Card 1 -->
                                    <div
                                        class="swiper-slide px-2 py-3 lg:px-[15px] lg:pt-[15px] lg:pb-[30px]"
                                    >
                                        <div
                                            class="product-card bg-white rounded-xl shadow-product hover:shadow-post transition-shadow duration-300 overflow-hidden group"
                                        >
                                            <div class="relative">
                                                <!-- Product Image -->
                                                <div class="">
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="aspect-square overflow-hidden"
                                                    >
                                                        <img
                                                            src="assets/images/image-product-1.png"
                                                            alt="Hệ thống hội nghị truyền hình Polycom CX5500"
                                                            class="w-full h-full object-contain p-4 group-hover:scale-105 transition-transform duration-300"
                                                        />
                                                    </a>
                                                </div>

                                                <!-- Badges -->
                                                <div
                                                    class="absolute top-4 left-4 flex flex-col gap-1"
                                                >
                                                    <span
                                                        class="bg-primary-badge1 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center justify-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M8.95525 5.86001H7.41025V2.26001C7.41025 1.42001 6.95525 1.25001 6.40025 1.88001L6.00025 2.33501L2.61525 6.18501C2.15025 6.71001 2.34525 7.14001 3.04525 7.14001H4.59025V10.74C4.59025 11.58 5.04525 11.75 5.60025 11.12L6.00025 10.665L9.38525 6.81501C9.85025 6.29001 9.65525 5.86001 8.95525 5.86001Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        -12%
                                                    </span>
                                                    <span
                                                        class="bg-primary-badge2 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="12"
                                                            viewBox="0 0 12 12"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M6 1L7.5 4.5H11L8.25 6.75L9.75 10.25L6 8L2.25 10.25L3.75 6.75L1 4.5H4.5L6 1Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        Nổi bật
                                                    </span>
                                                    <span
                                                        class="bg-primary-badge3 text-white text-xs font-bold px-2 py-1 rounded-[4px] flex items-center gap-1"
                                                    >
                                                        <svg
                                                            width="12"
                                                            height="13"
                                                            viewBox="0 0 12 13"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M6 1.5C3.24 1.5 1 3.74 1 6.5C1 9.26 3.24 11.5 6 11.5C8.76 11.5 11 9.26 11 6.5C11 3.74 8.76 1.5 6 1.5ZM6.165 9C6.075 9.03 5.92 9.03 5.83 9C5.05 8.735 3.3 7.62 3.3 5.73C3.3 4.895 3.97 4.22 4.8 4.22C5.29 4.22 5.725 4.455 6 4.825C6.27 4.46 6.71 4.22 7.2 4.22C8.03 4.22 8.7 4.895 8.7 5.73C8.7 7.62 6.95 8.735 6.165 9Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                        Bán chạy
                                                    </span>
                                                </div>
                                            </div>

                                            <!-- Product Info -->
                                            <div class="px-4 pb-4">
                                                <div>
                                                    <span
                                                        class="text-sm text-primary-gray font-medium"
                                                        >Thiết bị âm thanh</span
                                                    >
                                                </div>
                                                <h3
                                                    class="font-medium text-lg/[1.4] line-clamp-2 cursor-pointer mb-4"
                                                >
                                                    <a
                                                        href="pages/product-detail.html"
                                                        class="text-primary-base hover:text-secondary-main transition-colors"
                                                        >Hệ thống hội nghị
                                                        truyền hình Polycom
                                                        CX5500</a
                                                    >
                                                </h3>
                                                <div
                                                    class="flex items-center justify-between gap-1"
                                                >
                                                    <div class="flex flex-col">
                                                        <div
                                                            class="flex items-center gap-2"
                                                        >
                                                            <span
                                                                class="text-primary-price font-bold text-lg"
                                                                >68.500.000đ</span
                                                            >
                                                        </div>
                                                        <span
                                                            class="text-primary-gray font-medium line-through"
                                                            >70.050.000đ</span
                                                        >
                                                    </div>
                                                    <!-- Add to Cart Button -->
                                                    <button
                                                        class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg"
                                                    >
                                                        <svg
                                                            width="23"
                                                            height="22"
                                                            viewBox="0 0 23 22"
                                                            fill="none"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                        >
                                                            <path
                                                                d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                                                                fill="white"
                                                            />
                                                            <path
                                                                d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                                                                fill="white"
                                                            />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pagination -->
                            <div
                                class="swiper-pagination relative mt-5 lg:mt-[40px]"
                            ></div>
                            <!-- Navigation buttons -->
                            <div
                                class="swiper-button-next bg-transparent xl:-right-8"
                            ></div>
                            <div
                                class="swiper-button-prev bg-transparent xl:-left-8"
                            ></div>
                        </div>
                    </div>
                </div>
                <div
                    class="bg-img3 bg-no-repeat bg-cover bg-center pt-12 lg:pt-[70px] xl:pt-[93px] pb-12 lg:pb-[70px] xl:pb-[93px]"
                >
                    <div class="count-up container mx-auto">
                        <div
                            class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6"
                        >
                            <!-- Item 1 -->
                            <div class="col-span-1">
                                <div class="relative flex justify-center">
                                    <svg
                                        width="242"
                                        class="h-auto"
                                        viewBox="0 0 242 242"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <g filter="url(#filter0_d_1_4900)">
                                            <circle
                                                cx="121"
                                                cy="113"
                                                r="103"
                                                fill="white"
                                            />
                                        </g>
                                        <path
                                            d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z"
                                            fill="url(#paint0_linear_1_4900)"
                                        />
                                        <circle
                                            cx="121"
                                            cy="113"
                                            r="92"
                                            fill="white"
                                        />
                                        <defs>
                                            <filter
                                                id="filter0_d_1_4900"
                                                x="0"
                                                y="0"
                                                width="242"
                                                height="242"
                                                filterUnits="userSpaceOnUse"
                                                color-interpolation-filters="sRGB"
                                            >
                                                <feFlood
                                                    flood-opacity="0"
                                                    result="BackgroundImageFix"
                                                />
                                                <feColorMatrix
                                                    in="SourceAlpha"
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha"
                                                />
                                                <feOffset dy="8" />
                                                <feGaussianBlur
                                                    stdDeviation="9"
                                                />
                                                <feComposite
                                                    in2="hardAlpha"
                                                    operator="out"
                                                />
                                                <feColorMatrix
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in2="BackgroundImageFix"
                                                    result="effect1_dropShadow_1_4900"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in="SourceGraphic"
                                                    in2="effect1_dropShadow_1_4900"
                                                    result="shape"
                                                />
                                            </filter>
                                            <linearGradient
                                                id="paint0_linear_1_4900"
                                                x1="24.5742"
                                                y1="113"
                                                x2="217.425"
                                                y2="113"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#04389D" />
                                                <stop
                                                    offset="1"
                                                    stop-color="#17C1F5"
                                                />
                                            </linearGradient>
                                        </defs>
                                    </svg>

                                    <div
                                        class="absolute inset-0 flex items-center justify-center"
                                    >
                                        <span
                                            class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main"
                                            >5.000+</span
                                        >
                                    </div>
                                </div>
                                <p
                                    class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center"
                                >
                                    Khách hàng
                                </p>
                            </div>

                            <!-- Item 2 -->
                            <div class="col-span-1">
                                <div class="relative flex justify-center">
                                    <svg
                                        width="242"
                                        class="h-auto"
                                        viewBox="0 0 242 242"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <g filter="url(#filter0_d_1_4900)">
                                            <circle
                                                cx="121"
                                                cy="113"
                                                r="103"
                                                fill="white"
                                            />
                                        </g>
                                        <path
                                            d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z"
                                            fill="url(#paint0_linear_1_4900)"
                                        />
                                        <circle
                                            cx="121"
                                            cy="113"
                                            r="92"
                                            fill="white"
                                        />
                                        <defs>
                                            <filter
                                                id="filter0_d_1_4900"
                                                x="0"
                                                y="0"
                                                width="242"
                                                height="242"
                                                filterUnits="userSpaceOnUse"
                                                color-interpolation-filters="sRGB"
                                            >
                                                <feFlood
                                                    flood-opacity="0"
                                                    result="BackgroundImageFix"
                                                />
                                                <feColorMatrix
                                                    in="SourceAlpha"
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha"
                                                />
                                                <feOffset dy="8" />
                                                <feGaussianBlur
                                                    stdDeviation="9"
                                                />
                                                <feComposite
                                                    in2="hardAlpha"
                                                    operator="out"
                                                />
                                                <feColorMatrix
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in2="BackgroundImageFix"
                                                    result="effect1_dropShadow_1_4900"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in="SourceGraphic"
                                                    in2="effect1_dropShadow_1_4900"
                                                    result="shape"
                                                />
                                            </filter>
                                            <linearGradient
                                                id="paint0_linear_1_4900"
                                                x1="24.5742"
                                                y1="113"
                                                x2="217.425"
                                                y2="113"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#04389D" />
                                                <stop
                                                    offset="1"
                                                    stop-color="#17C1F5"
                                                />
                                            </linearGradient>
                                        </defs>
                                    </svg>

                                    <div
                                        class="absolute inset-0 flex items-center justify-center"
                                    >
                                        <span
                                            class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main"
                                            >320+</span
                                        >
                                    </div>
                                </div>
                                <p
                                    class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center"
                                >
                                    Nhân sự kinh nghiệm
                                </p>
                            </div>

                            <!-- Item 3 -->
                            <div class="col-span-1">
                                <div class="relative flex justify-center">
                                    <svg
                                        width="242"
                                        class="h-auto"
                                        viewBox="0 0 242 242"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <g filter="url(#filter0_d_1_4900)">
                                            <circle
                                                cx="121"
                                                cy="113"
                                                r="103"
                                                fill="white"
                                            />
                                        </g>
                                        <path
                                            d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z"
                                            fill="url(#paint0_linear_1_4900)"
                                        />
                                        <circle
                                            cx="121"
                                            cy="113"
                                            r="92"
                                            fill="white"
                                        />
                                        <defs>
                                            <filter
                                                id="filter0_d_1_4900"
                                                x="0"
                                                y="0"
                                                width="242"
                                                height="242"
                                                filterUnits="userSpaceOnUse"
                                                color-interpolation-filters="sRGB"
                                            >
                                                <feFlood
                                                    flood-opacity="0"
                                                    result="BackgroundImageFix"
                                                />
                                                <feColorMatrix
                                                    in="SourceAlpha"
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha"
                                                />
                                                <feOffset dy="8" />
                                                <feGaussianBlur
                                                    stdDeviation="9"
                                                />
                                                <feComposite
                                                    in2="hardAlpha"
                                                    operator="out"
                                                />
                                                <feColorMatrix
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in2="BackgroundImageFix"
                                                    result="effect1_dropShadow_1_4900"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in="SourceGraphic"
                                                    in2="effect1_dropShadow_1_4900"
                                                    result="shape"
                                                />
                                            </filter>
                                            <linearGradient
                                                id="paint0_linear_1_4900"
                                                x1="24.5742"
                                                y1="113"
                                                x2="217.425"
                                                y2="113"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#04389D" />
                                                <stop
                                                    offset="1"
                                                    stop-color="#17C1F5"
                                                />
                                            </linearGradient>
                                        </defs>
                                    </svg>

                                    <div
                                        class="absolute inset-0 flex items-center justify-center"
                                    >
                                        <span
                                            class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main"
                                            >5.000+</span
                                        >
                                    </div>
                                </div>
                                <p
                                    class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center"
                                >
                                    Dự án tham gia
                                </p>
                            </div>

                            <!-- Item 4 -->
                            <div class="col-span-1">
                                <div class="relative flex justify-center">
                                    <svg
                                        width="242"
                                        class="h-auto"
                                        viewBox="0 0 242 242"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <g filter="url(#filter0_d_1_4900)">
                                            <circle
                                                cx="121"
                                                cy="113"
                                                r="103"
                                                fill="white"
                                            />
                                        </g>
                                        <path
                                            d="M121 16.5747C144.415 16.5747 167.031 25.0953 184.626 40.5461C202.22 55.9968 213.592 77.3221 216.617 100.542C219.642 123.761 214.115 147.288 201.067 166.731C188.02 186.175 168.342 200.206 145.708 206.206C123.074 212.206 99.0305 209.766 78.0641 199.339C57.0978 188.913 40.6418 171.213 31.7675 149.544C22.8931 127.876 22.207 103.718 29.837 81.58C37.467 59.4423 52.8918 40.8373 73.2325 29.2377L121 113L121 16.5747Z"
                                            fill="url(#paint0_linear_1_4900)"
                                        />
                                        <circle
                                            cx="121"
                                            cy="113"
                                            r="92"
                                            fill="white"
                                        />
                                        <defs>
                                            <filter
                                                id="filter0_d_1_4900"
                                                x="0"
                                                y="0"
                                                width="242"
                                                height="242"
                                                filterUnits="userSpaceOnUse"
                                                color-interpolation-filters="sRGB"
                                            >
                                                <feFlood
                                                    flood-opacity="0"
                                                    result="BackgroundImageFix"
                                                />
                                                <feColorMatrix
                                                    in="SourceAlpha"
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                                    result="hardAlpha"
                                                />
                                                <feOffset dy="8" />
                                                <feGaussianBlur
                                                    stdDeviation="9"
                                                />
                                                <feComposite
                                                    in2="hardAlpha"
                                                    operator="out"
                                                />
                                                <feColorMatrix
                                                    type="matrix"
                                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in2="BackgroundImageFix"
                                                    result="effect1_dropShadow_1_4900"
                                                />
                                                <feBlend
                                                    mode="normal"
                                                    in="SourceGraphic"
                                                    in2="effect1_dropShadow_1_4900"
                                                    result="shape"
                                                />
                                            </filter>
                                            <linearGradient
                                                id="paint0_linear_1_4900"
                                                x1="24.5742"
                                                y1="113"
                                                x2="217.425"
                                                y2="113"
                                                gradientUnits="userSpaceOnUse"
                                            >
                                                <stop stop-color="#04389D" />
                                                <stop
                                                    offset="1"
                                                    stop-color="#17C1F5"
                                                />
                                            </linearGradient>
                                        </defs>
                                    </svg>

                                    <div
                                        class="absolute inset-0 flex items-center justify-center"
                                    >
                                        <span
                                            class="font-bold text-2xl sm:text-3xl xl:text-[38px]/[1.4] text-secondary-main"
                                            >20.000+</span
                                        >
                                    </div>
                                </div>
                                <p
                                    class="text-white text-lg sm:text-xl md:text-2xl lg:text-[28px]/[1.4] font-bold text-center"
                                >
                                    Thiết bị bán ra
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pt-12 pb-12 lg:pt-[70px] lg:pb-[70px]">
                    <div class="container mx-auto">
                        <div class="grid grid-cols-3 gap-[30px] items-center">
                            <div class="col-span-1">
                                <h2
                                    class="font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6"
                                >
                                    Đối tác
                                </h2>
                                <p class="lg:text-xl leading-[1.4]">
                                    Cảm ơn các đối tác đã luôn đồng hành với AV
                                    Plus để mang lại cho khách hàng những sản
                                    phẩm tốt nhất về cả chất lượn, thẩm mỹ và
                                    tiện ích.
                                </p>
                            </div>
                            <div class="col-span-2">
                                <div
                                    class="grid grid-cols-4 gap-4 items-center"
                                >
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-1.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-2.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-3.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-4.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-5.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-6.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-7.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-8.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-9.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-10.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-11.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                    <div class="col-span-1">
                                        <img
                                            src="assets/images/partner-12.png"
                                            alt="Partner Image"
                                            class="w-full aspect-[2.03603604] object-contain h-auto"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="bg-primary-grey pt-12 pb-12 lg:pt-[70px] lg:pb-[70px]"
                >
                    <div class="container mx-auto">
                        <h2
                            class="text-center font-semibold text-2xl md:text-3xl lg:text-4xl xl:text-[46px]/[1.4] mb-6 lg:mb-10"
                        >
                            Khách hàng
                        </h2>
                        <div
                            class="swiper-slider relative"
                            data-items="1"
                            data-mobile="1"
                            data-tablet="1"
                            data-desktop="1"
                            data-large="1"
                            data-xlarge="1"
                            data-spacing="0"
                            data-loop="true"
                            data-navigation="true"
                            data-autoplay="true"
                            data-autoplay-delay="3000"
                        >
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <!-- Product Card 1 -->
                                    <div class="swiper-slide">
                                        <div
                                            class="grid grid-cols-1 md:grid-cols-2 gap-[30px] items-center"
                                        >
                                            <div class="col-span-1">
                                                <div class="relative">
                                                    <img
                                                        src="assets/images/img-customer.jpg"
                                                        alt="Customer Image"
                                                        class="w-full aspect-[1.5] h-auto object-cover rounded-[20px]"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-1">
                                                <span class="mb-4 block">
                                                    <svg
                                                        width="49"
                                                        height="49"
                                                        viewBox="0 0 49 49"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M13.0419 20.5107C12.5944 20.5107 12.165 20.5785 11.7377 20.6404C11.8761 20.1776 12.0186 19.7068 12.2473 19.2839C12.476 18.6695 12.8332 18.1369 13.1883 17.6004C13.4853 17.0199 14.009 16.6269 14.3942 16.1302C14.7975 15.6475 15.3473 15.3263 15.7827 14.9254C16.21 14.5065 16.7698 14.2971 17.2153 14.0018C17.6808 13.7365 18.0861 13.4433 18.5194 13.3037L19.6009 12.8608L20.552 12.4679L19.5788 8.60205L18.381 8.88929C17.9978 8.98504 17.5303 9.09675 16.9986 9.2304C16.4548 9.33013 15.875 9.60341 15.2289 9.85276C14.5908 10.136 13.8525 10.3275 13.1663 10.7823C12.476 11.2172 11.6795 11.5802 10.9772 12.1627C10.2971 12.7631 9.47642 13.2837 8.87047 14.0477C8.20835 14.7618 7.55425 15.5119 7.04662 16.3656C6.45873 17.1795 6.05945 18.0731 5.6381 18.9568C5.25687 19.8405 4.94989 20.7441 4.69908 21.6218C4.22356 23.3811 4.01087 25.0527 3.92861 26.483C3.86039 27.9152 3.90052 29.1061 3.98479 29.9678C4.01489 30.3747 4.07107 30.7697 4.11119 31.043L4.16136 31.3781L4.21352 31.3661C4.57039 33.0234 5.39191 34.5464 6.58306 35.7589C7.7742 36.9714 9.28629 37.8239 10.9444 38.2177C12.6025 38.6116 14.3389 38.5307 15.9527 37.9844C17.5665 37.4381 18.9918 36.4487 20.0636 35.1308C21.1355 33.8128 21.8101 32.2201 22.0095 30.5369C22.2089 28.8537 21.9248 27.1487 21.1903 25.6193C20.4557 24.0899 19.3006 22.7984 17.8586 21.8944C16.4166 20.9903 14.7466 20.5106 13.0419 20.5107ZM35.1127 20.5107C34.6653 20.5107 34.2359 20.5785 33.8085 20.6404C33.947 20.1776 34.0894 19.7068 34.3182 19.2839C34.5469 18.6695 34.904 18.1369 35.2592 17.6004C35.5561 17.0199 36.0798 16.6269 36.465 16.1302C36.8683 15.6475 37.4181 15.3263 37.8535 14.9254C38.2809 14.5065 38.8407 14.2971 39.2861 14.0018C39.7516 13.7365 40.1569 13.4433 40.5903 13.3037L41.6718 12.8608L42.6228 12.4679L41.6497 8.60205L40.4518 8.88929C40.0686 8.98504 39.6011 9.09675 39.0694 9.2304C38.5257 9.33013 37.9458 9.60341 37.2997 9.85276C36.6637 10.138 35.9233 10.3275 35.2371 10.7843C34.5469 11.2192 33.7503 11.5822 33.0481 12.1647C32.3679 12.7651 31.5473 13.2857 30.9413 14.0477C30.2792 14.7618 29.6251 15.5119 29.1175 16.3656C28.5296 17.1795 28.1303 18.0731 27.7089 18.9568C27.3277 19.8405 27.0207 20.7441 26.7699 21.6218C26.2944 23.3811 26.0817 25.0527 25.9995 26.483C25.9312 27.9152 25.9714 29.1061 26.0556 29.9678C26.0857 30.3747 26.1419 30.7697 26.182 31.043L26.2322 31.3781L26.2844 31.3661C26.6412 33.0234 27.4628 34.5464 28.6539 35.7589C29.845 36.9714 31.3571 37.8239 33.0153 38.2177C34.6734 38.6116 36.4098 38.5307 38.0236 37.9844C39.6374 37.4381 41.0626 36.4487 42.1345 35.1308C43.2063 33.8128 43.8809 32.2201 44.0803 30.5369C44.2797 28.8537 43.9957 27.1487 43.2611 25.6193C42.5266 24.0899 41.3715 22.7984 39.9295 21.8944C38.4875 20.9903 36.8175 20.5106 35.1127 20.5107Z"
                                                            fill="url(#paint0_linear_1_4959)"
                                                        />
                                                        <defs>
                                                            <linearGradient
                                                                id="paint0_linear_1_4959"
                                                                x1="24.0182"
                                                                y1="8.60205"
                                                                x2="24.0182"
                                                                y2="38.4634"
                                                                gradientUnits="userSpaceOnUse"
                                                            >
                                                                <stop
                                                                    stop-color="#04389D"
                                                                />
                                                                <stop
                                                                    offset="1"
                                                                    stop-color="#17C1F5"
                                                                />
                                                            </linearGradient>
                                                        </defs>
                                                    </svg>
                                                </span>
                                                <h4
                                                    class="text-xl leading-[1.4] font-bold mb-2"
                                                >
                                                    Customer Name
                                                </h4>
                                                <div class="mb-6">
                                                    <svg
                                                        width="116"
                                                        height="21"
                                                        viewBox="0 0 116 21"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M11.4417 3.36221L12.9083 6.29554C13.1083 6.70387 13.6417 7.09554 14.0917 7.17054L16.75 7.61221C18.45 7.89554 18.85 9.12887 17.625 10.3455L15.5583 12.4122C15.2083 12.7622 15.0167 13.4372 15.125 13.9205L15.7167 16.4789C16.1833 18.5039 15.1083 19.2872 13.3167 18.2289L10.825 16.7539C10.375 16.4872 9.63332 16.4872 9.17499 16.7539L6.68332 18.2289C4.89999 19.2872 3.81665 18.4955 4.28332 16.4789L4.87499 13.9205C4.98332 13.4372 4.79165 12.7622 4.44165 12.4122L2.37499 10.3455C1.15832 9.12887 1.54999 7.89554 3.24999 7.61221L5.90832 7.17054C6.34999 7.09554 6.88332 6.70387 7.08332 6.29554L8.54999 3.36221C9.34999 1.77054 10.65 1.77054 11.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M35.4417 3.36221L36.9083 6.29554C37.1083 6.70387 37.6417 7.09554 38.0917 7.17054L40.75 7.61221C42.45 7.89554 42.85 9.12887 41.625 10.3455L39.5583 12.4122C39.2083 12.7622 39.0167 13.4372 39.125 13.9205L39.7167 16.4789C40.1833 18.5039 39.1083 19.2872 37.3167 18.2289L34.825 16.7539C34.375 16.4872 33.6333 16.4872 33.175 16.7539L30.6833 18.2289C28.9 19.2872 27.8167 18.4955 28.2833 16.4789L28.875 13.9205C28.9833 13.4372 28.7917 12.7622 28.4417 12.4122L26.375 10.3455C25.1583 9.12887 25.55 7.89554 27.25 7.61221L29.9083 7.17054C30.35 7.09554 30.8833 6.70387 31.0833 6.29554L32.55 3.36221C33.35 1.77054 34.65 1.77054 35.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M59.4417 3.36221L60.9084 6.29554C61.1084 6.70387 61.6417 7.09554 62.0917 7.17054L64.75 7.61221C66.45 7.89554 66.85 9.12887 65.625 10.3455L63.5584 12.4122C63.2084 12.7622 63.0167 13.4372 63.125 13.9205L63.7167 16.4789C64.1834 18.5039 63.1084 19.2872 61.3167 18.2289L58.825 16.7539C58.375 16.4872 57.6334 16.4872 57.175 16.7539L54.6834 18.2289C52.9 19.2872 51.8167 18.4955 52.2834 16.4789L52.875 13.9205C52.9834 13.4372 52.7917 12.7622 52.4417 12.4122L50.375 10.3455C49.1584 9.12887 49.55 7.89554 51.25 7.61221L53.9084 7.17054C54.35 7.09554 54.8834 6.70387 55.0834 6.29554L56.55 3.36221C57.35 1.77054 58.65 1.77054 59.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M83.4417 3.36221L84.9084 6.29554C85.1084 6.70387 85.6417 7.09554 86.0917 7.17054L88.75 7.61221C90.45 7.89554 90.85 9.12887 89.625 10.3455L87.5584 12.4122C87.2084 12.7622 87.0167 13.4372 87.125 13.9205L87.7167 16.4789C88.1834 18.5039 87.1084 19.2872 85.3167 18.2289L82.825 16.7539C82.375 16.4872 81.6334 16.4872 81.175 16.7539L78.6834 18.2289C76.9 19.2872 75.8167 18.4955 76.2834 16.4789L76.875 13.9205C76.9834 13.4372 76.7917 12.7622 76.4417 12.4122L74.375 10.3455C73.1584 9.12887 73.55 7.89554 75.25 7.61221L77.9084 7.17054C78.35 7.09554 78.8834 6.70387 79.0834 6.29554L80.55 3.36221C81.35 1.77054 82.65 1.77054 83.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M107.442 3.36221L108.908 6.29554C109.108 6.70387 109.642 7.09554 110.092 7.17054L112.75 7.61221C114.45 7.89554 114.85 9.12887 113.625 10.3455L111.558 12.4122C111.208 12.7622 111.017 13.4372 111.125 13.9205L111.717 16.4789C112.183 18.5039 111.108 19.2872 109.317 18.2289L106.825 16.7539C106.375 16.4872 105.633 16.4872 105.175 16.7539L102.683 18.2289C100.9 19.2872 99.8167 18.4955 100.283 16.4789L100.875 13.9205C100.983 13.4372 100.792 12.7622 100.442 12.4122L98.375 10.3455C97.1584 9.12887 97.55 7.89554 99.25 7.61221L101.908 7.17054C102.35 7.09554 102.883 6.70387 103.083 6.29554L104.55 3.36221C105.35 1.77054 106.65 1.77054 107.442 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                    </svg>
                                                </div>
                                                <p
                                                    class="text-lg leading-[1.4] text-[#2B2928]"
                                                >
                                                    Lorem ipsum dolor sit amet
                                                    consectetur. Facilisis
                                                    egestas amet vitae
                                                    scelerisque sit in cursus.
                                                    Aliquam dignissim euismod
                                                    tincidunt amet vitae amet.
                                                    Suspendisse feugiat in arcu
                                                    lorem sit tincidunt. Dui
                                                    nibh in in eu quis felis
                                                    velit amet aliquet. Nisl
                                                    facilisi in at non amet
                                                    ultrices mus. Facilisis
                                                    mauris gravida in sit sit
                                                    lorem fames faucibus. Lacus
                                                    feugiat cursus.
                                                </p>
                                                <button
                                                    class="bg-gradient2 px-6 h-11 rounded-full text-white font-bold hover:bg-gradient8 transition-colors duration-300 mt-5 lg:mt-8"
                                                >
                                                    <span
                                                        >Chia sẻ trải nghiệm của
                                                        bạn</span
                                                    >
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div
                                            class="grid grid-cols-1 md:grid-cols-2 gap-[30px] items-center"
                                        >
                                            <div class="col-span-1">
                                                <div class="relative">
                                                    <img
                                                        src="assets/images/img-customer.jpg"
                                                        alt="Customer Image"
                                                        class="w-full aspect-[1.5] h-auto object-cover rounded-[20px]"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-1">
                                                <span class="mb-4 block">
                                                    <svg
                                                        width="49"
                                                        height="49"
                                                        viewBox="0 0 49 49"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M13.0419 20.5107C12.5944 20.5107 12.165 20.5785 11.7377 20.6404C11.8761 20.1776 12.0186 19.7068 12.2473 19.2839C12.476 18.6695 12.8332 18.1369 13.1883 17.6004C13.4853 17.0199 14.009 16.6269 14.3942 16.1302C14.7975 15.6475 15.3473 15.3263 15.7827 14.9254C16.21 14.5065 16.7698 14.2971 17.2153 14.0018C17.6808 13.7365 18.0861 13.4433 18.5194 13.3037L19.6009 12.8608L20.552 12.4679L19.5788 8.60205L18.381 8.88929C17.9978 8.98504 17.5303 9.09675 16.9986 9.2304C16.4548 9.33013 15.875 9.60341 15.2289 9.85276C14.5908 10.136 13.8525 10.3275 13.1663 10.7823C12.476 11.2172 11.6795 11.5802 10.9772 12.1627C10.2971 12.7631 9.47642 13.2837 8.87047 14.0477C8.20835 14.7618 7.55425 15.5119 7.04662 16.3656C6.45873 17.1795 6.05945 18.0731 5.6381 18.9568C5.25687 19.8405 4.94989 20.7441 4.69908 21.6218C4.22356 23.3811 4.01087 25.0527 3.92861 26.483C3.86039 27.9152 3.90052 29.1061 3.98479 29.9678C4.01489 30.3747 4.07107 30.7697 4.11119 31.043L4.16136 31.3781L4.21352 31.3661C4.57039 33.0234 5.39191 34.5464 6.58306 35.7589C7.7742 36.9714 9.28629 37.8239 10.9444 38.2177C12.6025 38.6116 14.3389 38.5307 15.9527 37.9844C17.5665 37.4381 18.9918 36.4487 20.0636 35.1308C21.1355 33.8128 21.8101 32.2201 22.0095 30.5369C22.2089 28.8537 21.9248 27.1487 21.1903 25.6193C20.4557 24.0899 19.3006 22.7984 17.8586 21.8944C16.4166 20.9903 14.7466 20.5106 13.0419 20.5107ZM35.1127 20.5107C34.6653 20.5107 34.2359 20.5785 33.8085 20.6404C33.947 20.1776 34.0894 19.7068 34.3182 19.2839C34.5469 18.6695 34.904 18.1369 35.2592 17.6004C35.5561 17.0199 36.0798 16.6269 36.465 16.1302C36.8683 15.6475 37.4181 15.3263 37.8535 14.9254C38.2809 14.5065 38.8407 14.2971 39.2861 14.0018C39.7516 13.7365 40.1569 13.4433 40.5903 13.3037L41.6718 12.8608L42.6228 12.4679L41.6497 8.60205L40.4518 8.88929C40.0686 8.98504 39.6011 9.09675 39.0694 9.2304C38.5257 9.33013 37.9458 9.60341 37.2997 9.85276C36.6637 10.138 35.9233 10.3275 35.2371 10.7843C34.5469 11.2192 33.7503 11.5822 33.0481 12.1647C32.3679 12.7651 31.5473 13.2857 30.9413 14.0477C30.2792 14.7618 29.6251 15.5119 29.1175 16.3656C28.5296 17.1795 28.1303 18.0731 27.7089 18.9568C27.3277 19.8405 27.0207 20.7441 26.7699 21.6218C26.2944 23.3811 26.0817 25.0527 25.9995 26.483C25.9312 27.9152 25.9714 29.1061 26.0556 29.9678C26.0857 30.3747 26.1419 30.7697 26.182 31.043L26.2322 31.3781L26.2844 31.3661C26.6412 33.0234 27.4628 34.5464 28.6539 35.7589C29.845 36.9714 31.3571 37.8239 33.0153 38.2177C34.6734 38.6116 36.4098 38.5307 38.0236 37.9844C39.6374 37.4381 41.0626 36.4487 42.1345 35.1308C43.2063 33.8128 43.8809 32.2201 44.0803 30.5369C44.2797 28.8537 43.9957 27.1487 43.2611 25.6193C42.5266 24.0899 41.3715 22.7984 39.9295 21.8944C38.4875 20.9903 36.8175 20.5106 35.1127 20.5107Z"
                                                            fill="url(#paint0_linear_1_4959)"
                                                        />
                                                        <defs>
                                                            <linearGradient
                                                                id="paint0_linear_1_4959"
                                                                x1="24.0182"
                                                                y1="8.60205"
                                                                x2="24.0182"
                                                                y2="38.4634"
                                                                gradientUnits="userSpaceOnUse"
                                                            >
                                                                <stop
                                                                    stop-color="#04389D"
                                                                />
                                                                <stop
                                                                    offset="1"
                                                                    stop-color="#17C1F5"
                                                                />
                                                            </linearGradient>
                                                        </defs>
                                                    </svg>
                                                </span>
                                                <h4
                                                    class="text-xl leading-[1.4] font-bold mb-2"
                                                >
                                                    Customer Name
                                                </h4>
                                                <div class="mb-6">
                                                    <svg
                                                        width="116"
                                                        height="21"
                                                        viewBox="0 0 116 21"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M11.4417 3.36221L12.9083 6.29554C13.1083 6.70387 13.6417 7.09554 14.0917 7.17054L16.75 7.61221C18.45 7.89554 18.85 9.12887 17.625 10.3455L15.5583 12.4122C15.2083 12.7622 15.0167 13.4372 15.125 13.9205L15.7167 16.4789C16.1833 18.5039 15.1083 19.2872 13.3167 18.2289L10.825 16.7539C10.375 16.4872 9.63332 16.4872 9.17499 16.7539L6.68332 18.2289C4.89999 19.2872 3.81665 18.4955 4.28332 16.4789L4.87499 13.9205C4.98332 13.4372 4.79165 12.7622 4.44165 12.4122L2.37499 10.3455C1.15832 9.12887 1.54999 7.89554 3.24999 7.61221L5.90832 7.17054C6.34999 7.09554 6.88332 6.70387 7.08332 6.29554L8.54999 3.36221C9.34999 1.77054 10.65 1.77054 11.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M35.4417 3.36221L36.9083 6.29554C37.1083 6.70387 37.6417 7.09554 38.0917 7.17054L40.75 7.61221C42.45 7.89554 42.85 9.12887 41.625 10.3455L39.5583 12.4122C39.2083 12.7622 39.0167 13.4372 39.125 13.9205L39.7167 16.4789C40.1833 18.5039 39.1083 19.2872 37.3167 18.2289L34.825 16.7539C34.375 16.4872 33.6333 16.4872 33.175 16.7539L30.6833 18.2289C28.9 19.2872 27.8167 18.4955 28.2833 16.4789L28.875 13.9205C28.9833 13.4372 28.7917 12.7622 28.4417 12.4122L26.375 10.3455C25.1583 9.12887 25.55 7.89554 27.25 7.61221L29.9083 7.17054C30.35 7.09554 30.8833 6.70387 31.0833 6.29554L32.55 3.36221C33.35 1.77054 34.65 1.77054 35.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M59.4417 3.36221L60.9084 6.29554C61.1084 6.70387 61.6417 7.09554 62.0917 7.17054L64.75 7.61221C66.45 7.89554 66.85 9.12887 65.625 10.3455L63.5584 12.4122C63.2084 12.7622 63.0167 13.4372 63.125 13.9205L63.7167 16.4789C64.1834 18.5039 63.1084 19.2872 61.3167 18.2289L58.825 16.7539C58.375 16.4872 57.6334 16.4872 57.175 16.7539L54.6834 18.2289C52.9 19.2872 51.8167 18.4955 52.2834 16.4789L52.875 13.9205C52.9834 13.4372 52.7917 12.7622 52.4417 12.4122L50.375 10.3455C49.1584 9.12887 49.55 7.89554 51.25 7.61221L53.9084 7.17054C54.35 7.09554 54.8834 6.70387 55.0834 6.29554L56.55 3.36221C57.35 1.77054 58.65 1.77054 59.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M83.4417 3.36221L84.9084 6.29554C85.1084 6.70387 85.6417 7.09554 86.0917 7.17054L88.75 7.61221C90.45 7.89554 90.85 9.12887 89.625 10.3455L87.5584 12.4122C87.2084 12.7622 87.0167 13.4372 87.125 13.9205L87.7167 16.4789C88.1834 18.5039 87.1084 19.2872 85.3167 18.2289L82.825 16.7539C82.375 16.4872 81.6334 16.4872 81.175 16.7539L78.6834 18.2289C76.9 19.2872 75.8167 18.4955 76.2834 16.4789L76.875 13.9205C76.9834 13.4372 76.7917 12.7622 76.4417 12.4122L74.375 10.3455C73.1584 9.12887 73.55 7.89554 75.25 7.61221L77.9084 7.17054C78.35 7.09554 78.8834 6.70387 79.0834 6.29554L80.55 3.36221C81.35 1.77054 82.65 1.77054 83.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M107.442 3.36221L108.908 6.29554C109.108 6.70387 109.642 7.09554 110.092 7.17054L112.75 7.61221C114.45 7.89554 114.85 9.12887 113.625 10.3455L111.558 12.4122C111.208 12.7622 111.017 13.4372 111.125 13.9205L111.717 16.4789C112.183 18.5039 111.108 19.2872 109.317 18.2289L106.825 16.7539C106.375 16.4872 105.633 16.4872 105.175 16.7539L102.683 18.2289C100.9 19.2872 99.8167 18.4955 100.283 16.4789L100.875 13.9205C100.983 13.4372 100.792 12.7622 100.442 12.4122L98.375 10.3455C97.1584 9.12887 97.55 7.89554 99.25 7.61221L101.908 7.17054C102.35 7.09554 102.883 6.70387 103.083 6.29554L104.55 3.36221C105.35 1.77054 106.65 1.77054 107.442 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                    </svg>
                                                </div>
                                                <p
                                                    class="text-lg leading-[1.4] text-[#2B2928]"
                                                >
                                                    Lorem ipsum dolor sit amet
                                                    consectetur. Facilisis
                                                    egestas amet vitae
                                                    scelerisque sit in cursus.
                                                    Aliquam dignissim euismod
                                                    tincidunt amet vitae amet.
                                                    Suspendisse feugiat in arcu
                                                    lorem sit tincidunt. Dui
                                                    nibh in in eu quis felis
                                                    velit amet aliquet. Nisl
                                                    facilisi in at non amet
                                                    ultrices mus. Facilisis
                                                    mauris gravida in sit sit
                                                    lorem fames faucibus. Lacus
                                                    feugiat cursus.
                                                </p>
                                                <button
                                                    class="bg-gradient2 px-6 h-11 rounded-full text-white font-bold hover:bg-gradient8 transition-colors duration-300 mt-5 lg:mt-8"
                                                >
                                                    <span
                                                        >Chia sẻ trải nghiệm của
                                                        bạn</span
                                                    >
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div
                                            class="grid grid-cols-1 md:grid-cols-2 gap-[30px] items-center"
                                        >
                                            <div class="col-span-1">
                                                <div class="relative">
                                                    <img
                                                        src="assets/images/img-customer.jpg"
                                                        alt="Customer Image"
                                                        class="w-full aspect-[1.5] h-auto object-cover rounded-[20px]"
                                                    />
                                                </div>
                                            </div>
                                            <div class="col-span-1">
                                                <span class="mb-4 block">
                                                    <svg
                                                        width="49"
                                                        height="49"
                                                        viewBox="0 0 49 49"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M13.0419 20.5107C12.5944 20.5107 12.165 20.5785 11.7377 20.6404C11.8761 20.1776 12.0186 19.7068 12.2473 19.2839C12.476 18.6695 12.8332 18.1369 13.1883 17.6004C13.4853 17.0199 14.009 16.6269 14.3942 16.1302C14.7975 15.6475 15.3473 15.3263 15.7827 14.9254C16.21 14.5065 16.7698 14.2971 17.2153 14.0018C17.6808 13.7365 18.0861 13.4433 18.5194 13.3037L19.6009 12.8608L20.552 12.4679L19.5788 8.60205L18.381 8.88929C17.9978 8.98504 17.5303 9.09675 16.9986 9.2304C16.4548 9.33013 15.875 9.60341 15.2289 9.85276C14.5908 10.136 13.8525 10.3275 13.1663 10.7823C12.476 11.2172 11.6795 11.5802 10.9772 12.1627C10.2971 12.7631 9.47642 13.2837 8.87047 14.0477C8.20835 14.7618 7.55425 15.5119 7.04662 16.3656C6.45873 17.1795 6.05945 18.0731 5.6381 18.9568C5.25687 19.8405 4.94989 20.7441 4.69908 21.6218C4.22356 23.3811 4.01087 25.0527 3.92861 26.483C3.86039 27.9152 3.90052 29.1061 3.98479 29.9678C4.01489 30.3747 4.07107 30.7697 4.11119 31.043L4.16136 31.3781L4.21352 31.3661C4.57039 33.0234 5.39191 34.5464 6.58306 35.7589C7.7742 36.9714 9.28629 37.8239 10.9444 38.2177C12.6025 38.6116 14.3389 38.5307 15.9527 37.9844C17.5665 37.4381 18.9918 36.4487 20.0636 35.1308C21.1355 33.8128 21.8101 32.2201 22.0095 30.5369C22.2089 28.8537 21.9248 27.1487 21.1903 25.6193C20.4557 24.0899 19.3006 22.7984 17.8586 21.8944C16.4166 20.9903 14.7466 20.5106 13.0419 20.5107ZM35.1127 20.5107C34.6653 20.5107 34.2359 20.5785 33.8085 20.6404C33.947 20.1776 34.0894 19.7068 34.3182 19.2839C34.5469 18.6695 34.904 18.1369 35.2592 17.6004C35.5561 17.0199 36.0798 16.6269 36.465 16.1302C36.8683 15.6475 37.4181 15.3263 37.8535 14.9254C38.2809 14.5065 38.8407 14.2971 39.2861 14.0018C39.7516 13.7365 40.1569 13.4433 40.5903 13.3037L41.6718 12.8608L42.6228 12.4679L41.6497 8.60205L40.4518 8.88929C40.0686 8.98504 39.6011 9.09675 39.0694 9.2304C38.5257 9.33013 37.9458 9.60341 37.2997 9.85276C36.6637 10.138 35.9233 10.3275 35.2371 10.7843C34.5469 11.2192 33.7503 11.5822 33.0481 12.1647C32.3679 12.7651 31.5473 13.2857 30.9413 14.0477C30.2792 14.7618 29.6251 15.5119 29.1175 16.3656C28.5296 17.1795 28.1303 18.0731 27.7089 18.9568C27.3277 19.8405 27.0207 20.7441 26.7699 21.6218C26.2944 23.3811 26.0817 25.0527 25.9995 26.483C25.9312 27.9152 25.9714 29.1061 26.0556 29.9678C26.0857 30.3747 26.1419 30.7697 26.182 31.043L26.2322 31.3781L26.2844 31.3661C26.6412 33.0234 27.4628 34.5464 28.6539 35.7589C29.845 36.9714 31.3571 37.8239 33.0153 38.2177C34.6734 38.6116 36.4098 38.5307 38.0236 37.9844C39.6374 37.4381 41.0626 36.4487 42.1345 35.1308C43.2063 33.8128 43.8809 32.2201 44.0803 30.5369C44.2797 28.8537 43.9957 27.1487 43.2611 25.6193C42.5266 24.0899 41.3715 22.7984 39.9295 21.8944C38.4875 20.9903 36.8175 20.5106 35.1127 20.5107Z"
                                                            fill="url(#paint0_linear_1_4959)"
                                                        />
                                                        <defs>
                                                            <linearGradient
                                                                id="paint0_linear_1_4959"
                                                                x1="24.0182"
                                                                y1="8.60205"
                                                                x2="24.0182"
                                                                y2="38.4634"
                                                                gradientUnits="userSpaceOnUse"
                                                            >
                                                                <stop
                                                                    stop-color="#04389D"
                                                                />
                                                                <stop
                                                                    offset="1"
                                                                    stop-color="#17C1F5"
                                                                />
                                                            </linearGradient>
                                                        </defs>
                                                    </svg>
                                                </span>
                                                <h4
                                                    class="text-xl leading-[1.4] font-bold mb-2"
                                                >
                                                    Customer Name
                                                </h4>
                                                <div class="mb-6">
                                                    <svg
                                                        width="116"
                                                        height="21"
                                                        viewBox="0 0 116 21"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                    >
                                                        <path
                                                            d="M11.4417 3.36221L12.9083 6.29554C13.1083 6.70387 13.6417 7.09554 14.0917 7.17054L16.75 7.61221C18.45 7.89554 18.85 9.12887 17.625 10.3455L15.5583 12.4122C15.2083 12.7622 15.0167 13.4372 15.125 13.9205L15.7167 16.4789C16.1833 18.5039 15.1083 19.2872 13.3167 18.2289L10.825 16.7539C10.375 16.4872 9.63332 16.4872 9.17499 16.7539L6.68332 18.2289C4.89999 19.2872 3.81665 18.4955 4.28332 16.4789L4.87499 13.9205C4.98332 13.4372 4.79165 12.7622 4.44165 12.4122L2.37499 10.3455C1.15832 9.12887 1.54999 7.89554 3.24999 7.61221L5.90832 7.17054C6.34999 7.09554 6.88332 6.70387 7.08332 6.29554L8.54999 3.36221C9.34999 1.77054 10.65 1.77054 11.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M35.4417 3.36221L36.9083 6.29554C37.1083 6.70387 37.6417 7.09554 38.0917 7.17054L40.75 7.61221C42.45 7.89554 42.85 9.12887 41.625 10.3455L39.5583 12.4122C39.2083 12.7622 39.0167 13.4372 39.125 13.9205L39.7167 16.4789C40.1833 18.5039 39.1083 19.2872 37.3167 18.2289L34.825 16.7539C34.375 16.4872 33.6333 16.4872 33.175 16.7539L30.6833 18.2289C28.9 19.2872 27.8167 18.4955 28.2833 16.4789L28.875 13.9205C28.9833 13.4372 28.7917 12.7622 28.4417 12.4122L26.375 10.3455C25.1583 9.12887 25.55 7.89554 27.25 7.61221L29.9083 7.17054C30.35 7.09554 30.8833 6.70387 31.0833 6.29554L32.55 3.36221C33.35 1.77054 34.65 1.77054 35.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M59.4417 3.36221L60.9084 6.29554C61.1084 6.70387 61.6417 7.09554 62.0917 7.17054L64.75 7.61221C66.45 7.89554 66.85 9.12887 65.625 10.3455L63.5584 12.4122C63.2084 12.7622 63.0167 13.4372 63.125 13.9205L63.7167 16.4789C64.1834 18.5039 63.1084 19.2872 61.3167 18.2289L58.825 16.7539C58.375 16.4872 57.6334 16.4872 57.175 16.7539L54.6834 18.2289C52.9 19.2872 51.8167 18.4955 52.2834 16.4789L52.875 13.9205C52.9834 13.4372 52.7917 12.7622 52.4417 12.4122L50.375 10.3455C49.1584 9.12887 49.55 7.89554 51.25 7.61221L53.9084 7.17054C54.35 7.09554 54.8834 6.70387 55.0834 6.29554L56.55 3.36221C57.35 1.77054 58.65 1.77054 59.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M83.4417 3.36221L84.9084 6.29554C85.1084 6.70387 85.6417 7.09554 86.0917 7.17054L88.75 7.61221C90.45 7.89554 90.85 9.12887 89.625 10.3455L87.5584 12.4122C87.2084 12.7622 87.0167 13.4372 87.125 13.9205L87.7167 16.4789C88.1834 18.5039 87.1084 19.2872 85.3167 18.2289L82.825 16.7539C82.375 16.4872 81.6334 16.4872 81.175 16.7539L78.6834 18.2289C76.9 19.2872 75.8167 18.4955 76.2834 16.4789L76.875 13.9205C76.9834 13.4372 76.7917 12.7622 76.4417 12.4122L74.375 10.3455C73.1584 9.12887 73.55 7.89554 75.25 7.61221L77.9084 7.17054C78.35 7.09554 78.8834 6.70387 79.0834 6.29554L80.55 3.36221C81.35 1.77054 82.65 1.77054 83.4417 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                        <path
                                                            d="M107.442 3.36221L108.908 6.29554C109.108 6.70387 109.642 7.09554 110.092 7.17054L112.75 7.61221C114.45 7.89554 114.85 9.12887 113.625 10.3455L111.558 12.4122C111.208 12.7622 111.017 13.4372 111.125 13.9205L111.717 16.4789C112.183 18.5039 111.108 19.2872 109.317 18.2289L106.825 16.7539C106.375 16.4872 105.633 16.4872 105.175 16.7539L102.683 18.2289C100.9 19.2872 99.8167 18.4955 100.283 16.4789L100.875 13.9205C100.983 13.4372 100.792 12.7622 100.442 12.4122L98.375 10.3455C97.1584 9.12887 97.55 7.89554 99.25 7.61221L101.908 7.17054C102.35 7.09554 102.883 6.70387 103.083 6.29554L104.55 3.36221C105.35 1.77054 106.65 1.77054 107.442 3.36221Z"
                                                            fill="#FDC514"
                                                        />
                                                    </svg>
                                                </div>
                                                <p
                                                    class="text-lg leading-[1.4] text-[#2B2928]"
                                                >
                                                    Lorem ipsum dolor sit amet
                                                    consectetur. Facilisis
                                                    egestas amet vitae
                                                    scelerisque sit in cursus.
                                                    Aliquam dignissim euismod
                                                    tincidunt amet vitae amet.
                                                    Suspendisse feugiat in arcu
                                                    lorem sit tincidunt. Dui
                                                    nibh in in eu quis felis
                                                    velit amet aliquet. Nisl
                                                    facilisi in at non amet
                                                    ultrices mus. Facilisis
                                                    mauris gravida in sit sit
                                                    lorem fames faucibus. Lacus
                                                    feugiat cursus.
                                                </p>
                                                <button
                                                    class="bg-gradient2 px-6 h-11 rounded-full text-white font-bold hover:bg-gradient8 transition-colors duration-300 mt-5 lg:mt-8"
                                                >
                                                    <span
                                                        >Chia sẻ trải nghiệm của
                                                        bạn</span
                                                    >
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Pagination -->
                            <div
                                class="swiper-pagination unset mt-8 lg:mt-[40px]"
                            ></div>
                            <!-- Navigation buttons -->
                            <div
                                class="swiper-button-next bg-primary-background3 -right-6 shadow-new-shadow"
                            ></div>
                            <div
                                class="swiper-button-prev bg-primary-background3 -left-6 shadow-new-shadow"
                            ></div>
                        </div>
                    </div>
                </div>
                <div class="pt-12 lg:pt-0">
                    <div class="">
                        <div
                            class="flex flex-col lg:flex-row gap-[30px] items-center"
                        >
                            <div
                                class="flex-col w-full lg:max-w-[36.71875%] pl-4 pr-4 lg:pr-0"
                            >
                                <div class="ml-auto w-full lg:max-w-[448px]">
                                    <img
                                        src="assets/images/logo-small.png"
                                        alt="Icon 1"
                                        class="w-full h-auto max-w-[130px] aspect-[3.02325581] mb-6"
                                    />
                                    <h3
                                        class="text-xl font-bold bg-gradienttext bg-clip-text text-transparent mb-1"
                                    >
                                        WHERE SOUND MEETS VISION
                                    </h3>
                                    <p class="leading-[1.4] mb-6">
                                        Lorem ipsum dolor sit amet consectetur.
                                        Tempor scelerisque rhoncus aenean ut
                                        odio elementum sagittis facilisis. Et
                                        hac maecenas interdum eu.
                                    </p>
                                    <button
                                        class="bg-gradient2 px-6 w-full h-11 lg:h-[52px] text-sm leading-[1.4] rounded-lg text-white font-bold hover:bg-gradient8 transition-colors duration-300"
                                    >
                                        Hotline: 09885 99 2222
                                    </button>
                                </div>
                            </div>
                            <div class="flex-col w-full">
                                <div class="relative">
                                    <img
                                        src="assets/images/img-sound.jpg"
                                        alt="Icon 1"
                                        class="w-full h-auto aspect-[1.94581281] object-cover"
                                    />
                                    <div
                                        class="absolute bg-gradientwhite w-full max-w-[210px] h-full left-0 top-0"
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <a href="#">
                        <img
                            src="assets/images/banner-contact.jpg"
                            alt="Banner Contact"
                            class="w-full h-auto object-cover"
                        />
                    </a>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-footer-main text-white">
            <div class="container mx-auto">
                <div
                    class="flex flex-col md:flex-row justify-between items-start gap-6 md:gap-0 py-8 md:py-12 lg:py-16"
                >
                    <!-- Company Info -->
                    <div>
                        <div class="logo-footer mb-[30px]">
                            <img
                                src="assets/images/logo.png"
                                alt="AV Plus Logo"
                                class="w-60 h-auto"
                            />
                        </div>
                        <ul class="mb-[30px]">
                            <li class="font-medium uppercase mb-4">
                                CÔNG TY TNHH AV PLUS
                            </li>
                            <li class="mb-3 relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg
                                        width="20"
                                        height="21"
                                        viewBox="0 0 20 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M10.0004 12.3083C8.22539 12.3083 6.77539 10.8666 6.77539 9.0833C6.77539 7.29997 8.22539 5.86664 10.0004 5.86664C11.7754 5.86664 13.2254 7.30831 13.2254 9.09164C13.2254 10.875 11.7754 12.3083 10.0004 12.3083ZM10.0004 7.11664C8.91706 7.11664 8.02539 7.99997 8.02539 9.09164C8.02539 10.1833 8.90872 11.0666 10.0004 11.0666C11.0921 11.0666 11.9754 10.1833 11.9754 9.09164C11.9754 7.99997 11.0837 7.11664 10.0004 7.11664Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M9.99941 19.4667C8.76608 19.4667 7.52441 19 6.55775 18.075C4.09941 15.7084 1.38275 11.9334 2.40775 7.44169C3.33275 3.36669 6.89108 1.54169 9.99941 1.54169C9.99941 1.54169 9.99941 1.54169 10.0077 1.54169C13.1161 1.54169 16.6744 3.36669 17.5994 7.45002C18.6161 11.9417 15.8994 15.7084 13.4411 18.075C12.4744 19 11.2327 19.4667 9.99941 19.4667ZM9.99941 2.79169C7.57441 2.79169 4.45775 4.08335 3.63275 7.71669C2.73275 11.6417 5.19941 15.025 7.43275 17.1667C8.87441 18.5584 11.1327 18.5584 12.5744 17.1667C14.7994 15.025 17.2661 11.6417 16.3827 7.71669C15.5494 4.08335 12.4244 2.79169 9.99941 2.79169Z"
                                            fill="white"
                                        />
                                    </svg>
                                </span>
                                Địa chỉ: Trụ sở chính 88 Thái Hà, Hà Nội
                            </li>
                            <li class="mb-3 relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg
                                        width="20"
                                        height="21"
                                        viewBox="0 0 20 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M18.3327 19.4583H1.66602C1.32435 19.4583 1.04102 19.175 1.04102 18.8333C1.04102 18.4916 1.32435 18.2083 1.66602 18.2083H18.3327C18.6744 18.2083 18.9577 18.4916 18.9577 18.8333C18.9577 19.175 18.6744 19.4583 18.3327 19.4583Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M3.08398 18.8333H1.83398L1.87565 8.80832C1.87565 8.09998 2.20065 7.44167 2.75898 7.00834L8.59232 2.46666C9.41732 1.825 10.5757 1.825 11.409 2.46666L17.2423 7C17.7923 7.43333 18.1257 8.10832 18.1257 8.80832V18.8333H16.8757V8.81666C16.8757 8.49999 16.7257 8.19167 16.4757 7.99167L10.6423 3.45833C10.2673 3.16666 9.74232 3.16666 9.35899 3.45833L3.52566 8.00001C3.27566 8.19168 3.12565 8.49999 3.12565 8.81666L3.08398 18.8333Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M14.1673 19.4584H5.83398C5.49232 19.4584 5.20898 19.175 5.20898 18.8334V10.9167C5.20898 9.88335 6.05065 9.04169 7.08398 9.04169H12.9173C13.9507 9.04169 14.7923 9.88335 14.7923 10.9167V18.8334C14.7923 19.175 14.509 19.4584 14.1673 19.4584ZM6.45898 18.2084H13.5423V10.9167C13.5423 10.575 13.259 10.2917 12.9173 10.2917H7.08398C6.74232 10.2917 6.45898 10.575 6.45898 10.9167V18.2084Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M8.33398 15.9167C7.99232 15.9167 7.70898 15.6334 7.70898 15.2917V14.0417C7.70898 13.7 7.99232 13.4167 8.33398 13.4167C8.67565 13.4167 8.95898 13.7 8.95898 14.0417V15.2917C8.95898 15.6334 8.67565 15.9167 8.33398 15.9167Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M11.25 7.375H8.75C8.40833 7.375 8.125 7.09167 8.125 6.75C8.125 6.40833 8.40833 6.125 8.75 6.125H11.25C11.5917 6.125 11.875 6.40833 11.875 6.75C11.875 7.09167 11.5917 7.375 11.25 7.375Z"
                                            fill="white"
                                        />
                                    </svg>
                                </span>
                                VPGG: 123 Chùa Bộc, Hà Nội
                            </li>
                            <li class="relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg
                                        width="20"
                                        height="21"
                                        viewBox="0 0 20 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M14.166 18.2084H5.83268C2.79102 18.2084 1.04102 16.4584 1.04102 13.4167V7.58335C1.04102 4.54169 2.79102 2.79169 5.83268 2.79169H14.166C17.2077 2.79169 18.9577 4.54169 18.9577 7.58335V13.4167C18.9577 16.4584 17.2077 18.2084 14.166 18.2084ZM5.83268 4.04169C3.44935 4.04169 2.29102 5.20002 2.29102 7.58335V13.4167C2.29102 15.8 3.44935 16.9584 5.83268 16.9584H14.166C16.5493 16.9584 17.7077 15.8 17.7077 13.4167V7.58335C17.7077 5.20002 16.5493 4.04169 14.166 4.04169H5.83268Z"
                                            fill="white"
                                        />
                                        <path
                                            d="M9.999 11.225C9.299 11.225 8.59067 11.0083 8.049 10.5666L5.44067 8.48331C5.174 8.26664 5.124 7.87497 5.34067 7.60831C5.55734 7.34164 5.94901 7.29164 6.21567 7.50831L8.824 9.59164C9.45733 10.1 10.5323 10.1 11.1657 9.59164L13.774 7.50831C14.0407 7.29164 14.4407 7.33331 14.649 7.60831C14.8657 7.87497 14.824 8.27498 14.549 8.48331L11.9407 10.5666C11.4073 11.0083 10.699 11.225 9.999 11.225Z"
                                            fill="white"
                                        />
                                    </svg>
                                </span>
                                Mail: <EMAIL>
                            </li>
                        </ul>
                        <div class="logo-bct">
                            <img
                                src="assets/images/logo-bct.png"
                                alt="BCT Logo"
                                class="w-28 h-auto"
                            />
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div>
                        <h4 class="text-xl font-bold mb-4">
                            Hỗ trợ khách hàng
                        </h4>
                        <ul class="space-y-4 mb-4">
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Hướng dẫn mua hàng</a
                                >
                            </li>
                            <li>
                                <a
                                    href="#Hướng dẫn thanh toán"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Hướng dẫn thanh toán</a
                                >
                            </li>
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Đăng ký đại lý</a
                                >
                            </li>
                        </ul>
                        <div
                            class="h-12 px-3 bg-red-500 rounded-lg inline-flex justify-start items-center gap-2 pt-1 pb-1"
                        >
                            <div class="w-6 h-6 relative">
                                <div class="w-6 h-6 left-0 top-0 absolute">
                                    <svg
                                        width="24"
                                        height="24"
                                        viewBox="0 0 30 30"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M21.8125 28.4375C20.4 28.4375 18.9125 28.1 17.375 27.45C15.875 26.8125 14.3625 25.9375 12.8875 24.875C11.425 23.8 10.0125 22.6 8.675 21.2875C7.35 19.95 6.15 18.5375 5.0875 17.0875C4.0125 15.5875 3.15 14.0875 2.5375 12.6375C1.8875 11.0875 1.5625 9.5875 1.5625 8.175C1.5625 7.2 1.7375 6.275 2.075 5.4125C2.425 4.525 2.9875 3.7 3.75 2.9875C4.7125 2.0375 5.8125 1.5625 6.9875 1.5625C7.475 1.5625 7.975 1.675 8.4 1.875C8.8875 2.1 9.3 2.4375 9.6 2.8875L12.5 6.975C12.7625 7.3375 12.9625 7.6875 13.1 8.0375C13.2625 8.4125 13.35 8.7875 13.35 9.15C13.35 9.625 13.2125 10.0875 12.95 10.525C12.7625 10.8625 12.475 11.225 12.1125 11.5875L11.2625 12.475C11.275 12.5125 11.2875 12.5375 11.3 12.5625C11.45 12.825 11.75 13.275 12.325 13.95C12.9375 14.65 13.5125 15.2875 14.0875 15.875C14.825 16.6 15.4375 17.175 16.0125 17.65C16.725 18.25 17.1875 18.55 17.4625 18.6875L17.4375 18.75L18.35 17.85C18.7375 17.4625 19.1125 17.175 19.475 16.9875C20.1625 16.5625 21.0375 16.4875 21.9125 16.85C22.2375 16.9875 22.5875 17.175 22.9625 17.4375L27.1125 20.3875C27.575 20.7 27.9125 21.1 28.1125 21.575C28.3 22.05 28.3875 22.4875 28.3875 22.925C28.3875 23.525 28.25 24.125 27.9875 24.6875C27.725 25.25 27.4 25.7375 26.9875 26.1875C26.275 26.975 25.5 27.5375 24.6 27.9C23.7375 28.25 22.8 28.4375 21.8125 28.4375ZM6.9875 3.4375C6.3 3.4375 5.6625 3.7375 5.05 4.3375C4.475 4.875 4.075 5.4625 3.825 6.1C3.5625 6.75 3.4375 7.4375 3.4375 8.175C3.4375 9.3375 3.7125 10.6 4.2625 11.9C4.825 13.225 5.6125 14.6 6.6125 15.975C7.6125 17.35 8.75 18.6875 10 19.95C11.25 21.1875 12.6 22.3375 13.9875 23.35C15.3375 24.3375 16.725 25.1375 18.1 25.7125C20.2375 26.625 22.2375 26.8375 23.8875 26.15C24.525 25.8875 25.0875 25.4875 25.6 24.9125C25.8875 24.6 26.1125 24.2625 26.3 23.8625C26.45 23.55 26.525 23.225 26.525 22.9C26.525 22.7 26.4875 22.5 26.3875 22.275C26.35 22.2 26.275 22.0625 26.0375 21.9L21.8875 18.95C21.6375 18.775 21.4125 18.65 21.2 18.5625C20.925 18.45 20.8125 18.3375 20.3875 18.6C20.1375 18.725 19.9125 18.9125 19.6625 19.1625L18.7125 20.1C18.225 20.575 17.475 20.6875 16.9 20.475L16.5625 20.325C16.05 20.05 15.45 19.625 14.7875 19.0625C14.1875 18.55 13.5375 17.95 12.75 17.175C12.1375 16.55 11.525 15.8875 10.8875 15.15C10.3 14.4625 9.875 13.875 9.6125 13.3875L9.4625 13.0125C9.3875 12.725 9.3625 12.5625 9.3625 12.3875C9.3625 11.9375 9.525 11.5375 9.8375 11.225L10.775 10.25C11.025 10 11.2125 9.7625 11.3375 9.55C11.4375 9.3875 11.475 9.25 11.475 9.125C11.475 9.025 11.4375 8.875 11.375 8.725C11.2875 8.525 11.15 8.3 10.975 8.0625L8.075 3.9625C7.95 3.7875 7.8 3.6625 7.6125 3.575C7.4125 3.4875 7.2 3.4375 6.9875 3.4375ZM17.4375 18.7625L17.2375 19.6125L17.575 18.7375C17.5125 18.725 17.4625 18.7375 17.4375 18.7625Z"
                                            fill="white"
                                        />
                                    </svg>
                                </div>
                            </div>
                            <div
                                class="inline-flex flex-col justify-center items-start"
                            >
                                <div
                                    class="justify-start text-white text-xs font-normal font-['Neue_Einstellung'] leading-none mb-[2px]"
                                >
                                    Hotline hỗ trợ:
                                </div>
                                <div
                                    class="justify-start text-white text-xl font-bold font-['Neue_Einstellung'] leading-none"
                                >
                                    098888898
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div>
                        <h4 class="text-xl font-bold mb-4">Chính sách</h4>
                        <ul class="space-y-4 mb-4">
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Chính sách bảo hành</a
                                >
                            </li>
                            <li>
                                <a
                                    href="#Hướng dẫn thanh toán"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Chính sách bảo mật</a
                                >
                            </li>
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Chính sách mua hàng</a
                                >
                            </li>
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Chính sách vận chuyển</a
                                >
                            </li>
                            <li>
                                <a
                                    href="news-detail.html"
                                    class="text-footer-secondary font-medium hover:text-white transition-colors"
                                    >Chính sách đổi trả</a
                                >
                            </li>
                        </ul>
                        <div class="flex flex-row gap-5">
                            <a
                                href="news-detail.html"
                                target="_blank"
                                rel="nofollow"
                            >
                                <img
                                    src="assets/images/facebook-icon.png"
                                    alt="Facebook Icon"
                                    class="w-8 h-8"
                                />
                            </a>
                            <a
                                href="news-detail.html"
                                target="_blank"
                                rel="nofollow"
                            >
                                <img
                                    src="assets/images/instagram-icon.png"
                                    alt="Instagram Icon"
                                    class="w-8 h-8"
                                />
                            </a>
                            <a
                                href="news-detail.html"
                                target="_blank"
                                rel="nofollow"
                            >
                                <img
                                    src="assets/images/youtube-icon.png"
                                    alt="YouTube Icon"
                                    class="w-8 h-8"
                                />
                            </a>
                            <a
                                href="https://zalo.me/#"
                                rel="nofollow"
                                target="_blank"
                            >
                                <img
                                    src="assets/images/zalo-icon.png"
                                    alt="Zalo Icon"
                                    class="w-8 h-8"
                                />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="border-t border-secondary-border pt-6 pb-6">
                <div
                    class="container flex flex-col md:flex-row justify-between items-center"
                >
                    <div>© Copyright 2025 | Auvista</div>
                    <div class="text-footer-secondary">
                        Designed by Uni Creation Viet Nam
                    </div>
                </div>
            </div>
        </footer>

        <!-- Swiper JS -->
        <script src="assets/js/swiper-bundle.min.js"></script>

        <!-- Services Swiper Initialization -->
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                if (typeof Swiper === "undefined") {
                    console.error("Swiper is not loaded!");
                    return;
                }

                // Initialize Services Swiper
                const servicesSwiper = new Swiper(".services-swiper .swiper", {
                    slidesPerView: 1,
                    spaceBetween: 0,
                    loop: false, // Bây giờ có thể để true
                    // autoplay: {
                    //     delay: 4000,
                    //     disableOnInteraction: false,
                    // },
                    autoplay: false, // Tắt autoplay để dễ dàng kiểm tra
                    navigation: {
                        nextEl: ".services-swiper .swiper-button-next",
                        prevEl: ".services-swiper .swiper-button-prev",
                    },
                    breakpoints: {
                        0: {
                            slidesPerView: 2,
                            spaceBetween: 0,
                        },
                        550: {
                            slidesPerView: 2,
                            spaceBetween: 0,
                        },
                        768: {
                            slidesPerView: 2.5,
                            spaceBetween: 0,
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 0,
                        },
                        1280: {
                            slidesPerView: 4,
                            spaceBetween: 0,
                        },
                    },
                });

                let currentElevated = null;

                // Function to apply staggered effect (FIXED for loop mode)
                function applyStaggeredEffect() {
                    // Chỉ lấy slides gốc, bỏ qua duplicate slides
                    const allSlides = document.querySelectorAll(
                        ".services-swiper .swiper-slide:not(.swiper-slide-duplicate)"
                    );

                    if (allSlides.length === 0) {
                        console.log("No slides found!");
                        return;
                    }

                    // Reset all slides (bao gồm cả duplicate)
                    const allSlidesIncludingDuplicates =
                        document.querySelectorAll(
                            ".services-swiper .swiper-slide"
                        );

                    allSlidesIncludingDuplicates.forEach((slide) => {
                        slide.classList.remove(
                            "staggered-even",
                            "staggered-odd"
                        );
                        if (!slide.classList.contains("elevated")) {
                            slide.style.transform = "";
                        }
                    });

                    // Sử dụng realIndex thay vì activeIndex
                    const activeIndex = servicesSwiper.realIndex || 0;

                    // Get actual slidesPerView for current breakpoint
                    let currentSlidesPerView = 1;
                    if (window.innerWidth >= 1280) {
                        currentSlidesPerView = 4;
                    } else if (window.innerWidth >= 1024) {
                        currentSlidesPerView = 3;
                    } else if (window.innerWidth >= 550) {
                        currentSlidesPerView = 2;
                    }

                    // Apply staggered effect to visible slides
                    for (
                        let i = 0;
                        i < currentSlidesPerView && i < allSlides.length;
                        i++
                    ) {
                        const slideIndex = (activeIndex + i) % allSlides.length;
                        const slide = allSlides[slideIndex];

                        if (slide && !slide.classList.contains("elevated")) {
                            if (i % 2 === 1) {
                                slide.classList.add("staggered-even");
                                const offset =
                                    window.innerWidth < 768 ? "45px" : "90px";
                                slide.style.transform = `translateY(${offset})`;
                                slide.style.transition =
                                    "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
                            } else {
                                slide.classList.add("staggered-odd");
                                slide.style.transform = "translateY(0px)";
                                slide.style.transition =
                                    "transform 0.5s cubic-bezier(0.4, 0, 0.2, 1)";
                            }
                        }
                    }

                    console.log(
                        `Applied staggered effect to ${currentSlidesPerView} slides`
                    );
                }

                // Apply initial staggered effect (delay để đợi DOM render)
                setTimeout(() => {
                    applyStaggeredEffect();
                }, 500);

                // Update staggered effect when slides change
                servicesSwiper.on("slideChange", function () {
                    console.log("Slide changed, realIndex:", this.realIndex);

                    // Reset elevated states (chỉ slides gốc)
                    const serviceSlides = document.querySelectorAll(
                        ".services-swiper .swiper-slide:not(.swiper-slide-duplicate)"
                    );
                    serviceSlides.forEach((slide) => {
                        slide.classList.remove(
                            "elevated",
                            "slide-up-animation",
                            "slide-down-animation"
                        );
                    });
                    currentElevated = null;

                    // Reapply staggered effect
                    setTimeout(() => {
                        applyStaggeredEffect();
                    }, 150);
                });

                // Update on transition end
                servicesSwiper.on("transitionEnd", function () {
                    console.log("Transition ended");
                    setTimeout(() => {
                        applyStaggeredEffect();
                    }, 50);
                });

                // Reapply on window resize
                window.addEventListener("resize", function () {
                    setTimeout(() => {
                        applyStaggeredEffect();
                    }, 100);
                });

                // Add click animation functionality (FIXED for loop mode)
                // Sử dụng event delegation để handle cả duplicate slides
                document
                    .querySelector(".services-swiper")
                    .addEventListener("click", function (e) {
                        const clickedSlide = e.target.closest(".swiper-slide");
                        if (!clickedSlide) return;

                        console.log("Slide clicked");

                        // Remove elevated class from previously clicked slide
                        if (
                            currentElevated &&
                            currentElevated !== clickedSlide
                        ) {
                            currentElevated.classList.remove(
                                "elevated",
                                "slide-up-animation"
                            );
                        }

                        // Toggle elevation for clicked slide
                        if (clickedSlide.classList.contains("elevated")) {
                            clickedSlide.classList.remove(
                                "elevated",
                                "slide-up-animation"
                            );
                            currentElevated = null;

                            setTimeout(() => {
                                applyStaggeredEffect();
                            }, 50);
                        } else {
                            clickedSlide.classList.add("elevated");
                            const elevatedOffset =
                                window.innerWidth < 768 ? "-10px" : "-20px";
                            clickedSlide.style.transform = `translateY(${elevatedOffset})`;
                            clickedSlide.style.transition =
                                "transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)";
                            currentElevated = clickedSlide;
                        }
                    });

                console.log("Services swiper initialized with loop support");
            });
        </script>

        <!-- Fixed Contact Widget -->
        <div
            class="fixed top-1/2 right-0 transform -translate-y-1/2 z-50 space-y-3"
        >
            <!-- Hotline Button 1 -->
            <a href="tel:0979998877" class="block">
                <div
                    class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out"
                >
                    <div class="flex items-center gap-3">
                        <div
                            class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0"
                        >
                            <svg
                                class="w-5 h-5"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"
                                />
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                Hotline: 098888898
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Hotline Button 2 -->
            <a href="tel:0988888988" class="block">
                <div
                    class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out"
                >
                    <div class="flex items-center gap-3">
                        <div
                            class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0"
                        >
                            <svg
                                class="w-5 h-5"
                                fill="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z"
                                />
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                Hotline: 098888898
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Zalo Button -->
            <a
                href="https://zalo.me/0979998877"
                target="_blank"
                class="flex justify-end pr-4"
            >
                <img src="assets/images/icon-zalo.png" alt="zalo" />
            </a>

            <!-- Messenger Button -->
            <a
                href="https://m.me/avplus.vietnam"
                target="_blank"
                class="flex justify-end pr-4"
            >
                <img src="assets/images/icon-messenger.png" alt="messenger" />
            </a>

            <!-- Email Button -->
            <a
                href="mailto:<EMAIL>"
                class="flex justify-end pr-4"
            >
                <img src="assets/images/icon-email.png" alt="email" />
            </a>
        </div>
    </body>
    <!-- Main JS -->
    <script src="assets/js/main.js"></script>
</html>
