<?php

namespace App\View\Components;

use App\Helpers\TranslationHelper;
use Illuminate\View\Component;

class LanguageSwitcher extends Component
{
    public $currentPageId;
    public $type;

    /**
     * Create a new component instance.
     */
    public function __construct($currentPageId = null, $type = 'static_page')
    {
        $this->currentPageId = $currentPageId;
        $this->type = $type;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render()
    {
        $languageLinks = [];

        if ($this->currentPageId) {
            $languageLinks = TranslationHelper::getLanguageSwitcherLinks($this->currentPageId, $this->type);
        } else {
            // Fallback: try to get from session
            $currentCode = session('current_page_code');
            if ($currentCode) {
                $translations = TranslationHelper::getAllTranslations($currentCode, $this->type);
                foreach ($translations as $translation) {
                    $languageLinks[$translation->lang] = [
                        'url' => $translation->url,
                        'title' => $translation->page->title ?? '',
                        'lang' => $translation->lang,
                    ];
                }
            }
        }

        return view('components.language-switcher', [
            'languageLinks' => $languageLinks,
            'currentLang' => app()->getLocale(),
        ]);
    }
}
