<?php

namespace App\Observers;

use App\Models\LogAction;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;

class ModelLogObserver
{
    /**
     * Xử lý sự kiện khi model vừa được tạo.
     */
    public function created(Model $model): void
    {
        $this->logModelAction($model, 'created');
    }

    /**
     * Xử lý sự kiện khi model vừa được cập nhật.
     */
    public function updated(Model $model): void
    {
        $this->logModelAction($model, 'updated');
    }

    /**
     * Xử lý sự kiện TRƯỚC KHI model bị xóa.
     * Đ<PERSON>y là lý do tại sao chúng ta sử dụng deleting thay vì deleted
     */
    public function deleting(Model $model): void
    {
        $this->logModelAction($model, 'deleted');
    }

    /**
     * Ghi log cho các hành động trên model.
     */
    protected function logModelAction(Model $model, string $action): void
    {

        // Bỏ qua nếu model là LogAction để tránh đệ quy
        if ($model instanceof LogAction) {
            return;
        }

        // Ghi log debug để kiểm tra
        Log::debug('Observer catching model action', [
            'model' => get_class($model),
            'id' => $model->getKey(),
            'action' => $action
        ]);

        // Chuẩn bị dữ liệu
        $oldData = null;
        $newData = null;

        switch ($action) {
            case 'created':
                $newData = $model->getAttributes();
                break;

            case 'updated':
                $oldData = array_intersect_key($model->getOriginal(), $model->getChanges());
                $newData = $model->getChanges();
                
                // Bỏ qua nếu không có thay đổi thực sự
                if (empty($newData)) {
                    return;
                }
                break;

            case 'deleted':
                $oldData = $model->getAttributes();
                break;
        }

        try {
            // Tạo LogAction record
            LogAction::create([
                'user_id'    => Auth::id() ?: 1, // Default to 1 if no auth user
                'model_type' => get_class($model),
                'model_id'   => $model->getKey(),
                'action'     => $action,
                'old_data'   => $oldData,
                'new_data'   => $newData,
                'ip_address' => Request::ip(),
                'user_agent' => Request::userAgent(),
            ]);

            Log::info('Model log created successfully', [
                'model' => get_class($model),
                'id' => $model->getKey(),
                'action' => $action
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create model log', [
                'error' => $e->getMessage(),
                'stack' => $e->getTraceAsString(),
                'model' => get_class($model),
                'id' => $model->getKey(),
            ]);
        }
    }
} 