<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PermissionResource\Pages;
use Spatie\Permission\Models\Permission;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Enums\ActionsPosition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Filament\Notifications\Notification;

class PermissionResource extends BaseResource
{
    protected static ?string $model = Permission::class;
    protected static ?string $navigationIcon = 'heroicon-o-key';
    protected static ?string $navigationGroup = 'Quản lý tài khoản';
    protected static ?int $navigationSort = 3;
    protected static ?string $navigationLabel = 'Quyền hạn';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên quyền')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Hidden::make('guard_name')
                            ->default('web'),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3),
                        Forms\Components\Select::make('group')
                            ->label('Nhóm quyền')
                            ->options([
                                'admin' => 'Quản trị hệ thống',
                                'user' => 'Quản lý người dùng',
                                'content' => 'Quản lý nội dung',
                                'media' => 'Quản lý media',
                                'other' => 'Khác',
                            ])
                            ->required(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên quyền')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('group')
                    ->label('Nhóm')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'admin' => 'Quản trị hệ thống',
                        'user' => 'Quản lý người dùng',
                        'content' => 'Quản lý nội dung',
                        'media' => 'Quản lý media',
                        'other' => 'Khác',
                        default => $state,
                    })
                    ->colors([
                        'danger' => 'admin',
                        'warning' => 'user',
                        'success' => 'content',
                        'primary' => 'media',
                        'gray' => 'other',
                    ])
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('roles_count')
                    ->label('Vai trò sử dụng')
                    ->counts('roles')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime(Config::get('app.displayFormat', 'd/m/Y H:i'))
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('group')
                    ->label('Nhóm quyền')
                    ->options([
                        'admin' => 'Quản trị hệ thống',
                        'user' => 'Quản lý người dùng',
                        'content' => 'Quản lý nội dung',
                        'media' => 'Quản lý media',
                        'other' => 'Khác',
                    ]),
            ])
            ->actions([
                ViewAction::make()->modalWidth('md'),
                EditAction::make()->modalWidth('md'),
                DeleteAction::make()
                    ->hidden(fn (?Model $record): bool => 
                        $record && in_array($record->name, [
                            'admin', 
                            'super-admin',
                            'manage-users',
                            'manage-roles',
                        ])
                    )
                    ->before(function (?Model $record) {
                        if ($record && in_array($record->name, [
                            'admin', 
                            'super-admin',
                            'manage-users',
                            'manage-roles',
                        ])) {
                            Notification::make()
                                ->warning()
                                ->title('Quyền hệ thống')
                                ->body('Không thể xóa quyền hệ thống.')
                                ->send();
                            
                            return false;
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (Collection $records) {
                            $systemPermissions = $records->filter(fn ($record) => 
                                in_array($record->name, [
                                    'admin', 
                                    'super-admin',
                                    'manage-users',
                                    'manage-roles',
                                ])
                            );
                            
                            if ($systemPermissions->count()) {
                                Notification::make()
                                    ->warning()
                                    ->title('Quyền hệ thống')
                                    ->body('Không thể xóa quyền hệ thống.')
                                    ->send();
                                
                                return $records->filter(fn ($record) => 
                                    !in_array($record->name, [
                                        'admin', 
                                        'super-admin',
                                        'manage-users',
                                        'manage-roles',
                                    ])
                                );
                            }
                        }),
                ]),
            ])
            ->headerActions([
                CreateAction::make()->modalWidth('md'),
            ])
            ->recordAction(null);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPermissions::route('/')
        ];
    }
}
