<?php

use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Log;

if (!function_exists('formatPageTitle')) {
    /**
     * Format page title automatically
     * 
     * @param string|null $title
     * @param bool $isHomePage
     * @return string
     */
    function formatPageTitle($title = null, $isHomePage = false)
    {
        $siteTitle = getSetting('site_title', 'Midsun Vietnam - Sống khỏe - Sống đẹp - Sống hạnh phúc');
        $siteName = getSetting('site_name', 'Midsun');
        
        // Nếu là trang chủ hoặc không có title
        if ($isHomePage || empty($title)) {
            return $siteTitle;
        }
        
        // Loại bỏ các suffix đã có sẵn để tránh duplicate
        $title = preg_replace('/\s*-\s*(Midsun|Midsun Việt Nam|auvista).*$/i', '', $title);
        $title = trim($title);
        
        // Các trang con: "Tên trang - Midsun"
        return $title . ' - ' . $siteName;
    }
}

if (!function_exists('getPageTitle')) {
    /**
     * Get formatted page title for current request
     * 
     * @return string
     */
    function getPageTitle()
    {
        $isHomePage = request()->routeIs('home');
        
        // Lấy title từ view hoặc route
        $title = null;
        
        if (View::hasSection('title')) {
            $title = trim(View::yieldContent('title'));
        }
        
        return formatPageTitle($title, $isHomePage);
    }
}

if (!function_exists('autoDetectPageTitle')) {
    /**
     * Auto detect page title based on route and model
     * 
     * @return string
     */
    function autoDetectPageTitle()
    {
        $route = request()->route();
        
        if (!$route) {
            return formatPageTitle();
        }
        
        $routeName = $route->getName();
        $isHomePage = $routeName === 'home';
        
        // Nếu là trang chủ
        if ($isHomePage) {
            return formatPageTitle(null, true);
        }
        
        // Lấy title từ view section trước
        if (View::hasSection('title')) {
            $title = trim(View::yieldContent('title'));
            if (!empty($title)) {
                return formatPageTitle($title);
            }
        }
        
        // Auto detect từ route và parameters
        $title = detectTitleFromRoute($routeName, $route->parameters());
        
        return formatPageTitle($title);
    }
}

if (!function_exists('detectTitleFromRoute')) {
    /**
     * Detect title from route name and parameters
     * 
     * @param string $routeName
     * @param array $parameters
     * @return string|null
     */
    function detectTitleFromRoute($routeName, $parameters = [])
    {
        // Map route names to titles
        $routeTitles = [
            'login' => 'Đăng Nhập',
            'register' => 'Đăng Ký',
            'register.customer' => 'Đăng Ký Khách Hàng',
            'register.distributor' => 'Đăng Ký Nhà Phân Phối',
            'password.request' => 'Đặt Lại Mật Khẩu',
            'password.reset' => 'Đặt Lại Mật Khẩu',
            'verification.notice' => 'Xác thực email',
            'page.about' => 'Giới thiệu',
            'page.contact' => 'Liên hệ',
            'static.page' => 'Liên hệ',
            'products.index' => 'Sản phẩm',
            'products.search' => 'Tìm kiếm sản phẩm',
            'category.products' => 'Danh mục sản phẩm',
            'checkout' => 'Thanh toán',
            'wishlist' => 'Sản phẩm yêu thích',
            'pages.tin-tuc' => 'Tin tức',
        ];
        
        // Kiểm tra trong map trước
        if (isset($routeTitles[$routeName])) {
            return $routeTitles[$routeName];
        }
        
        // Xử lý các route dynamic
        try {
            switch ($routeName) {
                case 'products.show':
                    if (isset($parameters['slug'])) {
                        $product = \App\Models\Product::where('slug', $parameters['slug'])->first();
                        return $product ? $product->name : 'Chi tiết sản phẩm';
                    }
                    return 'Chi tiết sản phẩm';
                    
                case 'products.brand':
                    if (isset($parameters['slug'])) {
                        $brand = \App\Models\Brand::where('slug', $parameters['slug'])->first();
                        return $brand ? $brand->name : 'Thương hiệu';
                    }
                    return 'Thương hiệu';
                    
                case 'post.detail':
                    if (isset($parameters['slug'])) {
                        $post = \App\Models\Post::where('slug', $parameters['slug'])->first();
                        return $post ? $post->title : 'Chi tiết bài viết';
                    }
                    return 'Chi tiết bài viết';
                    
                case 'category.posts':
                    if (isset($parameters['slug'])) {
                        $category = \App\Models\Category::where('slug', $parameters['slug'])->first();
                        return $category ? $category->name : 'Danh mục bài viết';
                    }
                    return 'Danh mục bài viết';
                    
                default:
                    return null;
            }
        } catch (\Exception $e) {
            Log::warning('Error detecting title from route: ' . $e->getMessage());
            return null;
        }
    }
} 