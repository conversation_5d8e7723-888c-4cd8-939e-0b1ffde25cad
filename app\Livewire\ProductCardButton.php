<?php

namespace App\Livewire;

use App\Models\Product;
use Livewire\Component;
use Illuminate\Support\Facades\Session;

class ProductCardButton extends Component
{
    public $productId;
    public $productStock;

    public function mount($productId, $productStock)
    {
        $this->productId = $productId;
        $this->productStock = $productStock;
    }

    public function addToCart()
    {
        $cart = Session::get('cart', []);
        $quantityToAdd = 1;

        if (isset($cart[$this->productId])) {
            $newQuantity = $cart[$this->productId]['quantity'] + $quantityToAdd;
            if ($newQuantity > $this->productStock) {
                $this->dispatch('show-notification', [
                    'type' => 'error',
                    'message' => 'Số lượng sản phẩm trong giỏ hàng vượt quá số lượng tồn kho.',
                ]);
                return;
            }
            $cart[$this->productId]['quantity'] = $newQuantity;
        } else {
            if ($quantityToAdd > $this->productStock) {
                 $this->dispatch('show-notification', [
                    'type' => 'error',
                    'message' => 'Số lượng yêu cầu vượt quá số lượng tồn kho.',
                ]);
                return;
            }
            $cart[$this->productId] = ['quantity' => $quantityToAdd];
        }

        Session::put('cart', $cart);
        $this->dispatch('cartUpdated');
        $this->dispatch('show-notification', [
            'type' => 'success',
            'message' => 'Sản phẩm đã được thêm vào giỏ hàng!',
        ]);
    }

    public function render()
    {
        return view('livewire.product-card-button');
    }
} 