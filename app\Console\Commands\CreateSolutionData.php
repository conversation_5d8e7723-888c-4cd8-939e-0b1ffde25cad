<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;
use App\Models\Post;
use App\Models\User;

class CreateSolutionData extends Command
{
    protected $signature = 'data:create-solutions';
    protected $description = 'Tạo dữ liệu categories và posts cho solutions';

    public function handle()
    {
        $this->info('🚀 Bắt đầu tạo dữ liệu solutions...');

        // Bước 1: Tạo categories
        $this->createCategories();
        
        // Bước 2: Tạo posts
        $this->createPosts();
        
        $this->info('✅ Hoàn thành tạo dữ liệu solutions!');
    }

    private function createCategories()
    {
        $this->info('📂 Tạo categories...');
        
        $categories = [
            ['name' => 'Hội nghị truyền hình', 'slug' => 'hoi-nghi-truyen-hinh'],
            ['name' => 'Âm thanh hội thảo', 'slug' => 'am-thanh-hoi-thao'], 
            ['name' => 'Âm thanh ánh sáng biểu diễn', 'slug' => 'am-thanh-anh-sang-bieu-dien'],
            ['name' => 'Phòng học thông minh', 'slug' => 'phong-hoc-thong-minh'],
            ['name' => 'Âm nhạc nền đa vùng', 'slug' => 'am-nhac-nen-da-vung'],
            ['name' => 'Âm thanh thông báo', 'slug' => 'am-thanh-thong-bao'],
            ['name' => 'Hệ thống karaoke', 'slug' => 'he-thong-karaoke'],
            ['name' => 'Hệ thống phòng chiếu phim', 'slug' => 'he-thong-phong-chieu-phim']
        ];

        foreach ($categories as $cat) {
            try {
                Category::updateOrCreate(
                    ['slug' => $cat['slug'], 'type' => 'solution', 'lang' => 'vi'],
                    [
                        'name' => $cat['name'],
                        'slug' => $cat['slug'],
                        'type' => 'solution',
                        'status' => 'active',
                        'description' => 'Giải pháp ' . $cat['name'],
                        'lang' => 'vi'
                    ]
                );
                $this->line("   ✓ {$cat['name']}");
            } catch (\Exception $e) {
                $this->error("   ❌ Lỗi tạo {$cat['name']}: " . $e->getMessage());
            }
        }
        
        $this->info("📂 Đã tạo " . count($categories) . " categories");
    }

    private function createPosts()
    {
        $this->info('📝 Tạo posts...');
        
        // Lấy user để gán làm tác giả
        $author = User::first();
        if (!$author) {
            $this->error('❌ Không tìm thấy user nào. Vui lòng tạo user trước.');
            return;
        }

        $solutions = [
            [
                'title' => 'Hội nghị truyền hình',
                'slug' => 'hoi-nghi-truyen-hinh',
                'category_slug' => 'hoi-nghi-truyen-hinh',
                'excerpt' => 'Giải pháp hội nghị truyền hình chuyên nghiệp với chất lượng 4K Ultra HD, phục vụ phòng họp 6-20 người.',
                'content' => '<p>Giải pháp hội nghị truyền hình chuyên nghiệp với chất lượng 4K Ultra HD, âm thanh Hi-Fi và tính năng chia sẻ màn hình linh hoạt.</p>
                             <p>Hệ thống được thiết kế để phục vụ các phòng họp từ 6-20 người, tích hợp camera PTZ với zoom quang học 12x, microphone phủ sóng 360 độ và loa stereo chất lượng cao.</p>
                             <h3>Tính năng nổi bật:</h3>
                             <ul>
                                <li>Camera 4K UHD với zoom quang học 12x</li>
                                <li>Microphone phủ sóng 360 độ, khử tiếng ồn thông minh</li>
                                <li>Loa stereo chất lượng Hi-Fi</li>
                                <li>Hỗ trợ kết nối đa nền tảng: Zoom, Teams, Skype</li>
                                <li>Điều khiển từ xa và ứng dụng di động</li>
                             </ul>',
                'image' => 'solution-1.jpg'
            ],
            [
                'title' => 'Âm thanh hội thảo',
                'slug' => 'am-thanh-hoi-thao',
                'category_slug' => 'am-thanh-hoi-thao',
                'excerpt' => 'Hệ thống âm thanh hội thảo chuyên nghiệp cho sự kiện 50-500 người với chất lượng âm thanh vượt trội.',
                'content' => '<p>Hệ thống âm thanh hội thảo chuyên nghiệp được thiết kế để phục vụ các sự kiện lớn với số lượng người tham dự từ 50-500 người.</p>
                             <p>Giải pháp tích hợp micro không dây, loa line array và bộ xử lý âm thanh số để đảm bảo chất lượng âm thanh rõ ràng, đồng đều trong toàn bộ không gian sự kiện.</p>
                             <h3>Đặc điểm vượt trội:</h3>
                             <ul>
                                <li>Micro không dây chống nhiễu, tần số UHF</li>
                                <li>Loa line array phủ sóng đều khắp hội trường</li>
                                <li>Bộ xử lý âm thanh số với DSP chuyên nghiệp</li>
                                <li>Hệ thống feedback eliminator tự động</li>
                                <li>Mixer digital 32 kênh</li>
                             </ul>',
                'image' => 'solution-2.jpg'
            ],
            [
                'title' => 'Âm thanh, ánh sáng biểu diễn',
                'slug' => 'am-thanh-anh-sang-bieu-dien',
                'category_slug' => 'am-thanh-anh-sang-bieu-dien',
                'excerpt' => 'Giải pháp âm thanh và ánh sáng biểu diễn chuyên nghiệp cho concert, show diễn và sân khấu.',
                'content' => '<p>Giải pháp âm thanh và ánh sáng biểu diễn chuyên nghiệp dành cho các sự kiện giải trí, concert, show diễn và sân khấu.</p>
                             <p>Hệ thống kết hợp âm thanh công suất lớn với hiệu ứng ánh sáng đa dạng, tạo nên trải nghiệm thị giác và thính giác hoàn hảo cho khán giả.</p>
                             <h3>Thiết bị hiện đại:</h3>
                             <ul>
                                <li>Loa công suất lớn dải tần rộng</li>
                                <li>Đèn LED moving head chuyên nghiệp</li>
                                <li>Bàn mixer ánh sáng DMX512</li>
                                <li>Máy khói, máy tạo bọt chuyên dụng</li>
                                <li>Laser show và hiệu ứng đặc biệt</li>
                             </ul>',
                'image' => 'solution-3.jpg'
            ],
            [
                'title' => 'Phòng học thông minh',
                'slug' => 'phong-hoc-thong-minh',
                'category_slug' => 'phong-hoc-thong-minh',
                'excerpt' => 'Giải pháp phòng học thông minh với bảng tương tác và hệ thống âm thanh hiện đại.',
                'content' => '<p>Giải pháp phòng học thông minh tích hợp công nghệ hiện đại để nâng cao chất lượng giảng dạy và học tập.</p>
                             <p>Hệ thống bao gồm bảng tương tác thông minh, máy chiếu laser, hệ thống âm thanh và phần mềm quản lý lớp học.</p>
                             <h3>Tính năng đặc biệt:</h3>
                             <ul>
                                <li>Bảng tương tác multi-touch 75-86 inch</li>
                                <li>Máy chiếu laser 4K độ sáng cao</li>
                                <li>Hệ thống âm thanh phòng học</li>
                                <li>Camera ghi hình bài giảng</li>
                                <li>Phần mềm quản lý và chia sẻ nội dung</li>
                             </ul>',
                'image' => 'solution-4.jpg'
            ]
        ];

        foreach ($solutions as $solution) {
            // Tìm category
            $category = Category::where('slug', $solution['category_slug'])
                               ->where('type', 'solution')
                               ->first();
            
            if (!$category) {
                $this->error("❌ Không tìm thấy category: {$solution['category_slug']}");
                continue;
            }

            try {
                // Tạo post
                $post = Post::updateOrCreate(
                    ['slug' => $solution['slug'], 'lang' => 'vi'],
                    [
                        'title' => $solution['title'],
                        'slug' => $solution['slug'],
                        'content' => $solution['content'],
                        'excerpt' => $solution['excerpt'],
                        'status' => 'published',
                        'author_id' => $author->id,
                        'image' => $solution['image'],
                        'published_at' => now(),
                        'seo_title' => $solution['title'] . ' - Giải pháp công nghệ Auvista',
                        'seo_description' => $solution['excerpt'],
                        'seo_keywords' => str_replace([',', ' '], '', strtolower($solution['title'])) . ', giải pháp âm thanh, Auvista',
                        'lang' => 'vi'
                    ]
                );

                // Gán category cho post
                $post->categories()->syncWithoutDetaching([
                    $category->id => ['is_primary' => true]
                ]);

                $this->line("   ✓ {$solution['title']}");
            } catch (\Exception $e) {
                $this->error("   ❌ Lỗi tạo post {$solution['title']}: " . $e->getMessage());
            }
        }

        $this->info("📝 Đã tạo " . count($solutions) . " posts");
    }
}
