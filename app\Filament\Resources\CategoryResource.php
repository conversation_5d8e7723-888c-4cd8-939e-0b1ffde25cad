<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Pages;
use App\Filament\Resources\CategoryResource\RelationManagers;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use App\Filament\Resources\BaseResource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Filament\Forms\Set;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Enums\ActionsPosition;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Collection;
use App\Traits\HasLanguageSync;

class CategoryResource extends BaseResource
{
    use HasLanguageSync;

    protected static ?string $model = Category::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Quản lý nội dung';
    protected static ?int $navigationGroupSort = 2; // Changed from 1 to 2
    protected static ?string $navigationLabel = 'Danh mục bài viết';
    protected static ?int $navigationSort = 1;
    protected static string $defaultType = 'post';
    protected static ?string $slug = 'categories/post'; // Add specific slug

    public static function getNavigationUrl(): string
    {
        return static::getUrl('index');
    }

    public static function isNavigationActive(): bool
    {
        $currentUrl = request()->url();

        // Chỉ active khi URL chứa /categories/post
        return str_contains($currentUrl, '/categories/post');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên')
                            ->required()
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn (Set $set, ?string $state) => 
                                $set('slug', Str::slug($state))),
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->unique(ignoreRecord: true),
                        Forms\Components\Select::make('parent_id')
                            ->label('Danh mục cha')
                            ->relationship('parent', 'name', function (Builder $query) {
                                $query = $query->where('type', static::$defaultType);
                                
                                // Loại trừ danh mục hiện tại nếu đang edit
                                if (request()->route('record')) {
                                    $currentId = request()->route('record');
                                    $query->where('id', '!=', $currentId);
                                    
                                    // Loại trừ tất cả con của danh mục hiện tại
                                    $category = \App\Models\Category::find($currentId);
                                    if ($category) {
                                        $childrenIds = $category->getAllChildren()->pluck('id')->toArray();
                                        if (!empty($childrenIds)) {
                                            $query->whereNotIn('id', $childrenIds);
                                        }
                                    }
                                }
                                
                                return $query;
                            })
                            ->searchable()
                            ->preload()
                            ->nullable()
                            ->placeholder('Chọn danh mục cha (tùy chọn)')
                            ->helperText('Để trống nếu đây là danh mục gốc')
                            ->rules([
                                'nullable',
                                'exists:categories,id',
                            ])
                            ->afterStateUpdated(function ($state, $set, $get, $record) {
                                // Kiểm tra không chọn chính mình làm parent
                                if ($state && $record && $state == $record->id) {
                                    $set('parent_id', null);
                                    Notification::make()
                                        ->warning()
                                        ->title('Cảnh báo')
                                        ->body('Danh mục không thể chọn chính nó làm danh mục cha.')
                                        ->send();
                                }
                            }),
                        Forms\Components\Select::make('type')
                            ->label('Loại')
                            ->options([
                                'post' => 'Bài viết',
                                'product' => 'Sản phẩm',
                            ])
                            ->default(static::$defaultType)
                            ->disabled()
                            ->dehydrated(),
                        Forms\Components\Select::make('status')
                            ->label('Trạng thái')
                            ->options([
                                'active' => 'Hoạt động',
                                'inactive' => 'Không hoạt động',
                            ])
                            ->default('active')
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3),
                        Forms\Components\FileUpload::make('thumbnail')
                            ->label('Ảnh đại diện')
                            ->image()
                            ->directory('categories')
                            ->preserveFilenames()
                            ->imageEditor()
                            ->columnSpanFull(),
                    ])->columnSpan(['lg' => 2]),

                Forms\Components\Section::make('Thông tin SEO')
                    ->schema([
                        Forms\Components\TextInput::make('seo_title')
                            ->label('Tiêu đề SEO'),
                        Forms\Components\Textarea::make('seo_description')
                            ->label('Mô tả SEO')
                            ->rows(3),
                        Forms\Components\TextInput::make('seo_keywords')
                            ->label('Từ khóa SEO'),
                    ])->columnSpan(['lg' => 1]),
            ])->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->where('type', static::$defaultType))
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->disk('public')
                    ->square()
                    ->size(60)
                    ->visibility('public')
                    ->getStateUsing(function ($record) {
                        if ($record->image && Storage::disk('public')->exists($record->image)) {
                            return $record->image;
                        }
                        return null;
                    })
                    ->defaultImageUrl(asset(Config::get('filament-media.defaults.placeholder_image.url'))),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->formatStateUsing(function ($record) {
                        $prefix = '';
                        $level = 0;
                        $parent = $record->parent;
                        
                        while ($parent) {
                            $level++;
                            $parent = $parent->parent;
                        }
                    
                        if ($level > 0) {
                            $prefix = str_repeat('|-----', $level);
                        }
                        
                        return $prefix . $record->name;
                    })
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('Danh mục cha')
                    ->searchable()
                    ->placeholder('Danh mục gốc')
                    ->formatStateUsing(fn ($state) => $state ?: 'Danh mục gốc'),
                Tables\Columns\IconColumn::make('status')
                    ->label('Trạng thái')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->getStateUsing(fn ($record) => $record->status === 'active'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime(Config::get('app.displayFormat', 'd/m/Y H:i'))
                    ->sortable(),
            ])
            ->modifyQueryUsing(function ($query) {
                $currentLocale = \App\Services\LanguageSyncService::getCurrentLocale();
                return $query->where('lang', $currentLocale);
            })
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                    ]),
            ])
            ->actions([
                ViewAction::make()->modalWidth('7xl'),
                EditAction::make()->modalWidth('7xl'),
                DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('copy_to_language')
                        ->label('Copy sang ngôn ngữ khác...')
                        ->icon('heroicon-o-language')
                        ->action(function (Collection $records, array $data): void {
                            foreach ($records as $record) {
                                $newRecord = $record->replicate();
                                $newRecord->lang = $data['target_lang'];
                                $newRecord->slug = $newRecord->slug . '-' . $data['target_lang'];
                                $newRecord->save();
                            }

                            Notification::make()
                                ->title('Đã copy ' . count($records) . ' bản ghi')
                                ->success()
                                ->send();
                        })
                        ->form([
                            Forms\Components\Select::make('target_lang')
                                ->label('Chọn ngôn ngữ đích')
                                ->options([
                                    'vi' => 'Tiếng Việt',
                                    'en' => 'Tiếng Anh',
                                ])
                                ->required(),
                        ]),
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->recordAction(null);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
            'view' => Pages\ViewCategory::route('/{record}'),
        ];
    }
}
