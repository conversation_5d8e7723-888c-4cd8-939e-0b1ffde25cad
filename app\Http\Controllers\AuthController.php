<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Account;

class AuthController extends Controller
{
    public function loginForm()
    {
        return view('templates.auvista.auth.login');
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        if (Auth::guard('frontend')->attempt($credentials)) {
            $request->session()->regenerate();
            return redirect()->intended('/');
        }

        return back()->withErrors([
            'email' => 'Thông tin đăng nhập không chính xác.',
        ])->onlyInput('email');
    }

    public function registerLanding()
    {
        return view('templates.auvista.auth.register-landing');
    }

    public function registerForm()
    {
        // Chuyển hướng đến trang landing đăng ký mới
        return redirect()->route('register.landing');
    }

    public function registerDistributorForm()
    {
        return view('templates.auvista.auth.register-distributor');
    }

    public function registerCustomerForm()
    {
        return view('templates.auvista.auth.register-customer');
    }

    public function registerDistributor(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:accounts'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone' => ['required', 'string', 'max:15'],
            'id_number' => ['required', 'string', 'max:20'],
            'address' => ['required', 'string', 'max:255'],
            'bank_name' => ['required', 'string', 'max:100'],
            'bank_account' => ['required', 'string', 'max:50'],
            'bank_branch' => ['required', 'string', 'max:100'],
            'terms' => ['required', 'accepted'],
        ]);

        $account = Account::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'phone' => $validated['phone'],
            'status' => 'active',
            'account_type' => 'distributor',
            'address' => $validated['address'],
            'id_number' => $validated['id_number'],
            'bank_name' => $validated['bank_name'],
            'bank_account' => $validated['bank_account'],
            'bank_branch' => $validated['bank_branch'],
        ]);

        // Đăng nhập người dùng
        Auth::guard('frontend')->login($account);

        // Chuyển hướng đến trang xác thực email
        return redirect()->route('verification.notice');
    }

    public function registerCustomer(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:accounts'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone' => ['required', 'string', 'max:15'],
            'address' => ['nullable', 'string', 'max:255'],
            'distributor_code' => ['nullable', 'string', 'max:50'],
            'terms' => ['required', 'accepted'],
        ]);

        $account = Account::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'phone' => $validated['phone'],
            'address' => $validated['address'] ?? null,
            'status' => 'active',
            'account_type' => 'regular',
            'distributor_code' => $validated['distributor_code'] ?? null,
            'newsletter' => $request->has('newsletter') ? 1 : 0,
        ]);

        // Đăng nhập người dùng
        Auth::guard('frontend')->login($account);

        // Chuyển hướng đến trang xác thực email
        return redirect()->route('verification.notice');
    }

    public function register(Request $request)
    {
        // Chuyển hướng dựa trên account_type nếu được gửi từ form
        if ($request->has('account_type')) {
            if ($request->account_type === 'distributor') {
                return $this->registerDistributor($request);
            } else {
                return $this->registerCustomer($request);
            }
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:accounts'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone' => ['nullable', 'string', 'max:15'],
        ]);

        $account = Account::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'phone' => $validated['phone'] ?? null,
            'status' => 'active',
            'account_type' => 'regular', // Mặc định là tài khoản thường
        ]);

        // Đăng nhập người dùng
        Auth::guard('frontend')->login($account);

        // Chuyển hướng đến trang xác thực email
        return redirect()->route('verification.notice');
    }

    public function logout(Request $request)
    {
        Auth::guard('frontend')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect('/');
    }
} 