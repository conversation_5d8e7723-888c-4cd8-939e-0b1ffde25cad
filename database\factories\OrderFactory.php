<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class OrderFactory extends Factory
{
    protected $model = Order::class;

    public function definition(): array
    {
        $statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        $paymentMethods = ['cod', 'bank_transfer', 'vnpay', 'momo', 'zalopay'];
        
        // Chọn trạng thái ngẫu nhiên
        $status = $this->faker->randomElement($statuses);
        
        // Tạo ngày tương ứng với trạng thái
        $createdAt = $this->faker->dateTimeBetween('-3 months', 'now');
        $deliveredAt = null;
        $cancelledAt = null;
        
        if ($status === 'delivered') {
            $deliveredAt = $this->faker->dateTimeBetween($createdAt, 'now');
        }
        
        if ($status === 'cancelled') {
            $cancelledAt = $this->faker->dateTimeBetween($createdAt, 'now');
        }

        // Tạo giá trị giá tiền ngẫu nhiên
        $totalAmount = $this->faker->numberBetween(100000, ********);
        $taxRate = 0.1; // 10%
        $tax = round($totalAmount * $taxRate);
        $shippingCost = $this->faker->numberBetween(20000, 100000);
        $discount = $this->faker->numberBetween(0, 500000);
        
        // Tạo BV ngẫu nhiên (giả sử là tỉ lệ của tổng tiền)
        $bvRate = $this->faker->randomFloat(2, 0.03, 0.15);
        $bvTotal = round($totalAmount * $bvRate);

        return [
            'user_id' => User::factory(),
            'order_number' => 'ORD-' . strtoupper(Str::random(8)),
            'total_amount' => $totalAmount,
            'tax' => $tax,
            'shipping_cost' => $shippingCost,
            'discount' => $discount,
            'bv_total' => $bvTotal,
            'status' => $status,
            'payment_status' => $this->faker->randomElement($paymentStatuses),
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'transaction_id' => $this->faker->randomElement([null, 'TXN' . $this->faker->randomNumber(8, true)]),
            'shipping_method' => $this->faker->randomElement(['standard', 'express', 'same_day']),
            'shipping_fullname' => $this->faker->name(),
            'shipping_address' => $this->faker->streetAddress(),
            'shipping_city' => $this->faker->city(),
            'shipping_state' => $this->faker->randomElement(['Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ']),
            'shipping_postcode' => $this->faker->postcode(),
            'shipping_country' => 'Vietnam',
            'shipping_phone' => $this->faker->phoneNumber(),
            'shipping_email' => $this->faker->email(),
            'billing_fullname' => $this->faker->name(),
            'billing_address' => $this->faker->streetAddress(),
            'billing_city' => $this->faker->city(),
            'billing_state' => $this->faker->randomElement(['Hà Nội', 'Hồ Chí Minh', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ']),
            'billing_postcode' => $this->faker->postcode(),
            'billing_country' => 'Vietnam',
            'billing_phone' => $this->faker->phoneNumber(),
            'billing_email' => $this->faker->email(),
            'note' => $this->faker->optional(0.3)->paragraph(),
            'admin_note' => $this->faker->optional(0.2)->paragraph(),
            'created_at' => $createdAt,
            'updated_at' => $createdAt,
            'delivered_at' => $deliveredAt,
            'cancelled_at' => $cancelledAt,
        ];
    }
} 