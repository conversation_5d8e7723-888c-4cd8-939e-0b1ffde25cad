/* Font Imports */

@font-face {
    font-family: "Neue Einstellung";

    src: url("../fonts/NeueEinstellung-Regular.otf") format("opentype");

    font-weight: 400;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../fonts/NeueEinstellung-Light.otf") format("opentype");

    font-weight: 300;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../fonts/NeueEinstellung-Medium.otf") format("opentype");

    font-weight: 500;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../fonts/NeueEinstellung-SemiBold.otf") format("opentype");

    font-weight: 600;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../../assets/fonts/NeueEinstellung-Bold.otf") format("opentype");

    font-weight: 700;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../../assets/fonts/NeueEinstellung-ExtraBold.otf")
        format("opentype");

    font-weight: 800;

    font-style: normal;
}

@font-face {
    font-family: "Neue Einstellung";

    src: url("../../assets/fonts/NeueEinstellung-Black.otf") format("opentype");

    font-weight: 900;

    font-style: normal;
}

*,
::before,
::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
    box-sizing: border-box;
    /* 1 */
    border-width: 0;
    /* 2 */
    border-style: solid;
    /* 2 */
    border-color: #e5e7eb;
    /* 2 */
}

::before,
::after {
    --tw-content: "";
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
    line-height: 1.5;
    /* 1 */
    -webkit-text-size-adjust: 100%;
    /* 2 */
    -moz-tab-size: 4;
    /* 3 */
    -o-tab-size: 4;
    tab-size: 4;
    /* 3 */
    font-family: Inter, system-ui, sans-serif;
    /* 4 */
    font-feature-settings: normal;
    /* 5 */
    font-variation-settings: normal;
    /* 6 */
    -webkit-tap-highlight-color: transparent;
    /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
    margin: 0;
    /* 1 */
    line-height: inherit;
    /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
    height: 0;
    /* 1 */
    color: inherit;
    /* 2 */
    border-top-width: 1px;
    /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
    color: inherit;
    text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
    font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
        "Liberation Mono", "Courier New", monospace;
    /* 1 */
    font-feature-settings: normal;
    /* 2 */
    font-variation-settings: normal;
    /* 3 */
    font-size: 1em;
    /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
    font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25em;
}

sup {
    top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
    text-indent: 0;
    /* 1 */
    border-color: inherit;
    /* 2 */
    border-collapse: collapse;
    /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    /* 1 */
    font-feature-settings: inherit;
    /* 1 */
    font-variation-settings: inherit;
    /* 1 */
    font-size: 100%;
    /* 1 */
    font-weight: inherit;
    /* 1 */
    line-height: inherit;
    /* 1 */
    letter-spacing: inherit;
    /* 1 */
    color: inherit;
    /* 1 */
    margin: 0;
    /* 2 */
    padding: 0;
    /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
    text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type="button"]),
input:where([type="reset"]),
input:where([type="submit"]) {
    -webkit-appearance: button;
    /* 1 */
    background-color: transparent;
    /* 2 */
    background-image: none;
    /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
    outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
    box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
    vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    outline-offset: -2px;
    /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
    -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
    -webkit-appearance: button;
    /* 1 */
    font: inherit;
    /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
    display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0;
}

fieldset {
    margin: 0;
    padding: 0;
}

legend {
    padding: 0;
}

ol,
ul,
menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
    padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
    resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1;
    /* 1 */
    color: #9ca3af;
    /* 2 */
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    /* 1 */
    color: #9ca3af;
    /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
    cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
    cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    /* 1 */
    vertical-align: middle;
    /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
    max-width: 100%;
    height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
    display: none;
}

[type="text"],
input:where(:not([type])),
[type="email"],
[type="url"],
[type="password"],
[type="number"],
[type="date"],
[type="datetime-local"],
[type="month"],
[type="search"],
[type="tel"],
[type="time"],
[type="week"],
[multiple],
textarea,
select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
}

[type="text"]:focus,
input:where(:not([type])):focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
    border-color: #2563eb;
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    color: #6b7280;
    opacity: 1;
}

input::placeholder,
textarea::placeholder {
    color: #6b7280;
    opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
}

::-webkit-date-and-time-value {
    min-height: 1.5em;
    text-align: inherit;
}

::-webkit-datetime-edit {
    display: inline-flex;
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-year-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
}

select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

[multiple],
[size]:where(select:not([size="1"])) {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    -webkit-print-color-adjust: unset;
    print-color-adjust: unset;
}

[type="checkbox"],
[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    padding: 0;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: #2563eb;
    background-color: #fff;
    border-color: #6b7280;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
}

[type="checkbox"] {
    border-radius: 0px;
}

[type="radio"] {
    border-radius: 100%;
}

[type="checkbox"]:focus,
[type="radio"]:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: #2563eb;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow);
}

[type="checkbox"]:checked,
[type="radio"]:checked {
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

[type="checkbox"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

@media (forced-colors: active) {
    [type="checkbox"]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="radio"]:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

@media (forced-colors: active) {
    [type="radio"]:checked {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="checkbox"]:checked:hover,
[type="checkbox"]:checked:focus,
[type="radio"]:checked:hover,
[type="radio"]:checked:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="checkbox"]:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
    border-color: transparent;
    background-color: currentColor;
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}

@media (forced-colors: active) {
    [type="checkbox"]:indeterminate {
        -webkit-appearance: auto;
        -moz-appearance: auto;
        appearance: auto;
    }
}

[type="checkbox"]:indeterminate:hover,
[type="checkbox"]:indeterminate:focus {
    border-color: transparent;
    background-color: currentColor;
}

[type="file"] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
}

[type="file"]:focus {
    outline: 1px solid ButtonText;
    outline: 1px auto -webkit-focus-ring-color;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: Neue Einstellung, Inter, system-ui, sans-serif;
    --tw-text-opacity: 1;
    color: rgb(29 29 29 / var(--tw-text-opacity, 1));
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 600;
    line-height: 1.25;
}

a {
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.container {
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    padding-right: 1rem;
    padding-left: 1rem;
}

@media (min-width: 100%) {
    .container {
        max-width: 100%;
    }
}

@media (min-width: 1352px) {
    .container {
        max-width: 1352px;
    }
}

@media (min-width: 1442px) {
    .container {
        max-width: 1442px;
    }
}

/* Buttons */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    font-weight: 500;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.btn:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
    --tw-ring-offset-width: 2px;
}

/* Cards */

/* Navigation */

.nav-link {
    position: relative;
    font-weight: 500;
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.video-modal .nav-link {
    animation: videoSlideUp 0.3s ease-out;
}

@media (max-width: 768px) {
    .video-modal .nav-link {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

.nav-link:hover {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

#site-navigation .nav-link.active::after {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 0px;
    height: 0.125rem;
    width: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.nav-link:hover::after {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 0px;
    height: 0.125rem;
    width: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.nav-link.active {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

/* Forms */

/* Hero Section */

/* Sections */

.section {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

/* Grid Layouts */

/* Feature Cards */

/* Stats */

/* Testimonials */

/* Badges */

/* Loading States */

@keyframes pulse {
    50% {
        opacity: 0.5;
    }
}

.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Review System Styles */

.review-item {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

.star-btn svg {
    cursor: pointer;
    transition-property: color;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.2s;
    transition-timing-function: ease;
}

.star-btn:hover svg {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.filter-btn {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

.filter-btn:hover {
    --tw-translate-y: -1px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    --tw-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --tw-shadow-colored: 0 2px 4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.helpful-btn:hover {
    --tw-translate-y: -1px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }

    100% {
        opacity: 1;
    }
}

.review-modal {
    animation: fadeIn 0.3s ease;
    --tw-backdrop-blur: blur(4px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur)
        var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
        var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
        var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
        var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
        var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
        var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
        var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
        var(--tw-backdrop-sepia);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    0% {
        transform: translateY(20px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-content {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        --tw-translate-y: 30px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
        opacity: 0;
    }

    to {
        --tw-translate-y: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
        opacity: 1;
    }
}

.rating-bar {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

.rating-bar:hover {
    --tw-scale-x: 1.02;
    --tw-scale-y: 1.02;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.review-images img:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

/* Review form enhancements */

.review-form textarea:focus,
.review-form input:focus {
    --tw-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    --tw-shadow-colored: 0 0 0 3px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.submit-btn:hover {
    --tw-translate-y: -1px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    --tw-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    --tw-shadow-colored: 0 4px 8px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.product-thumbs-swiper .swiper-slide {
    opacity: 0.7;
    transition-property: opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.3s;
    transition-timing-function: ease;
}

.product-thumbs-swiper .swiper-slide:hover {
    opacity: 1;
}

.product-thumbs-swiper .swiper-slide-thumb-active {
    opacity: 1;
}

/* Custom Spacing */

.swiper-pagination {
    bottom: 1.75rem !important;
}

.swiper-pagination-bullet {
    height: 0.75rem;
    width: 0.75rem;
    --tw-bg-opacity: 1;
    background-color: rgb(231 231 234 / var(--tw-bg-opacity, 1));
    opacity: 1;
}

.swiper-pagination-bullet-active {
    height: 0.75rem;
    width: 0.75rem;
    --tw-bg-opacity: 1;
    background-color: rgb(36 109 218 / var(--tw-bg-opacity, 1));
    /* Kích thước và màu khi active */
}

.swiper-button-prev,
.swiper-button-next {
    display: flex;
    height: 2.75rem;
    width: 2.75rem;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    --tw-bg-opacity: 1;
    --tw-text-opacity: 1;
    color: rgb(31 41 55 / var(--tw-text-opacity, 1));
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}

.swiper-button-prev:focus,
.swiper-button-next:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.swiper-button-prev::after,
.swiper-button-next::after {
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-weight: 700;
    --tw-text-opacity: 1;
    color: rgb(4 56 157 / var(--tw-text-opacity, 1));
}

.swiper-button-prev {
    left: 0px;
}

.swiper-button-next {
    right: 0px;
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

.swiper-pagination.unset {
    top: unset !important;
    /* Adjust top position */
    position: unset !important;
}

.swiper-pagination-bullet {
    height: 10px;
    width: 10px;
    --tw-bg-opacity: 1;
    background-color: rgb(148 149 157 / var(--tw-bg-opacity, 1));
}

.swiper-pagination-bullet-active {
    height: 10px;
    width: 3rem;
    border-radius: 1rem;
    --tw-bg-opacity: 1;
    background-color: rgb(36 109 218 / var(--tw-bg-opacity, 1));
    /* Active bullet size and color */
}

.services-swiper .swiper-slide {
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    height: auto;
    /* Allow natural height */
    display: flex;
    /* Use flexbox for better control */
    align-items: stretch;
    /* Stretch to full height */
}

/* Staggered classes applied via JavaScript */

.services-swiper .swiper-slide.staggered-even {
    transform: translateY(90px);
}

.services-swiper .swiper-slide.staggered-odd {
    transform: translateY(0);
}

.services-swiper .swiper-button-next:after,
.services-swiper .swiper-button-prev:after {
    color: #ffffff;
}

/* Animation keyframes */

@keyframes slideUp {
    from {
        transform: translateY(90px);
        opacity: 0.8;
    }

    to {
        transform: translateY(-20px);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 1;
    }

    to {
        transform: translateY(90px);
        opacity: 0.8;
    }
}

.slide-up-animation {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.slide-down-animation {
    animation: slideDown 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

.pointer-events-none {
    pointer-events: none;
}

.visible {
    visibility: visible;
}

.static {
    position: static;
}

.fixed {
    position: fixed;
}

.absolute {
    position: absolute;
}

.relative {
    position: relative;
}

.sticky {
    position: sticky;
}

.inset-0 {
    inset: 0px;
}

.-top-12 {
    top: -3rem;
}

.bottom-0 {
    bottom: 0px;
}

.left-0 {
    left: 0px;
}

.left-1\/2 {
    left: 50%;
}

.left-4 {
    left: 1rem;
}

.right-0 {
    right: 0px;
}

.right-2 {
    right: 0.5rem;
}

.right-3 {
    right: 0.75rem;
}

.top-0 {
    top: 0px;
}

.top-1\/2 {
    top: 75%;
}

.top-4 {
    top: 1rem;
}

.top-\[2px\] {
    top: 2px;
}

.top-\[35\%\] {
    top: 35%;
}

.top-\[40\%\] {
    top: 40%;
}

.-left-6 {
    left: -1.5rem;
}

.-right-6 {
    right: -1.5rem;
}

.right-4 {
    right: 1rem;
}

.left-3 {
    left: 0.75rem;
}

.top-3 {
    top: 0.75rem;
}

.left-\[0\.42px\] {
    left: 0.42px;
}

.left-\[30px\] {
    left: 30px;
}

.left-\[37px\] {
    left: 37px;
}

.top-\[12\.83px\] {
    top: 12.83px;
}

.top-\[20\.42px\] {
    top: 20.42px;
}

.top-\[30px\] {
    top: 30px;
}

.top-\[4\.58px\] {
    top: 4.58px;
}

.top-\[5\.25px\] {
    top: 5.25px;
}

.top-\[75px\] {
    top: 75px;
}

.z-10 {
    z-index: 10;
}

.z-40 {
    z-index: 40;
}

.z-50 {
    z-index: 50;
}

.order-1 {
    order: 1;
}

.order-2 {
    order: 2;
}

.col-span-1 {
    grid-column: span 1 / span 1;
}

.col-span-2 {
    grid-column: span 2 / span 2;
}

.col-span-5 {
    grid-column: span 5 / span 5;
}

.m-4 {
    margin: 1rem;
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.-mb-\[30px\] {
    margin-bottom: -30px;
}

.-ml-2 {
    margin-left: -0.5rem;
}

.-ml-\[15px\] {
    margin-left: -15px;
}

.-mr-2 {
    margin-right: -0.5rem;
}

.-mr-\[15px\] {
    margin-right: -15px;
}

.-mt-20 {
    margin-top: -4rem;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.mb-10 {
    margin-bottom: 2.5rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-3 {
    margin-bottom: 0.75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-7 {
    margin-bottom: 1.75rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-\[17px\] {
    margin-bottom: 17px;
}

.mb-\[2px\] {
    margin-bottom: 2px;
}

.mb-\[30px\] {
    margin-bottom: 30px;
}

.mr-4 {
    margin-right: 1rem;
}

.mt-12 {
    margin-top: 3rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-20 {
    margin-top: 5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-5 {
    margin-top: 1.25rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.mt-\[30px\] {
    margin-top: 30px;
}

.ml-auto {
    margin-left: auto;
}

.ml-4 {
    margin-left: 1rem;
}

.mb-5 {
    margin-bottom: 1.25rem;
}

.ml-6 {
    margin-left: 1.5rem;
}

.line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.grid {
    display: grid;
}

.hidden {
    display: none;
}

.aspect-\[0\.637065637\] {
    aspect-ratio: 0.637065637;
}

.aspect-\[0\.680529303\] {
    aspect-ratio: 0.680529303;
}

.aspect-\[1\.33333333\] {
    aspect-ratio: 1.33333333;
}

.aspect-\[1\.37037037\] {
    aspect-ratio: 1.37037037;
}

.aspect-\[1\.39662447\] {
    aspect-ratio: 1.39662447;
}

.aspect-\[1\.767\] {
    aspect-ratio: 1.767;
}

.aspect-\[1\.77165354\] {
    aspect-ratio: 1.77165354;
}

.aspect-\[1\.77419355\] {
    aspect-ratio: 1.77419355;
}

.aspect-\[1\.94488189\] {
    aspect-ratio: 1.94488189;
}

.aspect-\[1\/0\.5\] {
    aspect-ratio: 1/0.5;
}

.aspect-\[2\.33331041\] {
    aspect-ratio: 2.33331041;
}

.aspect-\[1\.5\] {
    aspect-ratio: 1.5;
}

.aspect-\[1\.94581281\] {
    aspect-ratio: 1.94581281;
}

.aspect-\[2\.03603604\] {
    aspect-ratio: 2.03603604;
}

.aspect-\[3\.02325581\] {
    aspect-ratio: 3.02325581;
}

.size-6 {
    width: 1.5rem;
    height: 1.5rem;
}

.h-10 {
    height: 2.5rem;
}

.h-11 {
    height: 2.75rem;
}

.h-12 {
    height: 3rem;
}

.h-14 {
    height: 3.5rem;
}

.h-16 {
    height: 4rem;
}

.h-20 {
    height: 5rem;
}

.h-3 {
    height: 0.75rem;
}

.h-4 {
    height: 1rem;
}

.h-5 {
    height: 1.25rem;
}

.h-6 {
    height: 1.5rem;
}

.h-8 {
    height: 2rem;
}

.h-9 {
    height: 2.25rem;
}

.h-\[1px\] {
    height: 1px;
}

.h-\[2px\] {
    height: 2px;
}

.h-\[50px\] {
    height: 50px;
}

.h-auto {
    height: auto;
}

.h-full {
    height: 100%;
}

.h-0\.5 {
    height: 0.125rem;
}

.h-32 {
    height: 8rem;
}

.h-64 {
    height: 16rem;
}

.h-7 {
    height: 1.75rem;
}

.max-h-\[80vh\] {
    max-height: 80vh;
}

.min-h-32 {
    min-height: 8rem;
}

.min-h-screen {
    min-height: 100vh;
}

.w-10 {
    width: 2.5rem;
}

.w-11 {
    width: 2.75rem;
}

.w-12 {
    width: 3rem;
}

.w-14 {
    width: 3.5rem;
}

.w-16 {
    width: 4rem;
}

.w-20 {
    width: 5rem;
}

.w-28 {
    width: 7rem;
}

.w-3 {
    width: 0.75rem;
}

.w-32 {
    width: 8rem;
}

.w-4 {
    width: 1rem;
}

.w-5 {
    width: 1.25rem;
}

.w-6 {
    width: 1.5rem;
}

.w-60 {
    width: 15rem;
}

.w-8 {
    width: 2rem;
}

.w-80 {
    width: 20rem;
}

.w-\[1px\] {
    width: 1px;
}

.w-\[34px\] {
    width: 34px;
}

.w-\[50px\] {
    width: 50px;
}

.w-full {
    width: 100%;
}

.w-7 {
    width: 1.75rem;
}

.w-\[570px\] {
    width: 570px;
}

.w-\[584px\] {
    width: 584px;
}

.w-\[863px\] {
    width: 863px;
}

.w-\[930px\] {
    width: 930px;
}

.min-w-0 {
    min-width: 0px;
}

.min-w-\[120px\] {
    min-width: 120px;
}

.min-w-\[250px\] {
    min-width: 250px;
}

.min-w-\[200px\] {
    min-width: 200px;
}

.min-w-\[280px\] {
    min-width: 280px;
}

.max-w-2xl {
    max-width: 42rem;
}

.max-w-3xl {
    max-width: 48rem;
}

.max-w-4xl {
    max-width: 56rem;
}

.max-w-52 {
    max-width: 13rem;
}

.max-w-\[1170px\] {
    max-width: 1170px;
}

.max-w-\[286px\] {
    max-width: 286px;
}

.max-w-\[47\.1111111\%\] {
    max-width: 47.1111111%;
}

.max-w-\[690px\] {
    max-width: 690px;
}

.max-w-\[722px\] {
    max-width: 722px;
}

.max-w-\[880px\] {
    max-width: 880px;
}

.max-w-md {
    max-width: 28rem;
}

.max-w-\[130px\] {
    max-width: 130px;
}

.max-w-\[210px\] {
    max-width: 210px;
}

.max-w-\[42\.585\%\] {
    max-width: 42.585%;
}

.flex-1 {
    flex: 1 1 0%;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.origin-top-left {
    transform-origin: top left;
}

.-translate-x-1\/2 {
    --tw-translate-x: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
    --tw-translate-x: 100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
    --tw-rotate: -90deg;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.cursor-pointer {
    cursor: pointer;
}

.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}

.resize {
    resize: both;
}

.list-inside {
    list-style-position: inside;
}

.list-decimal {
    list-style-type: decimal;
}

.list-disc {
    list-style-type: disc;
}

.appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.flex-row {
    flex-direction: row;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-nowrap {
    flex-wrap: nowrap;
}

.items-start {
    align-items: flex-start;
}

.items-center {
    align-items: center;
}

.justify-start {
    justify-content: flex-start;
}

.justify-end {
    justify-content: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-1 {
    gap: 0.25rem;
}

.gap-10 {
    gap: 2.5rem;
}

.gap-2 {
    gap: 0.5rem;
}

.gap-3 {
    gap: 0.75rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-5 {
    gap: 1.25rem;
}

.gap-6 {
    gap: 1.5rem;
}

.gap-8 {
    gap: 2rem;
}

.gap-\[2px\] {
    gap: 2px;
}

.gap-\[30px\] {
    gap: 30px;
}

.gap-x-12 {
    -moz-column-gap: 3rem;
    column-gap: 3rem;
}

.gap-x-8 {
    -moz-column-gap: 2rem;
    column-gap: 2rem;
}

.gap-y-6 {
    row-gap: 1.5rem;
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}

.divide-y > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}

.divide-primary-border > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(231 231 234 / var(--tw-divide-opacity, 1));
}

.self-stretch {
    align-self: stretch;
}

.overflow-auto {
    overflow: auto;
}

.overflow-hidden {
    overflow: hidden;
}

.overflow-visible {
    overflow: visible;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.whitespace-normal {
    white-space: normal;
}

.whitespace-nowrap {
    white-space: nowrap;
}

.rounded {
    border-radius: 0.25rem;
}

.rounded-2xl {
    border-radius: 1rem;
}

.rounded-3xl {
    border-radius: 1.5rem;
}

.rounded-\[4px\] {
    border-radius: 4px;
}

.rounded-full {
    border-radius: 9999px;
}

.rounded-lg {
    border-radius: 0.5rem;
}

.rounded-md {
    border-radius: 0.375rem;
}

.rounded-none {
    border-radius: 0px;
}

.rounded-sm {
    border-radius: 0.125rem;
}

.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-\[20px\] {
    border-radius: 20px;
}

.rounded-l-md {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.rounded-r-md {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

.rounded-t-lg {
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.rounded-l-full {
    border-top-left-radius: 9999px;
    border-bottom-left-radius: 9999px;
}

.rounded-tl-lg {
    border-top-left-radius: 0.5rem;
}

.rounded-tr-lg {
    border-top-right-radius: 0.5rem;
}

.rounded-tr-full {
    border-top-right-radius: 9999px;
}

.rounded-bl-full {
    border-bottom-left-radius: 9999px;
}

.rounded-tl-full {
    border-top-left-radius: 9999px;
}

.border {
    border-width: 1px;
}

.border-0 {
    border-width: 0px;
}

.border-b {
    border-bottom-width: 1px;
}

.border-l {
    border-left-width: 1px;
}

.border-r {
    border-right-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
    --tw-border-opacity: 1;
    border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-primary-border {
    --tw-border-opacity: 1;
    border-color: rgb(231 231 234 / var(--tw-border-opacity, 1));
}

.border-primary-border2 {
    --tw-border-opacity: 1;
    border-color: rgb(188 185 192 / var(--tw-border-opacity, 1));
}

.border-primary-green {
    --tw-border-opacity: 1;
    border-color: rgb(35 180 6 / var(--tw-border-opacity, 1));
}

.border-primary-price {
    --tw-border-opacity: 1;
    border-color: rgb(222 56 59 / var(--tw-border-opacity, 1));
}

.border-secondary-border {
    --tw-border-opacity: 1;
    border-color: rgb(83 85 99 / var(--tw-border-opacity, 1));
}

.border-secondary-main {
    --tw-border-opacity: 1;
    border-color: rgb(36 109 218 / var(--tw-border-opacity, 1));
}

.border-transparent {
    border-color: transparent;
}

.border-white {
    --tw-border-opacity: 1;
    border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}

.border-white\/20 {
    border-color: rgb(255 255 255 / 0.2);
}

.bg-black {
    --tw-bg-opacity: 1;
    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

.bg-black\/50 {
    background-color: rgb(0 0 0 / 0.5);
}

.bg-blue-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}

.bg-footer-main {
    --tw-bg-opacity: 1;
    background-color: rgb(29 29 29 / var(--tw-bg-opacity, 1));
}

.bg-green-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}

.bg-primary-background1 {
    background-color: rgba(15, 115, 232, 0.1);
}

.bg-primary-background2 {
    background-color: rgba(190, 22, 33, 0.1);
}

.bg-primary-background3 {
    --tw-bg-opacity: 1;
    background-color: rgb(242 249 255 / var(--tw-bg-opacity, 1));
}

.bg-primary-badge1 {
    --tw-bg-opacity: 1;
    background-color: rgb(222 56 59 / var(--tw-bg-opacity, 1));
}

.bg-primary-badge2 {
    --tw-bg-opacity: 1;
    background-color: rgb(255 152 0 / var(--tw-bg-opacity, 1));
}

.bg-primary-badge3 {
    --tw-bg-opacity: 1;
    background-color: rgb(233 30 99 / var(--tw-bg-opacity, 1));
}

.bg-primary-base {
    --tw-bg-opacity: 1;
    background-color: rgb(29 29 29 / var(--tw-bg-opacity, 1));
}

.bg-primary-border {
    --tw-bg-opacity: 1;
    background-color: rgb(231 231 234 / var(--tw-bg-opacity, 1));
}

.bg-primary-grey {
    --tw-bg-opacity: 1;
    background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));
}

.bg-primary-main {
    --tw-bg-opacity: 1;
    background-color: rgb(4 56 157 / var(--tw-bg-opacity, 1));
}

.bg-purple-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}

.bg-red-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}

.bg-red-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.bg-secondary-main {
    --tw-bg-opacity: 1;
    background-color: rgb(36 109 218 / var(--tw-bg-opacity, 1));
}

.bg-transparent {
    background-color: transparent;
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/20 {
    background-color: rgb(255 255 255 / 0.2);
}

.bg-blue-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-200 {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-opacity-0 {
    --tw-bg-opacity: 0;
}

.bg-opacity-50 {
    --tw-bg-opacity: 0.5;
}

.bg-opacity-75 {
    --tw-bg-opacity: 0.75;
}

.bg-brand-gradient {
    background-image: linear-gradient(180deg, #04389d 0%, #17c1f5 100%);
}

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.bg-gradient2 {
    background-image: linear-gradient(90deg, #04389d 1.57%, #17c1f5 100%);
}

.bg-gradient3 {
    background-image: linear-gradient(
        0deg,
        rgba(18, 18, 18, 0.7),
        rgba(18, 18, 18, 0.7)
    );
}

.bg-gradient4 {
    background-image: linear-gradient(to right, #04389d 3.64%, #17c1f5 99.01%);
}

.bg-gradient6 {
    background-image: linear-gradient(180deg, #04389d 0%, #17c1f5 100%);
}

.bg-gradient7 {
    background-image: linear-gradient(
        180deg,
        rgba(26, 26, 26, 0) 0%,
        rgba(0, 0, 0, 0.7) 100%
    );
}

.bg-gradient8 {
    background-image: linear-gradient(180deg, #04389d 0%, #17c1f5 100%);
}

.bg-img1 {
    background-image: url("/public/images/bg-service.png");
}

.bg-img2 {
    background-image: url("/public/assets/images/bg-about-section-2.png");
}

.bg-img3 {
    background-image: url("/public/assets/images/bg-home-2.jpg");
}

.bg-gradienttext {
    background-image: linear-gradient(180deg, #04389d 0%, #17c1f5 100%);
}

.bg-gradientwhite {
    background-image: linear-gradient(
        90deg,
        #fff 0%,
        rgba(255, 255, 255, 0) 100%
    );
}

.from-blue-500 {
    --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
    --tw-gradient-from: #fff var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-blue-600 {
    --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-blue-600 {
    --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}

.to-cyan-400 {
    --tw-gradient-to: #22d3ee var(--tw-gradient-to-position);
}

.to-transparent {
    --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.bg-cover {
    background-size: cover;
}

.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

.bg-center {
    background-position: center;
}

.bg-no-repeat {
    background-repeat: no-repeat;
}

.object-contain {
    -o-object-fit: contain;
    object-fit: contain;
}

.object-cover {
    -o-object-fit: cover;
    object-fit: cover;
}

.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-\[15px\] {
    padding: 15px;
}

.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
}

.px-11 {
    padding-left: 2.75rem;
    padding-right: 2.75rem;
}

.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.px-\[30px\] {
    padding-left: 30px;
    padding-right: 30px;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

.py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
}

.py-\[9px\] {
    padding-top: 9px;
    padding-bottom: 9px;
}

.py-\[10px\] {
    padding-top: 10px;
    padding-bottom: 10px;
}

.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}

.pb-1 {
    padding-bottom: 0.25rem;
}

.pb-10 {
    padding-bottom: 2.5rem;
}

.pb-12 {
    padding-bottom: 3rem;
}

.pb-14 {
    padding-bottom: 3.5rem;
}

.pb-16 {
    padding-bottom: 4rem;
}

.pb-3 {
    padding-bottom: 0.75rem;
}

.pb-4 {
    padding-bottom: 1rem;
}

.pb-6 {
    padding-bottom: 1.5rem;
}

.pb-8 {
    padding-bottom: 2rem;
}

.pb-\[50px\] {
    padding-bottom: 50px;
}

.pl-4 {
    padding-left: 1rem;
}

.pl-5 {
    padding-left: 1.25rem;
}

.pl-\[30px\] {
    padding-left: 30px;
}

.pr-12 {
    padding-right: 3rem;
}

.pr-14 {
    padding-right: 3.5rem;
}

.pr-6 {
    padding-right: 1.5rem;
}

.pt-1 {
    padding-top: 0.25rem;
}

.pt-10 {
    padding-top: 2.5rem;
}

.pt-12 {
    padding-top: 3rem;
}

.pt-14 {
    padding-top: 3.5rem;
}

.pt-16 {
    padding-top: 4rem;
}

.pt-2 {
    padding-top: 0.5rem;
}

.pt-3 {
    padding-top: 0.75rem;
}

.pt-4 {
    padding-top: 1rem;
}

.pt-40 {
    padding-top: 10rem;
}

.pt-6 {
    padding-top: 1.5rem;
}

.pt-8 {
    padding-top: 2rem;
}

.pt-\[6px\] {
    padding-top: 6px;
}

.pr-4 {
    padding-right: 1rem;
}

.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-\[\'Neue_Einstellung\'\] {
    font-family: "Neue Einstellung";
}

.font-neue {
    font-family: Neue Einstellung, Inter, system-ui, sans-serif;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.text-2xl\/\[1\.4\] {
    font-size: 1.5rem;
    line-height: 1.4;
}

.text-\[38px\]\/\[1\.4\] {
    font-size: 38px;
    line-height: 1.4;
}

.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}

.text-base\/\[1\.4\] {
    font-size: 1rem;
    line-height: 1.4;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-lg\/\[1\.4\] {
    font-size: 1.125rem;
    line-height: 1.4;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.font-bold {
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

.font-normal {
    font-weight: 400;
}

.font-semibold {
    font-weight: 600;
}

.uppercase {
    text-transform: uppercase;
}

.italic {
    font-style: italic;
}

.leading-5 {
    line-height: 1.25rem;
}

.leading-\[1\.4\] {
    line-height: 1.4;
}

.leading-none {
    line-height: 1;
}

.leading-relaxed {
    line-height: 1.625;
}

.leading-\[1\.5\] {
    line-height: 1.5;
}

.leading-snug {
    line-height: 1.375;
}

.tracking-wide {
    letter-spacing: 0.025em;
}

.text-blue-500 {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.text-blue-600 {
    --tw-text-opacity: 1;
    color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}

.text-footer-secondary {
    --tw-text-opacity: 1;
    color: rgb(148 149 157 / var(--tw-text-opacity, 1));
}

.text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.text-green-600 {
    --tw-text-opacity: 1;
    color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}

.text-primary-base {
    --tw-text-opacity: 1;
    color: rgb(29 29 29 / var(--tw-text-opacity, 1));
}

.text-primary-gray {
    --tw-text-opacity: 1;
    color: rgb(148 149 157 / var(--tw-text-opacity, 1));
}

.text-primary-gray2 {
    --tw-text-opacity: 1;
    color: rgb(83 85 99 / var(--tw-text-opacity, 1));
}

.text-primary-green {
    --tw-text-opacity: 1;
    color: rgb(35 180 6 / var(--tw-text-opacity, 1));
}

.text-primary-grey2 {
    --tw-text-opacity: 1;
    color: rgb(215 215 215 / var(--tw-text-opacity, 1));
}

.text-primary-price {
    --tw-text-opacity: 1;
    color: rgb(222 56 59 / var(--tw-text-opacity, 1));
}

.text-primary-white2 {
    --tw-text-opacity: 1;
    color: rgb(254 253 253 / var(--tw-text-opacity, 1));
}

.text-purple-600 {
    --tw-text-opacity: 1;
    color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}

.text-red-600 {
    --tw-text-opacity: 1;
    color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}

.text-secondary-main {
    --tw-text-opacity: 1;
    color: rgb(36 109 218 / var(--tw-text-opacity, 1));
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.text-white\/90 {
    color: rgb(255 255 255 / 0.9);
}

.text-yellow-400 {
    --tw-text-opacity: 1;
    color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.text-\[\#2B2928\] {
    --tw-text-opacity: 1;
    color: rgb(43 41 40 / var(--tw-text-opacity, 1));
}

.text-transparent {
    color: transparent;
}

.text-black\/20 {
    color: rgb(0 0 0 / 0.2);
}

.line-through {
    text-decoration-line: line-through;
}

.placeholder-primary-gray::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(148 149 157 / var(--tw-placeholder-opacity, 1));
}

.placeholder-primary-gray::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(148 149 157 / var(--tw-placeholder-opacity, 1));
}

.placeholder-primary-gray3::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(102 102 102 / var(--tw-placeholder-opacity, 1));
}

.placeholder-primary-gray3::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(102 102 102 / var(--tw-placeholder-opacity, 1));
}

.placeholder-white::-moz-placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(255 255 255 / var(--tw-placeholder-opacity, 1));
}

.placeholder-white::placeholder {
    --tw-placeholder-opacity: 1;
    color: rgb(255 255 255 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
    opacity: 0;
}

.opacity-90 {
    opacity: 0.9;
}

.opacity-100 {
    opacity: 1;
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),
        0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-\[0px_16px_32px_0px_rgba\(104\2c 121\2c 131\2c 0\.16\)\] {
    --tw-shadow: 0px 16px 32px 0px rgba(104, 121, 131, 0.16);
    --tw-shadow-colored: 0px 16px 32px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-box {
    --tw-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.08);
    --tw-shadow-colored: 0px 6px 10px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-checkbox {
    --tw-shadow: 0 0 0 3px #f2f9ff inset;
    --tw-shadow-colored: inset 0 0 0 3px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
        0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
        0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-new-shadow {
    --tw-shadow: 0px 16px 32px 0px rgba(104, 121, 131, 0.16);
    --tw-shadow-colored: 0px 16px 32px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-post {
    --tw-shadow: 0px 6px 16px 0px rgba(104, 121, 131, 0.16);
    --tw-shadow-colored: 0px 6px 16px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-product {
    --tw-shadow: 0px 5px 23px 0px rgba(0, 0, 0, 0.08);
    --tw-shadow-colored: 0px 5px 23px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.ring-1 {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.ring-inset {
    --tw-ring-inset: inset;
}

.ring-primary-gray2 {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(83 85 99 / var(--tw-ring-opacity, 1));
}

.blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
        var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert)
        var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-filter {
    -webkit-backdrop-filter: var(--tw-backdrop-blur)
        var(--tw-backdrop-brightness) var(--tw-backdrop-contrast)
        var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate)
        var(--tw-backdrop-invert) var(--tw-backdrop-opacity)
        var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
        var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
        var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
        var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
        var(--tw-backdrop-sepia);
}

.transition {
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter;
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke, opacity, box-shadow, transform,
        filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-colors {
    transition-property: color, background-color, border-color,
        text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-transform {
    transition-property: transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.duration-500 {
    transition-duration: 500ms;
}

.ease-in-out {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Aspect Ratios */

.aspect-video {
    aspect-ratio: 16 / 9;
}

.aspect-square {
    aspect-ratio: 1 / 1;
}

/* Custom Base Styles */

/* Custom Components */

/* Custom Utilities */

/* Mobile Filter Sidebar Styles */

@media (max-width: 767px) {
    .filter-sidebar {
        width: 320px !important;
        max-width: 85vw !important;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
    }

    .mobile-filter-overlay {
        -webkit-backdrop-filter: blur(2px);
        backdrop-filter: blur(2px);
    }
}

/* Video Modal Styles */

.video-modal {
    animation: fadeIn 0.3s ease-in-out;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

.video-modal-open {
    animation: modalSlideIn 0.4s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.video-modal .relative {
    animation: videoSlideUp 0.3s ease-out;
}

@keyframes videoSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Video modal responsiveness */

@media (max-width: 768px) {
    .video-modal .relative {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

.before\:absolute::before {
    content: var(--tw-content);
    position: absolute;
}

.before\:left-0::before {
    content: var(--tw-content);
    left: 0px;
}

.before\:top-1\/2::before {
    content: var(--tw-content);
    top: 50%;
}

.before\:h-\[6px\]::before {
    content: var(--tw-content);
    height: 6px;
}

.before\:w-\[6px\]::before {
    content: var(--tw-content);
    width: 6px;
}

.before\:-translate-y-1\/2::before {
    content: var(--tw-content);
    --tw-translate-y: -50%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:rounded-full::before {
    content: var(--tw-content);
    border-radius: 9999px;
}

.before\:bg-primary-gray2::before {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(83 85 99 / var(--tw-bg-opacity, 1));
}

.before\:content-\[\'\'\]::before {
    --tw-content: "";
    content: var(--tw-content);
}

.before\:content-\[counter\(item\)\'\.\'\]::before {
    --tw-content: counter(item) ".";
    content: var(--tw-content);
}

.before\:content-\[counter\(item\)\'\.\'counter\(subitem\)\'\.\'\]::before {
    --tw-content: counter(item) "." counter(subitem) ".";
    content: var(--tw-content);
}

.after\:absolute::after {
    content: var(--tw-content);
    position: absolute;
}

.after\:bottom-0::after {
    content: var(--tw-content);
    bottom: 0px;
}

.after\:left-0::after {
    content: var(--tw-content);
    left: 0px;
}

.after\:h-\[2px\]::after {
    content: var(--tw-content);
    height: 2px;
}

.after\:w-\[50px\]::after {
    content: var(--tw-content);
    width: 50px;
}

.after\:bg-primary-white2::after {
    content: var(--tw-content);
    --tw-bg-opacity: 1;
    background-color: rgb(254 253 253 / var(--tw-bg-opacity, 1));
}

.after\:content-\[\'\'\]::after {
    --tw-content: "";
    content: var(--tw-content);
}

.last\:border-b-0:last-child {
    border-bottom-width: 0px;
}

.hover\:-translate-y-1:hover {
    --tw-translate-y: -0.25rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-secondary-main:hover {
    --tw-border-opacity: 1;
    border-color: rgb(36 109 218 / var(--tw-border-opacity, 1));
}

.hover\:bg-blue-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-200:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.hover\:bg-red-50:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary-main:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(36 109 218 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
    background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-gradient2:hover {
    background-image: linear-gradient(90deg, #04389d 1.57%, #17c1f5 100%);
}

.hover\:bg-gradient5:hover {
    background-image: linear-gradient(to right, #ffffff 3.64%, #ffffff 99.01%);
}

.hover\:bg-gradient8:hover {
    background-image: linear-gradient(180deg, #04389d 0%, #17c1f5 100%);
}

.hover\:from-blue-600:hover {
    --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
    --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-blue-700:hover {
    --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}

.hover\:text-gray-300:hover {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-base:hover {
    --tw-text-opacity: 1;
    color: rgb(29 29 29 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-main:hover {
    --tw-text-opacity: 1;
    color: rgb(4 56 157 / var(--tw-text-opacity, 1));
}

.hover\:text-red-500:hover {
    --tw-text-opacity: 1;
    color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-secondary-main:hover {
    --tw-text-opacity: 1;
    color: rgb(36 109 218 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:text-yellow-400:hover {
    --tw-text-opacity: 1;
    color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
    text-decoration-line: underline;
}

.hover\:opacity-80:hover {
    opacity: 0.8;
}

.hover\:shadow-lg:hover {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
        0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
        0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-post:hover {
    --tw-shadow: 0px 6px 16px 0px rgba(104, 121, 131, 0.16);
    --tw-shadow-colored: 0px 6px 16px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-product:hover {
    --tw-shadow: 0px 5px 23px 0px rgba(0, 0, 0, 0.08);
    --tw-shadow-colored: 0px 5px 23px 0px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
    --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
        0 8px 10px -6px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),
        0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
        var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:right-0:focus {
    right: 0px;
}

.focus\:z-20:focus {
    z-index: 20;
}

.focus\:border-none:focus {
    border-style: none;
}

.focus\:border-secondary-main:focus {
    --tw-border-opacity: 1;
    border-color: rgb(36 109 218 / var(--tw-border-opacity, 1));
}

.focus\:border-transparent:focus {
    border-color: transparent;
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
}

.focus\:outline-offset-0:focus {
    outline-offset: 0px;
}

.focus\:ring-0:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-1:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
        var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
        var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-blue-400:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}

.focus\:ring-blue-500:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-base:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(29 29 29 / var(--tw-ring-opacity, 1));
}

.focus\:ring-primary-main:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(4 56 157 / var(--tw-ring-opacity, 1));
}

.focus-visible\:outline-2:focus-visible {
    outline-width: 2px;
}

.focus-visible\:outline-offset-2:focus-visible {
    outline-offset: 2px;
}

.active\:translate-y-0\.5:active {
    --tw-translate-y: 0.125rem;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:z-0 {
    z-index: 0;
}

.group:hover .group-hover\:z-10 {
    z-index: 10;
}

.group:hover .group-hover\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
    --tw-scale-x: 1.05;
    --tw-scale-y: 1.05;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
    --tw-scale-x: 1.1;
    --tw-scale-y: 1.1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y))
        rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
        scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-opacity-20 {
    --tw-bg-opacity: 0.2;
}

.group:hover .group-hover\:opacity-100 {
    opacity: 1;
}

.peer:checked ~ .peer-checked\:block {
    display: block;
}

.peer:checked ~ .peer-checked\:border-secondary-main {
    --tw-border-opacity: 1;
    border-color: rgb(36 109 218 / var(--tw-border-opacity, 1));
}

.peer:checked ~ .peer-checked\:bg-secondary-main {
    --tw-bg-opacity: 1;
    background-color: rgb(36 109 218 / var(--tw-bg-opacity, 1));
}

.peer:checked ~ .peer-checked\:text-secondary-main {
    --tw-text-opacity: 1;
    color: rgb(36 109 218 / var(--tw-text-opacity, 1));
}

@media (min-width: 414px) {
    .xs\:grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    .xs\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 550px) {
    .sm\:col-span-1 {
        grid-column: span 1 / span 1;
    }

    .sm\:aspect-2 {
        aspect-ratio: 2;
    }

    .sm\:h-12 {
        height: 3rem;
    }

    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .sm\:flex-row {
        flex-direction: row;
    }

    .sm\:gap-3 {
        gap: 0.75rem;
    }

    .sm\:pb-\[90px\] {
        padding-bottom: 90px;
    }

    .sm\:pt-20 {
        padding-top: 5rem;
    }

    .sm\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }

    .sm\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
}

@media (min-width: 768px) {
    .md\:col-span-1 {
        grid-column: span 1 / span 1;
    }

    .md\:col-span-2 {
        grid-column: span 2 / span 2;
    }

    .md\:mb-16 {
        margin-bottom: 4rem;
    }

    .md\:mb-20 {
        margin-bottom: 5rem;
    }

    .md\:mb-\[70px\] {
        margin-bottom: 70px;
    }

    .md\:mt-16 {
        margin-top: 4rem;
    }

    .md\:block {
        display: block;
    }

    .md\:flex {
        display: flex;
    }

    .md\:inline-flex {
        display: inline-flex;
    }

    .md\:grid {
        display: grid;
    }

    .md\:hidden {
        display: none;
    }

    .md\:aspect-\[1\/0\.2375\] {
        aspect-ratio: 1/0.2375;
    }

    .md\:aspect-\[2\.92682927\] {
        aspect-ratio: 2.92682927;
    }

    .md\:h-\[70px\] {
        height: 70px;
    }

    .md\:w-\[30px\] {
        width: 30px;
    }

    .md\:w-\[50\%\] {
        width: 50%;
    }

    .md\:max-w-\[42\.585\%\] {
        max-width: 42.585%;
    }

    .md\:max-w-\[836px\] {
        max-width: 836px;
    }

    .md\:grid-cols-12 {
        grid-template-columns: repeat(12, minmax(0, 1fr));
    }

    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\:flex-row {
        flex-direction: row;
    }

    .md\:items-center {
        align-items: center;
    }

    .md\:gap-0 {
        gap: 0px;
    }

    .md\:gap-12 {
        gap: 3rem;
    }

    .md\:gap-4 {
        gap: 1rem;
    }

    .md\:p-6 {
        padding: 1.5rem;
    }

    .md\:py-12 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .md\:pb-0 {
        padding-bottom: 0px;
    }

    .md\:pb-24 {
        padding-bottom: 6rem;
    }

    .md\:pt-32 {
        padding-top: 8rem;
    }

    .md\:pt-8 {
        padding-top: 2rem;
    }

    .md\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .md\:text-3xl {
        font-size: 1.875rem;
        line-height: 2.25rem;
    }

    .md\:text-\[28px\] {
        font-size: 28px;
    }

    .md\:text-\[28px\]\/\[1\.4\] {
        font-size: 28px;
        line-height: 1.4;
    }

    .md\:text-\[38px\]\/\[1\.4\] {
        font-size: 38px;
        line-height: 1.4;
    }

    .md\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem;
    }

    .md\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
}

@media (min-width: 1024px) {
    .lg\:relative {
        position: relative;
    }

    .lg\:z-auto {
        z-index: auto;
    }

    .lg\:-order-1 {
        order: -1;
    }

    .lg\:order-1 {
        order: 1;
    }

    .lg\:order-2 {
        order: 2;
    }

    .lg\:col-span-1 {
        grid-column: span 1 / span 1;
    }

    .lg\:col-span-3 {
        grid-column: span 3 / span 3;
    }

    .lg\:-ml-\[15px\] {
        margin-left: -15px;
    }

    .lg\:-mr-\[15px\] {
        margin-right: -15px;
    }

    .lg\:mb-8 {
        margin-bottom: 2rem;
    }

    .lg\:mb-\[40px\] {
        margin-bottom: 40px;
    }

    .lg\:mb-\[54px\] {
        margin-bottom: 54px;
    }

    .lg\:mb-\[70px\] {
        margin-bottom: 70px;
    }

    .lg\:mt-\[40px\] {
        margin-top: 40px;
    }

    .lg\:mt-\[54px\] {
        margin-top: 54px;
    }

    .lg\:mt-\[70px\] {
        margin-top: 70px;
    }

    .lg\:mb-10 {
        margin-bottom: 2.5rem;
    }

    .lg\:mt-8 {
        margin-top: 2rem;
    }

    .lg\:block {
        display: block;
    }

    .lg\:flex {
        display: flex;
    }

    .lg\:hidden {
        display: none;
    }

    .lg\:h-\[104px\] {
        height: 104px;
    }

    .lg\:h-\[52px\] {
        height: 52px;
    }

    .lg\:h-auto {
        height: auto;
    }

    .lg\:w-\[52px\] {
        width: 52px;
    }

    .lg\:w-full {
        width: 100%;
    }

    .lg\:max-w-\[21\.2765957\%\] {
        max-width: 21.2765957%;
    }

    .lg\:max-w-\[22\.6950355\%\] {
        max-width: 22.6950355%;
    }

    .lg\:max-w-\[25\.5319149\%\] {
        max-width: 25.5319149%;
    }

    .lg\:max-w-\[36\.71875\%\] {
        max-width: 36.71875%;
    }

    .lg\:max-w-\[448px\] {
        max-width: 448px;
    }

    .lg\:translate-x-0 {
        --tw-translate-x: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y))
            rotate(var(--tw-rotate)) skewX(var(--tw-skew-x))
            skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x))
            scaleY(var(--tw-scale-y));
    }

    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .lg\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .lg\:flex-row {
        flex-direction: row;
    }

    .lg\:gap-16 {
        gap: 4rem;
    }

    .lg\:gap-4 {
        gap: 1rem;
    }

    .lg\:gap-5 {
        gap: 1.25rem;
    }

    .lg\:gap-\[30px\] {
        gap: 30px;
    }

    .lg\:p-0 {
        padding: 0px;
    }

    .lg\:p-6 {
        padding: 1.5rem;
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .lg\:px-\[15px\] {
        padding-left: 15px;
        padding-right: 15px;
    }

    .lg\:py-16 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .lg\:pb-40 {
        padding-bottom: 10rem;
    }

    .lg\:pb-6 {
        padding-bottom: 1.5rem;
    }

    .lg\:pb-\[30px\] {
        padding-bottom: 30px;
    }

    .lg\:pb-\[70px\] {
        padding-bottom: 70px;
    }

    .lg\:pb-\[86px\] {
        padding-bottom: 86px;
    }

    .lg\:pt-12 {
        padding-top: 3rem;
    }

    .lg\:pt-20 {
        padding-top: 5rem;
    }

    .lg\:pt-40 {
        padding-top: 10rem;
    }

    .lg\:pt-6 {
        padding-top: 1.5rem;
    }

    .lg\:pt-\[15px\] {
        padding-top: 15px;
    }

    .lg\:pt-\[70px\] {
        padding-top: 70px;
    }

    .lg\:pt-\[86px\] {
        padding-top: 86px;
    }

    .lg\:pr-0 {
        padding-right: 0px;
    }

    .lg\:pt-0 {
        padding-top: 0px;
    }

    .lg\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }

    .lg\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }

    .lg\:text-\[28px\]\/\[1\.4\] {
        font-size: 28px;
        line-height: 1.4;
    }

    .lg\:text-\[38px\]\/\[1\.4\] {
        font-size: 38px;
        line-height: 1.4;
    }

    .lg\:text-lg {
        font-size: 1.125rem;
        line-height: 1.75rem;
    }

    .lg\:text-xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
}

@media (min-width: 1280px) {
    .xl\:-left-3 {
        left: -0.75rem;
    }

    .xl\:-left-4 {
        left: -1rem;
    }

    .xl\:-left-8 {
        left: -2rem;
    }

    .xl\:-right-2 {
        right: -0.5rem;
    }

    .xl\:-right-3 {
        right: -0.75rem;
    }

    .xl\:-right-4 {
        right: -1rem;
    }

    .xl\:-right-8 {
        right: -2rem;
    }

    .xl\:left-2 {
        left: 0.5rem;
    }

    .xl\:h-10 {
        height: 2.5rem;
    }

    .xl\:w-10 {
        width: 2.5rem;
    }

    .xl\:w-20 {
        width: 5rem;
    }

    .xl\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .xl\:gap-\[30px\] {
        gap: 30px;
    }

    .xl\:pb-64 {
        padding-bottom: 16rem;
    }

    .xl\:pb-\[130px\] {
        padding-bottom: 130px;
    }

    .xl\:pb-\[93px\] {
        padding-bottom: 93px;
    }

    .xl\:pt-\[200px\] {
        padding-top: 200px;
    }

    .xl\:pt-\[93px\] {
        padding-top: 93px;
    }

    .xl\:text-\[46px\]\/\[1\.4\] {
        font-size: 46px;
        line-height: 1.4;
    }

    .xl\:text-sm {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }

    .xl\:text-\[38px\]\/\[1\.4\] {
        font-size: 38px;
        line-height: 1.4;
    }
    /* Đảm bảo tất cả swiper slides có chiều cao bằng nhau */
    .related-slider .swiper-wrapper {
        align-items: stretch !important;
    }

    .related-slider .swiper-slide {
        height: auto !important;
    }

    .related-slider .swiper-slide > div {
        height: 100%;
    }
    /* NEWS DETAIL */
    /* Cố định chiều cao tối thiểu cho các box */
    .related-slider .swiper-slide .bg-white {
        min-height: 400px;
    }

    /* Đảm bảo flexbox hoạt động đúng */
    .related-slider .swiper-slide .flex-col {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .related-slider .swiper-slide .flex-grow {
        flex: 1;
    }
}
