<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if (! $request->expectsJson()) {
            // Nếu đường dẫn bắt đầu với 'admin', chuyển hướng đến trang đăng nhập admin
            if (str_starts_with($request->path(), 'admin')) {
                return route('filament.admin.auth.login');
            } 
            
            // <PERSON><PERSON><PERSON><PERSON> l<PERSON>, chuyển hướng đến trang đăng nhập frontend
            return route('login');
        }
        
        return null;
    }
}
