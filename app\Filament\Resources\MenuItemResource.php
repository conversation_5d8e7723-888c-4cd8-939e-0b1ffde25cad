<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MenuItemResource\Pages;
use App\Models\Category;
use App\Models\Menu;
use App\Models\MenuItem;
use App\Models\Post;
use App\Models\StaticPage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\TextInput;

class MenuItemResource extends Resource
{
    protected static ?string $model = MenuItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';

    protected static ?string $navigationGroup = 'Quản lý nội dung';

    protected static ?int $navigationSort = 21;

    protected static bool $shouldRegisterNavigation = false;

    public static function canAccess(): bool
    {
        return true; // Tạm thời cho phép access tới mọi người
    }

    public static function getModelLabel(): string
    {
        return 'Mục Menu';
    }

    public static function getPluralModelLabel(): string
    {
        return 'Mục Menu';
    }

    public static function form(Form $form): Form
    {
        $menuId = request()->route('menu');
        
        return $form
            ->schema([
                Forms\Components\Hidden::make('menu_id')
                    ->default($menuId)
                    ->required(),

                Forms\Components\Hidden::make('parent_id'),

                Forms\Components\Hidden::make('model_type'),

                Forms\Components\TextInput::make('title')
                    ->label('Tiêu đề')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('type')
                    ->label('Loại liên kết')
                    ->options([
                        'custom' => 'URL tùy chỉnh',
                        'page' => 'Trang tĩnh',
                        'post' => 'Bài viết',
                        'category' => 'Danh mục',
                    ])
                    ->default('custom')
                    ->live()
                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                        // Reset model_type và model_id khi thay đổi loại liên kết
                        if ($state === 'custom') {
                            $set('model_type', null);
                            $set('model_id', null);
                        } elseif ($state === 'page') {
                            $set('model_type', StaticPage::class);
                        } elseif ($state === 'post') {
                            $set('model_type', Post::class);
                        } elseif ($state === 'category') {
                            $set('model_type', Category::class);
                        }
                    })
                    ->required(),

                Forms\Components\Tabs::make('Link')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('custom')
                            ->label('URL tùy chỉnh')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'custom')
                            ->schema([
                                Forms\Components\TextInput::make('url')
                                    ->label('URL')
                                    ->required()
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Tabs\Tab::make('page')
                            ->label('Trang tĩnh')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'page')
                            ->schema([
                                Forms\Components\Select::make('model_id')
                                    ->label('Chọn trang')
                                    ->options(function () {
                                        // Lấy trực tiếp từ database để đảm bảo dữ liệu mới nhất
                                        $pages = DB::table('static_pages')
                                            ->where('status', 'published')
                                            ->orderBy('title')
                                            ->select('id', 'title')
                                            ->get();
                                        
                                        $options = [];
                                        foreach ($pages as $page) {
                                            $options[$page->id] = $page->title;
                                        }
                                        
                                        return $options;
                                    })
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                            ]),

                        Forms\Components\Tabs\Tab::make('post')
                            ->label('Bài viết')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'post')
                            ->schema([
                                Forms\Components\Select::make('model_id')
                                    ->label('Chọn bài viết')
                                    ->options(function () {
                                        return Post::where('status', 'published')
                                            ->orderBy('title')
                                            ->pluck('title', 'id');
                                    })
                                    ->searchable()
                                    ->required()
                                    ->preload(),
                            ]),

                        Forms\Components\Tabs\Tab::make('category')
                            ->label('Danh mục')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'category')
                            ->schema([
                                Forms\Components\Select::make('model_id')
                                    ->label('Chọn danh mục')
                                    ->options(function () {
                                        return Category::where('status', true)
                                            ->orderBy('name')
                                            ->pluck('name', 'id');
                                    })
                                    ->searchable()
                                    ->required()
                                    ->preload(),
                            ]),
                    ]),

                Forms\Components\Select::make('target')
                    ->label('Mở liên kết trong')
                    ->options([
                        '_self' => 'Cửa sổ hiện tại',
                        '_blank' => 'Cửa sổ mới',
                    ])
                    ->default('_self')
                    ->required(),

                TextInput::make('icon')
                    ->label('Icon (sử dụng FontAwesome v6)')
                    ->placeholder('Ví dụ: fa-solid fa-home')
                    ->helperText('Nhập class của FontAwesome icon, ví dụ: fa-solid fa-home')
                    ->columnSpan(1),

                Forms\Components\TextInput::make('class')
                    ->label('CSS Class')
                    ->helperText('Nhập class CSS tùy chỉnh')
                    ->maxLength(255),

                Forms\Components\Toggle::make('status')
                    ->label('Hiển thị')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('order')
            ->defaultSort('order')
            ->reorderRecordsTriggerAction(
                fn (Tables\Actions\Action $action) => $action
                    ->button()
                    ->label('Sắp xếp menu')
                    ->icon('heroicon-o-arrows-up-down')
            )
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Tiêu đề')
                    ->sortable()
                    ->searchable()
                    ->extraAttributes(fn () => [
                        'class' => 'filament-tables-text-column cursor-move',
                    ]),

                Tables\Columns\TextColumn::make('url')
                    ->label('URL')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Loại')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'custom' => 'URL tùy chỉnh',
                        'page' => 'Trang tĩnh',
                        'post' => 'Bài viết',
                        'category' => 'Danh mục',
                        default => $state,
                    })
                    ->colors([
                        'primary' => 'custom',
                        'success' => 'page',
                        'warning' => 'post',
                        'danger' => 'category',
                    ])
                    ->toggleable(),

                Tables\Columns\IconColumn::make('status')
                    ->label('Hiển thị')
                    ->boolean()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Loại liên kết')
                    ->options([
                        'custom' => 'URL tùy chỉnh',
                        'page' => 'Trang tĩnh',
                        'post' => 'Bài viết',
                        'category' => 'Danh mục',
                    ]),
                
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        '1' => 'Hiển thị',
                        '0' => 'Ẩn',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->modalHeading('Chỉnh sửa mục menu')
                    ->modalWidth('md'),
                
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->modalHeading('Tạo mục menu mới')
                    ->modalWidth('md'),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageMenuItems::route('/{menu}'),
            'edit' => Pages\EditMenuItem::route('/{menu}/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $menuId = request()->route('menu');
        
        return parent::getEloquentQuery()
            ->where('menu_id', $menuId);
    }

    public static function getUrl(string $name = 'index', array $parameters = [], bool $isAbsolute = true, string|null $panel = null, \Illuminate\Database\Eloquent\Model|null $tenant = null): string
    {
        if ($name === 'index' && !isset($parameters['menu'])) {
            $parameters['menu'] = request()->route('menu');
        }
        
        return parent::getUrl($name, $parameters, $isAbsolute, $panel, $tenant);
    }
}
